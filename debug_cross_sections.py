#!/usr/bin/env python3
"""
Debug script to understand the cross-section data extraction
"""

import numpy as np

def debug_cross_sections():
    """Debug the cross-section data extraction logic"""
    
    # Create a test 3D array with shape (energy, kx, ky)
    n_energy, n_kx, n_ky = 5, 4, 3
    
    # Create test data where we can easily identify which dimension is which
    test_data = np.zeros((n_energy, n_kx, n_ky))
    
    # Fill with identifiable patterns
    for e in range(n_energy):
        for kx in range(n_kx):
            for ky in range(n_ky):
                # Value = energy*100 + kx*10 + ky
                test_data[e, kx, ky] = e*100 + kx*10 + ky
    
    print("Test data shape (energy, kx, ky):", test_data.shape)
    print("Test data pattern: value = energy*100 + kx*10 + ky")
    print()
    
    print("Full data array:")
    for e in range(n_energy):
        print(f"Energy slice {e}:")
        print(test_data[e, :, :])
        print()
    
    # Test kx slices (should give Energy vs ky at fixed kx)
    kx_slice_idx = 2  # Fix kx=2
    print(f"=== kx slice at kx_index={kx_slice_idx} ===")
    print("Should show: Energy vs ky at fixed kx=2")
    print("Expected pattern: values should be energy*100 + 20 + ky")
    
    kx_slice = test_data[:, kx_slice_idx, :]  # Shape: (energy, ky)
    print(f"Extracted data shape: {kx_slice.shape}")
    print("Extracted data:")
    print(kx_slice)
    print("Analysis:")
    for e in range(n_energy):
        for ky in range(n_ky):
            expected = e*100 + 20 + ky
            actual = kx_slice[e, ky]
            print(f"  [E={e}, ky={ky}]: expected={expected}, actual={actual}, match={expected==actual}")
    print()
    
    # Test ky slices (should give Energy vs kx at fixed ky)
    ky_slice_idx = 1  # Fix ky=1
    print(f"=== ky slice at ky_index={ky_slice_idx} ===")
    print("Should show: Energy vs kx at fixed ky=1")
    print("Expected pattern: values should be energy*100 + kx*10 + 1")
    
    ky_slice = test_data[:, :, ky_slice_idx]  # Shape: (energy, kx)
    print(f"Extracted data shape: {ky_slice.shape}")
    print("Extracted data:")
    print(ky_slice)
    print("Analysis:")
    for e in range(n_energy):
        for kx in range(n_kx):
            expected = e*100 + kx*10 + 1
            actual = ky_slice[e, kx]
            print(f"  [E={e}, kx={kx}]: expected={expected}, actual={actual}, match={expected==actual}")
    print()
    
    print("=== SUMMARY ===")
    print("For data shape (energy, kx, ky):")
    print("- kx slices: data[:, kx_index, :] → (energy, ky) → Energy vs ky at fixed kx ✓")
    print("- ky slices: data[:, :, ky_index] → (energy, kx) → Energy vs kx at fixed ky ✓")
    print()
    print("If the GUI is showing the wrong data, the issue might be:")
    print("1. Data structure is different than (energy, kx, ky)")
    print("2. Slice indices are being swapped somewhere")
    print("3. Grid coordinates are mismatched")

if __name__ == "__main__":
    debug_cross_sections()
