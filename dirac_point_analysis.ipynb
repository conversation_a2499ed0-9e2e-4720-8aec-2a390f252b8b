{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Dirac Point Energy Level vs Cr Substitution Ratio Analysis\n", "\n", "This notebook analyzes the relationship between Dirac Point Energy Level (E_D) and Cr Substitution Ratio (x) using power law fitting with professional ARPES-style plotting."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import plotly.graph_objects as go\n", "from scipy.optimize import curve_fit\n", "from sklearn.metrics import r2_score\n", "import pandas as pd"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Professional Plotting Style Functions\n", "\n", "These functions replicate the professional styling used in the ARPES analysis GUI."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def create_latex_labels():\n", "    \"\"\"Create LaTeX-style labels for plots\"\"\"\n", "    return {\n", "        'x_cr': 'x (Cr Substitution Ratio)',\n", "        'E_D': 'E<sub>D</sub> (eV)',\n", "        'dirac_energy': 'Dirac Point Energy Level (eV)',\n", "        'substitution_ratio': 'Cr Substitution Ratio'\n", "    }\n", "\n", "def create_professional_layout():\n", "    \"\"\"Create professional plot layout settings\"\"\"\n", "    return {\n", "        'font': {\n", "            'family': 'Times New Roman, serif',\n", "            'size': 21,  # Increased from 18\n", "            'color': 'black'\n", "        },\n", "        'plot_bgcolor': 'white',\n", "        'paper_bgcolor': 'white',\n", "        'margin': dict(l=80, r=80, t=80, b=80),\n", "        'showlegend': True,\n", "        'legend': {\n", "            'font': {\n", "                'family': 'Times New Roman, serif',\n", "                'size': 19,  # Increased from 16\n", "                'color': 'black'\n", "            },\n", "            'bgcolor': 'rgba(255,255,255,0.8)',\n", "            'bordercolor': 'black',\n", "            'borderwidth': 1\n", "        }\n", "    }\n", "\n", "def create_professional_title_style():\n", "    \"\"\"Create professional title styling\"\"\"\n", "    return {\n", "        'font': {\n", "            'family': 'Times New Roman, serif',\n", "            'size': 24,  # Increased from 20\n", "            'color': 'black'\n", "        }\n", "    }\n", "\n", "def create_professional_axis_style():\n", "    \"\"\"Create professional axis styling\"\"\"\n", "    return {\n", "        'showline': True,\n", "        'linewidth': 2,\n", "        'linecolor': 'black',\n", "        'mirror': True,\n", "        'ticks': 'outside',\n", "        'tickwidth': 2,\n", "        'tickcolor': 'black',\n", "        'tickfont': {\n", "            'family': 'Times New Roman, serif',\n", "            'size': 19,  # Increased from 16\n", "            'color': 'black'\n", "        },\n", "        'title': {\n", "            'font': {\n", "                'family': 'Times New Roman, serif',\n", "                'size': 22,  # Increased from 18\n", "                'color': 'black'\n", "            }\n", "        },\n", "        'showgrid': True,\n", "        'gridwidth': 1,\n", "        'gridcolor': 'lightgray',\n", "        'zeroline': True,\n", "        'zerolinewidth': 2,\n", "        'zerolinecolor': 'black'\n", "    }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Definition\n", "\n", "Define the experimental data points for Cr substitution ratio (x) and Dirac Point Energy Level (E_D)."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Experimental Data:\n", "   x (Cr Substitution Ratio)  E_D (eV)\n", "0                       0.00     -1.00\n", "1                       0.15     -0.90\n", "2                       0.25     -0.75\n", "3                       0.35     -0.40\n", "4                       0.45      0.00\n"]}], "source": ["# Experimental data: (x, E_D) pairs\n", "x_data = np.array([0, 0.15, 0.25, 0.35, 0.45])  # Cr Substitution Ratio\n", "E_D_data = np.array([-1, -0.9, -0.75, -0.4, 0])  # Dirac Point Energy Level (eV)\n", "\n", "# Create DataFrame for easy viewing\n", "data_df = pd.DataFrame({\n", "    'x (Cr Substitution Ratio)': x_data,\n", "    'E_D (eV)': E_D_data\n", "})\n", "\n", "print(\"Experimental Data:\")\n", "print(data_df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Power Law Fitting\n", "\n", "Fit the data to a power law function of the form: E_D = a * x^b + c"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Power Law Fit Results:\n", "E_D = a * x^b + c\n", "a = 5.8437 ± 0.7599\n", "b = 2.2031 ± 0.1646\n", "c = -0.9999 ± 0.0225\n", "R² = 0.9981\n"]}], "source": ["def power_law(x, a, b, c):\n", "    \"\"\"Power law function: E_D = a * x^b + c\"\"\"\n", "    # Handle x=0 case to avoid division by zero\n", "    result = np.zeros_like(x)\n", "    mask = x > 0\n", "    result[mask] = a * np.power(x[mask], b) + c\n", "    result[~mask] = c  # When x=0, E_D = c\n", "    return result\n", "\n", "# Initial parameter guesses\n", "# Since E_D goes from -1 to 0 as x goes from 0 to 0.45\n", "# We expect: a > 0, b > 0, c ≈ -1\n", "initial_guess = [2.0, 1.0, -1.0]\n", "\n", "# Perform curve fitting\n", "try:\n", "    popt, pcov = curve_fit(power_law, x_data, E_D_data, p0=initial_guess, maxfev=5000)\n", "    a_fit, b_fit, c_fit = popt\n", "    \n", "    # Calculate parameter uncertainties\n", "    param_errors = np.sqrt(np.diag(pcov))\n", "    a_err, b_err, c_err = param_errors\n", "    \n", "    print(f\"Power Law Fit Results:\")\n", "    print(f\"E_D = a * x^b + c\")\n", "    print(f\"a = {a_fit:.4f} ± {a_err:.4f}\")\n", "    print(f\"b = {b_fit:.4f} ± {b_err:.4f}\")\n", "    print(f\"c = {c_fit:.4f} ± {c_err:.4f}\")\n", "    \n", "    # Calculate R-squared\n", "    y_pred = power_law(x_data, a_fit, b_fit, c_fit)\n", "    r2 = r2_score(E_D_data, y_pred)\n", "    print(f\"R² = {r2:.4f}\")\n", "    \n", "    fit_successful = True\n", "    \n", "except Exception as e:\n", "    print(f\"Fitting failed: {e}\")\n", "    fit_successful = False"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Professional Visualization\n", "\n", "Create a professional plot using the ARPES GUI styling with the experimental data and fitted power law curve."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"marker": {"color": "red", "line": {"color": "black", "width": 2}, "size": 12, "symbol": "circle"}, "mode": "markers", "name": "Measured Dirac Point Energy Levels", "showlegend": true, "type": "scatter", "x": [0, 0.15, 0.25, 0.35, 0.45], "y": [-1, -0.9, -0.75, -0.4, 0]}, {"line": {"color": "blue", "width": 3}, "mode": "lines", "name": "Power Law Fit<br>E<sub>D</sub> = 5.844x<sup>2.203</sup> + -1.000<br>R² = 0.9981", "showlegend": true, "type": "scatter", "x": [0, 0.0005005005005005005, 0.001001001001001001, 0.0015015015015015015, 0.002002002002002002, 0.0025025025025025025, 0.003003003003003003, 0.0035035035035035035, 0.004004004004004004, 0.0045045045045045045, 0.005005005005005005, 0.0055055055055055055, 0.006006006006006006, 0.0065065065065065065, 0.007007007007007007, 0.0075075075075075074, 0.008008008008008008, 0.008508508508508508, 0.009009009009009009, 0.00950950950950951, 0.01001001001001001, 0.01051051051051051, 0.011011011011011011, 0.011511511511511512, 0.012012012012012012, 0.012512512512512512, 0.013013013013013013, 0.013513513513513514, 0.014014014014014014, 0.014514514514514514, 0.015015015015015015, 0.015515515515515516, 0.016016016016016016, 0.016516516516516516, 0.017017017017017015, 0.017517517517517518, 0.018018018018018018, 0.018518518518518517, 0.01901901901901902, 0.01951951951951952, 0.02002002002002002, 0.02052052052052052, 0.02102102102102102, 0.021521521521521522, 0.022022022022022022, 0.02252252252252252, 0.023023023023023025, 0.023523523523523524, 0.024024024024024024, 0.024524524524524523, 0.025025025025025023, 0.025525525525525526, 0.026026026026026026, 0.026526526526526525, 0.02702702702702703, 0.027527527527527528, 0.028028028028028028, 0.028528528528528527, 0.029029029029029027, 0.02952952952952953, 0.03003003003003003, 0.03053053053053053, 0.031031031031031032, 0.03153153153153153, 0.03203203203203203, 0.032532532532532535, 0.03303303303303303, 0.033533533533533534, 0.03403403403403403, 0.03453453453453453, 0.035035035035035036, 0.03553553553553553, 0.036036036036036036, 0.03653653653653654, 0.037037037037037035, 0.03753753753753754, 0.03803803803803804, 0.03853853853853854, 0.03903903903903904, 0.03953953953953954, 0.04004004004004004, 0.04054054054054054, 0.04104104104104104, 0.04154154154154154, 0.04204204204204204, 0.04254254254254254, 0.043043043043043044, 0.04354354354354354, 0.044044044044044044, 0.04454454454454455, 0.04504504504504504, 0.045545545545545546, 0.04604604604604605, 0.046546546546546545, 0.04704704704704705, 0.047547547547547545, 0.04804804804804805, 0.04854854854854855, 0.04904904904904905, 0.04954954954954955, 0.050050050050050046, 0.05055055055055055, 0.05105105105105105, 0.05155155155155155, 0.05205205205205205, 0.052552552552552555, 0.05305305305305305, 0.053553553553553554, 0.05405405405405406, 0.05455455455455455, 0.055055055055055056, 0.05555555555555555, 0.056056056056056056, 0.05655655655655656, 0.057057057057057055, 0.05755755755755756, 0.058058058058058054, 0.05855855855855856, 0.05905905905905906, 0.059559559559559556, 0.06006006006006006, 0.06056056056056056, 0.06106106106106106, 0.06156156156156156, 0.062062062062062065, 0.06256256256256257, 0.06306306306306306, 0.06356356356356356, 0.06406406406406406, 0.06456456456456457, 0.06506506506506507, 0.06556556556556556, 0.06606606606606606, 0.06656656656656657, 0.06706706706706707, 0.06756756756756757, 0.06806806806806806, 0.06856856856856856, 0.06906906906906907, 0.06956956956956957, 0.07007007007007007, 0.07057057057057058, 0.07107107107107107, 0.07157157157157157, 0.07207207207207207, 0.07257257257257257, 0.07307307307307308, 0.07357357357357357, 0.07407407407407407, 0.07457457457457457, 0.07507507507507508, 0.07557557557557558, 0.07607607607607608, 0.07657657657657657, 0.07707707707707707, 0.07757757757757758, 0.07807807807807808, 0.07857857857857858, 0.07907907907907907, 0.07957957957957958, 0.08008008008008008, 0.08058058058058058, 0.08108108108108109, 0.08158158158158157, 0.08208208208208208, 0.08258258258258258, 0.08308308308308308, 0.08358358358358359, 0.08408408408408408, 0.08458458458458458, 0.08508508508508508, 0.08558558558558559, 0.08608608608608609, 0.08658658658658659, 0.08708708708708708, 0.08758758758758758, 0.08808808808808809, 0.08858858858858859, 0.0890890890890891, 0.08958958958958958, 0.09009009009009009, 0.09059059059059059, 0.09109109109109109, 0.0915915915915916, 0.0920920920920921, 0.09259259259259259, 0.09309309309309309, 0.0935935935935936, 0.0940940940940941, 0.0945945945945946, 0.09509509509509509, 0.09559559559559559, 0.0960960960960961, 0.0965965965965966, 0.0970970970970971, 0.09759759759759759, 0.0980980980980981, 0.0985985985985986, 0.0990990990990991, 0.0995995995995996, 0.10010010010010009, 0.1006006006006006, 0.1011011011011011, 0.1016016016016016, 0.1021021021021021, 0.10260260260260261, 0.1031031031031031, 0.1036036036036036, 0.1041041041041041, 0.1046046046046046, 0.10510510510510511, 0.1056056056056056, 0.1061061061061061, 0.1066066066066066, 0.10710710710710711, 0.10760760760760761, 0.10810810810810811, 0.1086086086086086, 0.1091091091091091, 0.10960960960960961, 0.11011011011011011, 0.11061061061061062, 0.1111111111111111, 0.11161161161161161, 0.11211211211211211, 0.11261261261261261, 0.11311311311311312, 0.1136136136136136, 0.11411411411411411, 0.11461461461461461, 0.11511511511511512, 0.11561561561561562, 0.11611611611611611, 0.11661661661661661, 0.11711711711711711, 0.11761761761761762, 0.11811811811811812, 0.11861861861861862, 0.11911911911911911, 0.11961961961961962, 0.12012012012012012, 0.12062062062062062, 0.12112112112112113, 0.12162162162162161, 0.12212212212212212, 0.12262262262262262, 0.12312312312312312, 0.12362362362362363, 0.12412412412412413, 0.12462462462462462, 0.12512512512512514, 0.12562562562562563, 0.12612612612612611, 0.12662662662662663, 0.12712712712712712, 0.12762762762762764, 0.12812812812812813, 0.12862862862862862, 0.12912912912912913, 0.12962962962962962, 0.13013013013013014, 0.13063063063063063, 0.13113113113113112, 0.13163163163163163, 0.13213213213213212, 0.13263263263263264, 0.13313313313313313, 0.13363363363363362, 0.13413413413413414, 0.13463463463463463, 0.13513513513513514, 0.13563563563563563, 0.13613613613613612, 0.13663663663663664, 0.13713713713713713, 0.13763763763763764, 0.13813813813813813, 0.13863863863863865, 0.13913913913913914, 0.13963963963963963, 0.14014014014014015, 0.14064064064064064, 0.14114114114114115, 0.14164164164164164, 0.14214214214214213, 0.14264264264264265, 0.14314314314314314, 0.14364364364364365, 0.14414414414414414, 0.14464464464464463, 0.14514514514514515, 0.14564564564564564, 0.14614614614614616, 0.14664664664664664, 0.14714714714714713, 0.14764764764764765, 0.14814814814814814, 0.14864864864864866, 0.14914914914914915, 0.14964964964964964, 0.15015015015015015, 0.15065065065065064, 0.15115115115115116, 0.15165165165165165, 0.15215215215215216, 0.15265265265265265, 0.15315315315315314, 0.15365365365365366, 0.15415415415415415, 0.15465465465465467, 0.15515515515515516, 0.15565565565565564, 0.15615615615615616, 0.15665665665665665, 0.15715715715715717, 0.15765765765765766, 0.15815815815815815, 0.15865865865865866, 0.15915915915915915, 0.15965965965965967, 0.16016016016016016, 0.16066066066066065, 0.16116116116116116, 0.16166166166166165, 0.16216216216216217, 0.16266266266266266, 0.16316316316316315, 0.16366366366366367, 0.16416416416416416, 0.16466466466466467, 0.16516516516516516, 0.16566566566566565, 0.16616616616616617, 0.16666666666666666, 0.16716716716716717, 0.16766766766766766, 0.16816816816816815, 0.16866866866866867, 0.16916916916916916, 0.16966966966966968, 0.17017017017017017, 0.17067067067067068, 0.17117117117117117, 0.17167167167167166, 0.17217217217217218, 0.17267267267267267, 0.17317317317317318, 0.17367367367367367, 0.17417417417417416, 0.17467467467467468, 0.17517517517517517, 0.17567567567567569, 0.17617617617617617, 0.17667667667667666, 0.17717717717717718, 0.17767767767767767, 0.1781781781781782, 0.17867867867867868, 0.17917917917917917, 0.17967967967967968, 0.18018018018018017, 0.1806806806806807, 0.18118118118118118, 0.18168168168168167, 0.18218218218218218, 0.18268268268268267, 0.1831831831831832, 0.18368368368368368, 0.1841841841841842, 0.18468468468468469, 0.18518518518518517, 0.1856856856856857, 0.18618618618618618, 0.1866866866866867, 0.1871871871871872, 0.18768768768768768, 0.1881881881881882, 0.18868868868868868, 0.1891891891891892, 0.1896896896896897, 0.19019019019019018, 0.1906906906906907, 0.19119119119119118, 0.1916916916916917, 0.1921921921921922, 0.19269269269269268, 0.1931931931931932, 0.19369369369369369, 0.1941941941941942, 0.1946946946946947, 0.19519519519519518, 0.1956956956956957, 0.1961961961961962, 0.1966966966966967, 0.1971971971971972, 0.19769769769769768, 0.1981981981981982, 0.1986986986986987, 0.1991991991991992, 0.1996996996996997, 0.20020020020020018, 0.2007007007007007, 0.2012012012012012, 0.2017017017017017, 0.2022022022022022, 0.20270270270270271, 0.2032032032032032, 0.2037037037037037, 0.2042042042042042, 0.2047047047047047, 0.20520520520520522, 0.2057057057057057, 0.2062062062062062, 0.2067067067067067, 0.2072072072072072, 0.20770770770770772, 0.2082082082082082, 0.2087087087087087, 0.2092092092092092, 0.2097097097097097, 0.21021021021021022, 0.2107107107107107, 0.2112112112112112, 0.21171171171171171, 0.2122122122122122, 0.21271271271271272, 0.2132132132132132, 0.2137137137137137, 0.21421421421421422, 0.2147147147147147, 0.21521521521521522, 0.2157157157157157, 0.21621621621621623, 0.21671671671671672, 0.2172172172172172, 0.21771771771771772, 0.2182182182182182, 0.21871871871871873, 0.21921921921921922, 0.2197197197197197, 0.22022022022022023, 0.22072072072072071, 0.22122122122122123, 0.22172172172172172, 0.2222222222222222, 0.22272272272272273, 0.22322322322322322, 0.22372372372372373, 0.22422422422422422, 0.2247247247247247, 0.22522522522522523, 0.22572572572572572, 0.22622622622622623, 0.22672672672672672, 0.2272272272272272, 0.22772772772772773, 0.22822822822822822, 0.22872872872872874, 0.22922922922922923, 0.22972972972972971, 0.23023023023023023, 0.23073073073073072, 0.23123123123123124, 0.23173173173173173, 0.23223223223223222, 0.23273273273273273, 0.23323323323323322, 0.23373373373373374, 0.23423423423423423, 0.23473473473473475, 0.23523523523523523, 0.23573573573573572, 0.23623623623623624, 0.23673673673673673, 0.23723723723723725, 0.23773773773773774, 0.23823823823823823, 0.23873873873873874, 0.23923923923923923, 0.23973973973973975, 0.24024024024024024, 0.24074074074074073, 0.24124124124124124, 0.24174174174174173, 0.24224224224224225, 0.24274274274274274, 0.24324324324324323, 0.24374374374374375, 0.24424424424424424, 0.24474474474474475, 0.24524524524524524, 0.24574574574574573, 0.24624624624624625, 0.24674674674674674, 0.24724724724724725, 0.24774774774774774, 0.24824824824824826, 0.24874874874874875, 0.24924924924924924, 0.24974974974974976, 0.2502502502502503, 0.25075075075075076, 0.25125125125125125, 0.25175175175175174, 0.25225225225225223, 0.2527527527527528, 0.25325325325325326, 0.25375375375375375, 0.25425425425425424, 0.25475475475475473, 0.2552552552552553, 0.25575575575575576, 0.25625625625625625, 0.25675675675675674, 0.25725725725725723, 0.2577577577577578, 0.25825825825825827, 0.25875875875875876, 0.25925925925925924, 0.25975975975975973, 0.2602602602602603, 0.26076076076076077, 0.26126126126126126, 0.26176176176176175, 0.26226226226226224, 0.2627627627627628, 0.26326326326326327, 0.26376376376376376, 0.26426426426426425, 0.26476476476476474, 0.2652652652652653, 0.26576576576576577, 0.26626626626626626, 0.26676676676676675, 0.26726726726726724, 0.2677677677677678, 0.2682682682682683, 0.26876876876876876, 0.26926926926926925, 0.26976976976976974, 0.2702702702702703, 0.2707707707707708, 0.27127127127127126, 0.27177177177177175, 0.27227227227227224, 0.2727727727727728, 0.2732732732732733, 0.27377377377377377, 0.27427427427427425, 0.2747747747747748, 0.2752752752752753, 0.2757757757757758, 0.27627627627627627, 0.27677677677677676, 0.2772772772772773, 0.2777777777777778, 0.2782782782782783, 0.27877877877877877, 0.27927927927927926, 0.2797797797797798, 0.2802802802802803, 0.2807807807807808, 0.28128128128128127, 0.28178178178178176, 0.2822822822822823, 0.2827827827827828, 0.2832832832832833, 0.28378378378378377, 0.28428428428428426, 0.2847847847847848, 0.2852852852852853, 0.2857857857857858, 0.2862862862862863, 0.28678678678678676, 0.2872872872872873, 0.2877877877877878, 0.2882882882882883, 0.2887887887887888, 0.28928928928928926, 0.2897897897897898, 0.2902902902902903, 0.2907907907907908, 0.2912912912912913, 0.29179179179179177, 0.2922922922922923, 0.2927927927927928, 0.2932932932932933, 0.2937937937937938, 0.29429429429429427, 0.2947947947947948, 0.2952952952952953, 0.2957957957957958, 0.2962962962962963, 0.29679679679679677, 0.2972972972972973, 0.2977977977977978, 0.2982982982982983, 0.2987987987987988, 0.29929929929929927, 0.2997997997997998, 0.3003003003003003, 0.3008008008008008, 0.3013013013013013, 0.3018018018018018, 0.3023023023023023, 0.3028028028028028, 0.3033033033033033, 0.3038038038038038, 0.30430430430430433, 0.3048048048048048, 0.3053053053053053, 0.3058058058058058, 0.3063063063063063, 0.30680680680680683, 0.3073073073073073, 0.3078078078078078, 0.3083083083083083, 0.3088088088088088, 0.30930930930930933, 0.3098098098098098, 0.3103103103103103, 0.3108108108108108, 0.3113113113113113, 0.31181181181181183, 0.3123123123123123, 0.3128128128128128, 0.3133133133133133, 0.3138138138138138, 0.31431431431431434, 0.3148148148148148, 0.3153153153153153, 0.3158158158158158, 0.3163163163163163, 0.31681681681681684, 0.3173173173173173, 0.3178178178178178, 0.3183183183183183, 0.3188188188188188, 0.31931931931931934, 0.31981981981981983, 0.3203203203203203, 0.3208208208208208, 0.3213213213213213, 0.32182182182182184, 0.32232232232232233, 0.3228228228228228, 0.3233233233233233, 0.3238238238238238, 0.32432432432432434, 0.32482482482482483, 0.3253253253253253, 0.3258258258258258, 0.3263263263263263, 0.32682682682682684, 0.32732732732732733, 0.3278278278278278, 0.3283283283283283, 0.3288288288288288, 0.32932932932932935, 0.32982982982982983, 0.3303303303303303, 0.3308308308308308, 0.3313313313313313, 0.33183183183183185, 0.33233233233233234, 0.3328328328328328, 0.3333333333333333, 0.3338338338338338, 0.33433433433433435, 0.33483483483483484, 0.3353353353353353, 0.3358358358358358, 0.3363363363363363, 0.33683683683683685, 0.33733733733733734, 0.33783783783783783, 0.3383383383383383, 0.33883883883883886, 0.33933933933933935, 0.33983983983983984, 0.34034034034034033, 0.3408408408408408, 0.34134134134134136, 0.34184184184184185, 0.34234234234234234, 0.34284284284284283, 0.3433433433433433, 0.34384384384384387, 0.34434434434434436, 0.34484484484484484, 0.34534534534534533, 0.3458458458458458, 0.34634634634634637, 0.34684684684684686, 0.34734734734734735, 0.34784784784784784, 0.3483483483483483, 0.34884884884884887, 0.34934934934934936, 0.34984984984984985, 0.35035035035035034, 0.3508508508508508, 0.35135135135135137, 0.35185185185185186, 0.35235235235235235, 0.35285285285285284, 0.3533533533533533, 0.3538538538538539, 0.35435435435435436, 0.35485485485485485, 0.35535535535535534, 0.35585585585585583, 0.3563563563563564, 0.35685685685685686, 0.35735735735735735, 0.35785785785785784, 0.35835835835835833, 0.3588588588588589, 0.35935935935935936, 0.35985985985985985, 0.36036036036036034, 0.36086086086086083, 0.3613613613613614, 0.36186186186186187, 0.36236236236236236, 0.36286286286286284, 0.36336336336336333, 0.3638638638638639, 0.36436436436436437, 0.36486486486486486, 0.36536536536536535, 0.36586586586586584, 0.3663663663663664, 0.36686686686686687, 0.36736736736736736, 0.36786786786786785, 0.3683683683683684, 0.3688688688688689, 0.36936936936936937, 0.36986986986986986, 0.37037037037037035, 0.3708708708708709, 0.3713713713713714, 0.3718718718718719, 0.37237237237237236, 0.37287287287287285, 0.3733733733733734, 0.3738738738738739, 0.3743743743743744, 0.37487487487487486, 0.37537537537537535, 0.3758758758758759, 0.3763763763763764, 0.3768768768768769, 0.37737737737737737, 0.37787787787787785, 0.3783783783783784, 0.3788788788788789, 0.3793793793793794, 0.37987987987987987, 0.38038038038038036, 0.3808808808808809, 0.3813813813813814, 0.3818818818818819, 0.38238238238238237, 0.38288288288288286, 0.3833833833833834, 0.3838838838838839, 0.3843843843843844, 0.38488488488488487, 0.38538538538538536, 0.3858858858858859, 0.3863863863863864, 0.3868868868868869, 0.38738738738738737, 0.38788788788788786, 0.3883883883883884, 0.3888888888888889, 0.3893893893893894, 0.3898898898898899, 0.39039039039039036, 0.3908908908908909, 0.3913913913913914, 0.3918918918918919, 0.3923923923923924, 0.39289289289289286, 0.3933933933933934, 0.3938938938938939, 0.3943943943943944, 0.3948948948948949, 0.39539539539539537, 0.3958958958958959, 0.3963963963963964, 0.3968968968968969, 0.3973973973973974, 0.39789789789789787, 0.3983983983983984, 0.3988988988988989, 0.3993993993993994, 0.3998998998998999, 0.40040040040040037, 0.4009009009009009, 0.4014014014014014, 0.4019019019019019, 0.4024024024024024, 0.4029029029029029, 0.4034034034034034, 0.4039039039039039, 0.4044044044044044, 0.4049049049049049, 0.40540540540540543, 0.4059059059059059, 0.4064064064064064, 0.4069069069069069, 0.4074074074074074, 0.40790790790790793, 0.4084084084084084, 0.4089089089089089, 0.4094094094094094, 0.4099099099099099, 0.41041041041041043, 0.4109109109109109, 0.4114114114114114, 0.4119119119119119, 0.4124124124124124, 0.41291291291291293, 0.4134134134134134, 0.4139139139139139, 0.4144144144144144, 0.4149149149149149, 0.41541541541541543, 0.4159159159159159, 0.4164164164164164, 0.4169169169169169, 0.4174174174174174, 0.41791791791791794, 0.4184184184184184, 0.4189189189189189, 0.4194194194194194, 0.4199199199199199, 0.42042042042042044, 0.4209209209209209, 0.4214214214214214, 0.4219219219219219, 0.4224224224224224, 0.42292292292292294, 0.42342342342342343, 0.4239239239239239, 0.4244244244244244, 0.4249249249249249, 0.42542542542542544, 0.42592592592592593, 0.4264264264264264, 0.4269269269269269, 0.4274274274274274, 0.42792792792792794, 0.42842842842842843, 0.4289289289289289, 0.4294294294294294, 0.4299299299299299, 0.43043043043043044, 0.43093093093093093, 0.4314314314314314, 0.4319319319319319, 0.43243243243243246, 0.43293293293293295, 0.43343343343343343, 0.4339339339339339, 0.4344344344344344, 0.43493493493493496, 0.43543543543543545, 0.43593593593593594, 0.4364364364364364, 0.4369369369369369, 0.43743743743743746, 0.43793793793793795, 0.43843843843843844, 0.4389389389389389, 0.4394394394394394, 0.43993993993993996, 0.44044044044044045, 0.44094094094094094, 0.44144144144144143, 0.4419419419419419, 0.44244244244244246, 0.44294294294294295, 0.44344344344344344, 0.44394394394394393, 0.4444444444444444, 0.44494494494494496, 0.44544544544544545, 0.44594594594594594, 0.44644644644644643, 0.4469469469469469, 0.44744744744744747, 0.44794794794794796, 0.44844844844844844, 0.44894894894894893, 0.4494494494494494, 0.44994994994994997, 0.45045045045045046, 0.45095095095095095, 0.45145145145145144, 0.4519519519519519, 0.45245245245245247, 0.45295295295295296, 0.45345345345345345, 0.45395395395395394, 0.4544544544544544, 0.45495495495495497, 0.45545545545545546, 0.45595595595595595, 0.45645645645645644, 0.45695695695695693, 0.4574574574574575, 0.45795795795795796, 0.45845845845845845, 0.45895895895895894, 0.45945945945945943, 0.45995995995996, 0.46046046046046046, 0.46096096096096095, 0.46146146146146144, 0.46196196196196193, 0.4624624624624625, 0.46296296296296297, 0.46346346346346345, 0.46396396396396394, 0.46446446446446443, 0.464964964964965, 0.46546546546546547, 0.46596596596596596, 0.46646646646646645, 0.466966966966967, 0.4674674674674675, 0.46796796796796797, 0.46846846846846846, 0.46896896896896895, 0.4694694694694695, 0.46996996996997, 0.47047047047047047, 0.47097097097097096, 0.47147147147147145, 0.471971971971972, 0.4724724724724725, 0.47297297297297297, 0.47347347347347346, 0.47397397397397395, 0.4744744744744745, 0.474974974974975, 0.4754754754754755, 0.47597597597597596, 0.47647647647647645, 0.476976976976977, 0.4774774774774775, 0.477977977977978, 0.47847847847847846, 0.47897897897897895, 0.4794794794794795, 0.47997997997998, 0.4804804804804805, 0.48098098098098097, 0.48148148148148145, 0.481981981981982, 0.4824824824824825, 0.482982982982983, 0.48348348348348347, 0.48398398398398396, 0.4844844844844845, 0.484984984984985, 0.4854854854854855, 0.48598598598598597, 0.48648648648648646, 0.486986986986987, 0.4874874874874875, 0.487987987987988, 0.48848848848848847, 0.48898898898898896, 0.4894894894894895, 0.48998998998999, 0.4904904904904905, 0.49099099099099097, 0.49149149149149146, 0.491991991991992, 0.4924924924924925, 0.492992992992993, 0.4934934934934935, 0.49399399399399396, 0.4944944944944945, 0.494994994994995, 0.4954954954954955, 0.495995995995996, 0.4964964964964965, 0.496996996996997, 0.4974974974974975, 0.497997997997998, 0.4984984984984985, 0.498998998998999, 0.4994994994994995, 0.5], "y": [-0.999934683859304, -0.9999343711015203, -0.9999332437291831, -0.9999311654600114, -0.9999280526102259, -0.9999238421940156, -0.9999184829745867, -0.9999119314200396, -0.9999041494904298, -0.9998951032821711, -0.9998847621321564, -0.9998730979922478, -0.9998600849739023, -0.9998456990055586, -0.9998299175678999, -0.9998127194847369, -0.9997940847547359, -0.9997739944138557, -0.9997524304213469, -0.9997293755641434, -0.9997048133758374, -0.9996787280673692, -0.9996511044672475, -0.9996219279696, -0.9995911844887265, -0.9995588604190944, -0.9995249425999285, -0.9994894182837076, -0.9994522751080027, -0.9994135010701951, -0.9993730845046873, -0.9993310140622855, -0.99928727869148, -0.9992418676213958, -0.9991947703462163, -0.999145976610913, -0.9990954763981358, -0.9990432599161407, -0.9989893175876455, -0.9989336400395199, -0.9988762180932265, -0.9988170427559417, -0.9987561052122915, -0.9986933968166448, -0.9986289090859175, -0.9985626336928387, -0.9984945624596427, -0.9984246873521485, -0.998353000474198, -0.9982794940624208, -0.998204160481303, -0.9981269922185347, -0.9980479818806162, -0.9979671221887024, -0.9978844059746709, -0.997799826177394, -0.9977133758392043, -0.9976250481025374, -0.9975348362067422, -0.997442733485046, -0.9973487333616653, -0.9972528293490525, -0.9971550150452704, -0.9970552841314859, -0.996953630369577, -0.9968500475998437, -0.9967445297388206, -0.9966370707771807, -0.9965276647777294, -0.9964163058734812, -0.996302988265814, -0.9961877062226999, -0.996070454077004, -0.9959512262248519, -0.9958300171240586, -0.9957068212926186, -0.9955816333072518, -0.9954544478020041, -0.9953252594668986, -0.9951940630466362, -0.9950608533393415, -0.9949256251953544, -0.9947883735160629, -0.9946490932527752, -0.9945077794056317, -0.9943644270225528, -0.994219031198221, -0.994071587073097, -0.9939220898324679, -0.993770534705525, -0.9936169169644716, -0.9934612319236581, -0.9933034749387443, -0.9931436414058868, -0.9929817267609523, -0.9928177264787524, -0.992651636072303, -0.9924834510921042, -0.9923131671254418, -0.9921407797957085, -0.9919662847617442, -0.9917896777171955, -0.9916109543898928, -0.9914301105412437, -0.9912471419656449, -0.9910620444899076, -0.9908748139727006, -0.9906854463040067, -0.9904939374045933, -0.9903002832254986, -0.9901044797475281, -0.9899065229807673, -0.9897064089641038, -0.9895041337647633, -0.9892996934778565, -0.989093084225937, -0.9888843021585703, -0.9886733434519132, -0.9884602043083034, -0.9882448809558585, -0.9880273696480848, -0.9878076666634952, -0.987585768305236, -0.9873616709007225, -0.9871353708012819, -0.986906864381806, -0.9866761480404104, -0.9864432181981013, -0.9862080712984511, -0.985970703807279, -0.9857311122123402, -0.9854892930230212, -0.9852452427700419, -0.984998958005163, -0.9847504353009018, -0.9844996712502513, -0.9842466624664072, -0.9839914055824995, -0.9837338972513299, -0.9834741341451143, -0.9832121129552313, -0.9829478303919748, -0.9826812831843115, -0.9824124680796446, -0.9821413818435801, -0.9818680212596994, -0.9815923831293353, -0.9813144642713525, -0.981034261521933, -0.9807517717343638, -0.9804669917788313, -0.980179918542217, -0.979890548927898, -0.9795988798555516, -0.9793049082609634, -0.9790086310958376, -0.9787100453276127, -0.978409147939279, -0.9781059359292003, -0.9778004063109377, -0.9774925561130782, -0.9771823823790645, -0.976869882167029, -0.9765550525496297, -0.9762378906138903, -0.9759183934610416, -0.9755965582063664, -0.9752723819790466, -0.9749458619220138, -0.9746169951918008, -0.9742857789583969, -0.9739522104051054, -0.9736162867284026, -0.9732780051378004, -0.9729373628557095, -0.972594357117307, -0.9722489851704035, -0.9719012442753144, -0.971551131704733, -0.9711986447436036, -0.9708437806889998, -0.9704865368500019, -0.9701269105475776, -0.9697648991144644, -0.9694004998950536, -0.9690337102452758, -0.968664527532489, -0.9682929491353671, -0.9679189724437918, -0.967542594858744, -0.967163813792199, -0.9667826266670212, -0.9663990309168623, -0.9660130239860593, -0.9656246033295348, -0.9652337664126993, -0.9648405107113532, -0.9644448337115921, -0.9640467329097122, -0.9636462058121176, -0.9632432499352285, -0.9628378628053915, -0.9624300419587899, -0.9620197849413563, -0.9616070893086861, -0.961191952625952, -0.9607743724678199, -0.9603543464183656, -0.9599318720709931, -0.9595069470283538, -0.9590795689022666, -0.9586497353136397, -0.9582174438923919, -0.9577826922773773, -0.957345478116309, -0.9569057990656843, -0.9564636527907117, -0.9560190369652377, -0.9555719492716754, -0.9551223874009338, -0.9546703490523473, -0.954215831933607, -0.9537588337606931, -0.9532993522578068, -0.9528373851573044, -0.9523729301996313, -0.9519059851332577, -0.9514365477146144, -0.9509646157080294, -0.9504901868856657, -0.9500132590274597, -0.9495338299210604, -0.949051897361769, -0.9485674591524796, -0.9480805131036204, -0.9475910570330958, -0.9470990887662291, -0.9466046061357056, -0.9461076069815174, -0.945608089150907, -0.9451060504983133, -0.9446014888853178, -0.9440944021805905, -0.9435847882598378, -0.9430726450057499, -0.9425579703079495, -0.9420407620629409, -0.9415210181740589, -0.9409987365514201, -0.9404739151118728, -0.9399465517789487, -0.9394166444828146, -0.9388841911602246, -0.9383491897544736, -0.9378116382153499, -0.9372715344990898, -0.9367288765683323, -0.9361836623920728, -0.9356358899456201, -0.935085557210551, -0.9345326621746676, -0.933977202831954, -0.9334191771825333, -0.9328585832326256, -0.9322954189945064, -0.9317296824864651, -0.9311613717327641, -0.9305904847635987, -0.9300170196150563, -0.9294409743290779, -0.9288623469534181, -0.9282811355416063, -0.9276973381529089, -0.927110952852291, -0.9265219777103786, -0.9259304108034216, -0.9253362502132574, -0.9247394940272734, -0.9241401403383719, -0.9235381872449338, -0.9229336328507836, -0.922326475265154, -0.9217167126026519, -0.9211043429832232, -0.9204893645321196, -0.9198717753798643, -0.9192515736622194, -0.9186287575201524, -0.9180033250998034, -0.9173752745524536, -0.9167446040344922, -0.9161113117073856, -0.9154753957376451, -0.914836854296797, -0.9141956855613501, -0.9135518877127669, -0.9129054589374321, -0.9122563974266237, -0.9116047013764823, -0.9109503689879823, -0.9102933984669028, -0.9096337880237988, -0.9089715358739721, -0.9083066402374437, -0.9076390993389253, -0.9069689114077919, -0.9062960746780536, -0.9056205873883293, -0.9049424477818188, -0.9042616541062763, -0.9035782046139843, -0.9028920975617264, -0.9022033312107622, -0.9015119038268009, -0.9008178136799758, -0.900121059044819, -0.8994216382002361, -0.8987195494294817, -0.8980147910201344, -0.8973073612640718, -0.8965972584574476, -0.8958844809006661, -0.8951690268983592, -0.8944508947593625, -0.893730082796692, -0.8930065893275206, -0.8922804126731556, -0.8915515511590151, -0.8908200031146061, -0.8900857668735016, -0.8893488407733187, -0.8886092231556961, -0.8878669123662725, -0.8871219067546652, -0.8863742046744478, -0.8856238044831297, -0.8848707045421346, -0.8841149032167792, -0.8833563988762532, -0.8825951898935979, -0.8818312746456859, -0.8810646515132015, -0.8802953188806196, -0.8795232751361864, -0.8787485186718994, -0.8779710478834877, -0.8771908611703925, -0.8764079569357482, -0.8756223335863629, -0.8748339895326989, -0.8740429231888551, -0.8732491329725472, -0.8724526173050897, -0.8716533746113772, -0.8708514033198661, -0.8700467018625573, -0.8692392686749768, -0.8684291021961593, -0.8676162008686297, -0.8668005631383854, -0.8659821874548798, -0.8651610722710039, -0.86433721604307, -0.8635106172307946, -0.8626812742972807, -0.8618491857090022, -0.8610143499357864, -0.8601767654507984, -0.8593364307305233, -0.8584933442547518, -0.8576475045065625, -0.8567989099723066, -0.8559475591415922, -0.8550934505072679, -0.8542365825654082, -0.8533769538152969, -0.8525145627594117, -0.8516494079034104, -0.8507814877561137, -0.8499108008294917, -0.849037345638648, -0.8481611207018053, -0.8472821245402907, -0.8464003556785209, -0.8455158126439875, -0.8446284939672432, -0.8437383981818866, -0.842845523824549, -0.8419498694348796, -0.8410514335555316, -0.8401502147321487, -0.8392462115133504, -0.8383394224507198, -0.8374298460987882, -0.8365174810150232, -0.8356023257598142, -0.8346843788964594, -0.8337636389911531, -0.8328401046129718, -0.8319137743338614, -0.8309846467286247, -0.8300527203749077, -0.8291179938531881, -0.8281804657467611, -0.8272401346417279, -0.8262969991269828, -0.8253510577942013, -0.8244023092378265, -0.8234507520550587, -0.8224963848458413, -0.8215392062128504, -0.8205792147614817, -0.819616409099839, -0.8186507878387225, -0.8176823495916168, -0.8167110929746793, -0.8157370166067287, -0.8147601191092335, -0.8137803991063002, -0.8127978552246626, -0.8118124860936698, -0.8108242903452758, -0.8098332666140273, -0.8088394135370538, -0.8078427297540559, -0.8068432139072943, -0.8058408646415797, -0.804835680604261, -0.8038276604452156, -0.8028168028168381, -0.8018031063740299, -0.8007865697741889, -0.7997671916771991, -0.7987449707454197, -0.7977199056436755, -0.7966919950392461, -0.7956612376018566, -0.7946276320036663, -0.7935911769192594, -0.7925518710256354, -0.7915097130021984, -0.7904647015307475, -0.7894168352954675, -0.7883661129829186, -0.787312533282027, -0.7862560948840752, -0.785196796482693, -0.7841346367738471, -0.7830696144558323, -0.7820017282292623, -0.7809309767970598, -0.7798573588644478, -0.7787808731389404, -0.7777015183303331, -0.7766192931506946, -0.7755341963143567, -0.7744462265379071, -0.773355382540178, -0.7722616630422399, -0.7711650667673908, -0.7700655924411486, -0.7689632387912417, -0.7678580045476011, -0.7667498884423514, -0.765638889209802, -0.7645250055864392, -0.7634082363109175, -0.7622885801240511, -0.7611660357688061, -0.7600406019902914, -0.7589122775357513, -0.7577810611545568, -0.7566469515981975, -0.755509947620274, -0.7543700479764892, -0.753227251424641, -0.7520815567246135, -0.7509329626383703, -0.7497814679299455, -0.7486270713654364, -0.7474697717129961, -0.7463095677428253, -0.7451464582271643, -0.7439804419402867, -0.7428115176584902, -0.7416396841600905, -0.7404649402254129, -0.7392872846367851, -0.7381067161785299, -0.7369232336369578, -0.7357368358003598, -0.7345475214589996, -0.7333552894051074, -0.7321601384328715, -0.7309620673384323, -0.729761074919874, -0.7285571599772185, -0.7273503213124184, -0.7261405577293488, -0.724927868033802, -0.7237122510334794, -0.7224937055379851, -0.721272230358819, -0.72004782430937, -0.718820486204909, -0.7175902148625828, -0.716357009101407, -0.7151208677422591, -0.7138817896078726, -0.7126397735228296, -0.7113948183135548, -0.710146922808309, -0.7088960858371824, -0.7076423062320887, -0.7063855828267575, -0.7051259144567291, -0.7038632999593484, -0.7025977381737571, -0.7013292279408889, -0.7000577681034628, -0.6987833575059762, -0.6975059949947005, -0.6962256794176727, -0.6949424096246914, -0.6936561844673093, -0.692367002798828, -0.6910748634742916, -0.6897797653504809, -0.6884817072859075, -0.6871806881408076, -0.6858767067771363, -0.6845697620585625, -0.6832598528504616, -0.6819469780199113, -0.6806311364356845, -0.6793123269682444, -0.6779905484897388, -0.6766657998739941, -0.6753380799965099, -0.674007387734453, -0.672673721966653, -0.6713370815735951, -0.6699974654374159, -0.6686548724418975, -0.6673093014724614, -0.6659607514161643, -0.6646092211616922, -0.6632547095993543, -0.6618972156210787, -0.6605367381204066, -0.6591732759924869, -0.6578068281340717, -0.6564373934435095, -0.6550649708207419, -0.6536895591672969, -0.652311157386285, -0.6509297643823926, -0.6495453790618784, -0.6481580003325672, -0.6467676271038456, -0.6453742582866566, -0.6439778927934947, -0.6425785295384006, -0.6411761674369569, -0.6397708054062825, -0.6383624423650287, -0.6369510772333729, -0.6355367089330146, -0.6341193363871709, -0.6326989585205705, -0.6312755742594507, -0.6298491825315509, -0.6284197822661086, -0.626987372393855, -0.6255519518470096, -0.6241135195592766, -0.6226720744658386, -0.6212276155033538, -0.6197801416099502, -0.618329651725221, -0.6168761447902216, -0.6154196197474626, -0.6139600755409071, -0.6124975111159663, -0.6110319254194932, -0.6095633173997808, -0.6080916860065548, -0.6066170301909719, -0.6051393489056138, -0.6036586411044825, -0.602174905742998, -0.6006881417779917, -0.5991983481677033, -0.5977055238717764, -0.5962096678512541, -0.5947107790685746, -0.5932088564875677, -0.5917038990734493, -0.5901959057928188, -0.5886848756136533, -0.5871708075053053, -0.5856537004384966, -0.5841335533853158, -0.5826103653192135, -0.5810841352149979, -0.579554862048832, -0.5780225447982279, -0.5764871824420443, -0.5749487739604815, -0.573407318335078, -0.5718628145487064, -0.5703152615855688, -0.5687646584311945, -0.5672110040724343, -0.5656542974974577, -0.5640945376957491, -0.5625317236581029, -0.5609658543766208, -0.5593969288447075, -0.5578249460570672, -0.5562499050096994, -0.5546718046998953, -0.5530906441262342, -0.5515064222885794, -0.5499191381880755, -0.5483287908271433, -0.546735379209477, -0.5451389023400404, -0.543539359225063, -0.5419367488720372, -0.5403310702897136, -0.5387223224880976, -0.5371105044784467, -0.5354956152732657, -0.5338776538863046, -0.5322566193325537, -0.530632510628241, -0.5290053267908277, -0.5273750668390058, -0.5257417297926947, -0.5241053146730366, -0.5224658205023935, -0.5208232463043447, -0.5191775911036822, -0.5175288539264078, -0.51587703379973, -0.5142221297520602, -0.512564140813009, -0.5109030660133841, -0.5092389043851859, -0.5075716549616047, -0.5059013167770169, -0.5042278888669822, -0.50255137026824, -0.5008717600187073, -0.4991890571574732, -0.4975032607247978, -0.4958143697621079, -0.494122383311994, -0.4924273004182075, -0.49072912012565717, -0.4890278414804059, -0.4873234635296675, -0.4856159853218043, -0.48390540590632347, -0.4821917243338736, -0.4804749396562422, -0.4787550509263525, -0.47703205719826003, -0.4753059575271503, -0.47357675096933505, -0.47184443658224917, -0.47010901342444844, -0.4683704805556058, -0.46662883703650904, -0.46488408192905717, -0.46313621429625784, -0.4613852332022237, -0.45963113771217123, -0.45787392689241546, -0.45611359981036903, -0.45435015553453795, -0.45258359313451935, -0.45081391168099905, -0.44904111024574733, -0.4472651879016174, -0.4454861437225419, -0.44370397678353, -0.44191868616066543, -0.44013027093110246, -0.4383387301730639, -0.4365440629658379, -0.4347462683897755, -0.43294534552628816, -0.431141293457844, -0.42933411126796595, -0.4275237980412284, -0.4257103528632551, -0.42389377482071655, -0.422074063001326, -0.42025121649383845, -0.4184252343880468, -0.4165961157747795, -0.4147638597458988, -0.41292846539429606, -0.41108993181389153, -0.4092482580996295, -0.40740344334747725, -0.40555548665442254, -0.40370438711846934, -0.4018501438386368, -0.39999275591495653, -0.3981322224484688, -0.3962685425412221, -0.39440171529626866, -0.39253173981766276, -0.3906586152104582, -0.3887823405807055, -0.38690291503545005, -0.3850203376827289, -0.3831346076315685, -0.3812457239919823, -0.3793536858749682, -0.37745849239250673, -0.3755601426575573, -0.373658635784057, -0.3717539708869172, -0.3698461470820221, -0.3679351634862257, -0.36602101921734975, -0.3641037133941806, -0.36218324513646805, -0.3602596135649214, -0.3583328178012094, -0.3564028569679554, -0.3544697301887363, -0.35253343658808023, -0.3505939752914642, -0.3486513454253113, -0.34670554611698856, -0.3447565764948054, -0.34280443568801, -0.3408491228267886, -0.33889063704226163, -0.33692897746648265, -0.3349641432324356, -0.332996133474032, -0.33102494732611065, -0.32905058392443287, -0.3270730424056819, -0.3250923219074602, -0.32310842156828734, -0.321121340527598, -0.31913107792573947, -0.3171376329039691, -0.31514100460445316, -0.3131411921702636, -0.3111381947453771, -0.3091320114746716, -0.3071226415039251, -0.3051100839798131, -0.30309433804990615, -0.3010754028626693, -0.2990532775674577, -0.2970279613145165, -0.294999453254977, -0.2929677525408563, -0.29093285832505444, -0.28889476976135164, -0.2868534860044074, -0.2848090062097576, -0.28276132953381294, -0.28071045513385706, -0.2786563821680439, -0.2765991097953957, -0.27453863717580174, -0.27247496347001543, -0.2704080878396533, -0.26833800944719177, -0.2662647274559664, -0.26418824103016847, -0.26210854933484484, -0.2600256515358945, -0.25793954680006725, -0.25585023429496134, -0.25375771318902207, -0.25166198265153916, -0.24956304185264622, -0.2474608899633166, -0.2453555261553636, -0.243246949601437, -0.24113515947502207, -0.2390201549504377, -0.2369019352028342, -0.2347804994081908, -0.23265584674331508, -0.23052797638583977, -0.2283968875142225, -0.22626257930774207, -0.22412505094649804, -0.22198430161140803, -0.21984033048420615, -0.21769313674744184, -0.2155427195844769, -0.21338907817948405, -0.21123221171744555, -0.20907211938415127, -0.20690880036619652, -0.20474225385098, -0.20257247902670328, -0.20039947508236733, -0.19822324120777268, -0.19604377659351557, -0.19386108043098793, -0.1916751519123745, -0.18948599023065127, -0.1872935945795846, -0.18509796415372826, -0.18289909814842253, -0.18069699575979192, -0.17849165618474383, -0.17628307862096704, -0.17407126226692915, -0.17185620632187593, -0.1696379099858285, -0.16741637245958252, -0.16519159294470676, -0.16296357064353995, -0.16073230475919054, -0.15849779449553458, -0.1562600390572132, -0.15401903764963332, -0.15177478947896283, -0.14952729375213147, -0.14727654967682768, -0.14502255646149775, -0.14276531331534437, -0.14050481944832416, -0.1382410740711466, -0.13597407639527215, -0.1337038256329106, -0.13143032099702057, -0.12915356170130643, -0.12687354696021702, -0.12459027598894457, -0.1223037480034227, -0.12001396222032579, -0.11772091785706562, -0.11542461413179117, -0.11312505026338693, -0.11082222547147058, -0.10851613897639256, -0.10620678999923383, -0.10389417776180421, -0.10157830148664093, -0.0992591603970071, -0.09693675371689137, -0.09461108067100421, -0.09228214048477812, -0.0899499323843651, -0.08761445559663572, -0.08527570934917794, -0.08293369287029473, -0.08058840538900292, -0.07823984613503177, -0.07588801433882186, -0.073532909231523, -0.07117453004499297, -0.0688128760117962, -0.0664479463652018, -0.06407974033918362, -0.061708257168416436, -0.059333496088276094, -0.05695545633483823, -0.05457413714487547, -0.05218953775585755, -0.049801657405948996, -0.04741049533400754, -0.04501605077958348, -0.042618322982917234, -0.04021731118493921, -0.03781301462726738, -0.03540543255220596, -0.0329945642027446, -0.030580408822556082, -0.028162965655996186, -0.02574223394810149, -0.023318212944587358, -0.020890901891848168, -0.018460300036954203, -0.016026406627652312, -0.013589220912362476, -0.011148742140177248, -0.008704969560861309, -0.006257902424848361, -0.003807539983241459, -0.001353881487810904, 0.0011030738090076486, 0.003563326654113519, 0.006026877793743113, 0.008493727973470033, 0.010963877938207967, 0.01343732843221157, 0.015914080199076697, 0.0183941339817435, 0.020877490522495323, 0.023364150562962926, 0.025854114844123366, 0.028347384106302886, 0.030843959089177142, 0.03334384053177253, 0.035847029172468625, 0.038353525748998196, 0.04086333099844852, 0.04337644565726473, 0.045892870461247126, 0.048412606145556536, 0.050935653444712514, 0.05346201309259713, 0.055991685822454285, 0.058524672366891295, 0.06106097345788064, 0.06360058982676153, 0.06614352220424036, 0.068689771320392, 0.07123933790466164, 0.07379222268586494, 0.07634842639219053, 0.07890794975120086, 0.08147079348983244, 0.08403695833439828, 0.08660644501058834, 0.08917925424347128, 0.09175538675749517, 0.094334843276489, 0.09691762452366359, 0.09950373122161316, 0.10209316409231661, 0.10468592385713782, 0.10728201123682735, 0.10988142695152403, 0.11248417172075587, 0.11509024626344089, 0.11769965129788895, 0.12031238754180174, 0.12292845571227518, 0.1255478565257997, 0.12817059069826242, 0.1307966589449474, 0.13342606198053675, 0.13605880051911168, 0.13869487527415525, 0.141334286958551, 0.1439770362845867, 0.14662312396395216, 0.14927255070774414, 0.15192531722646474, 0.15458142423002363, 0.157240872427739, 0.15990366252833854, 0.1625697952399603, 0.16523927127015448, 0.16791209132588447, 0.17058825611352746, 0.17326776633887508, 0.17595062270713546, 0.17863682592293495, 0.1813263766903166, 0.1840192757127448, 0.18671552369310196, 0.18941512133369454, 0.1921180693362503, 0.19482436840192086, 0.19753401923128333, 0.20024702252433935, 0.20296337898051853, 0.2056830892986783, 0.20840615417710495, 0.21113257431351506, 0.21386235040505563, 0.21659548314830634, 0.21933197323928044, 0.22207182137342518, 0.22481502824562272, 0.22756159455019143, 0.2303115209808877, 0.23306480823090614, 0.23582145699287982, 0.23858146795888424, 0.2413448418204338, 0.24411157926848737, 0.2468816809934462, 0.2496551476851565, 0.2524319800329098, 0.2552121787254438, 0.2579957444509442, 0.26078267789704523, 0.2635729797508304, 0.2663666506988329, 0.2691636914270388]}], "layout": {"font": {"color": "black", "family": "Times New Roman, serif", "size": 21}, "height": 600, "legend": {"bgcolor": "rgba(255,255,255,0.8)", "bordercolor": "black", "borderwidth": 1, "font": {"color": "black", "family": "Times New Roman, serif", "size": 19}}, "margin": {"b": 80, "l": 80, "r": 80, "t": 80}, "paper_bgcolor": "white", "plot_bgcolor": "white", "showlegend": true, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"font": {"color": "black", "family": "Times New Roman, serif", "size": 24}, "text": ""}, "width": 800, "xaxis": {"gridcolor": "lightgray", "gridwidth": 1, "linecolor": "black", "linewidth": 2, "mirror": true, "showgrid": true, "showline": true, "tickcolor": "black", "tickfont": {"color": "black", "family": "Times New Roman, serif", "size": 19}, "ticks": "outside", "tickwidth": 2, "title": {"font": {"color": "black", "family": "Times New Roman, serif", "size": 22}, "text": "x (Cr Substitution Ratio)"}, "zeroline": true, "zerolinecolor": "black", "zerolinewidth": 2}, "yaxis": {"gridcolor": "lightgray", "gridwidth": 1, "linecolor": "black", "linewidth": 2, "mirror": true, "showgrid": true, "showline": true, "tickcolor": "black", "tickfont": {"color": "black", "family": "Times New Roman, serif", "size": 19}, "ticks": "outside", "tickwidth": 2, "title": {"font": {"color": "black", "family": "Times New Roman, serif", "size": 22}, "text": "E<sub>D</sub> (eV)"}, "zeroline": true, "zerolinecolor": "black", "zerolinewidth": 2}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["if fit_successful:\n", "    # Generate smooth curve for plotting\n", "    x_smooth = np.linspace(0, 0.5, 1000)\n", "    y_smooth = power_law(x_smooth, a_fit, b_fit, c_fit)\n", "    \n", "    # Create the plot\n", "    fig = go.Figure()\n", "    \n", "    # Add experimental data points\n", "    fig.add_trace(go.<PERSON>(\n", "        x=x_data,\n", "        y=E_D_data,\n", "        mode='markers',\n", "        marker=dict(\n", "            size=12,\n", "            color='red',\n", "            symbol='circle',\n", "            line=dict(width=2, color='black')\n", "        ),\n", "        name='Measured Dirac Point Energy Levels',\n", "        showlegend=True\n", "    ))\n", "    \n", "    # Add fitted curve\n", "    fig.add_trace(go.<PERSON>(\n", "        x=x_smooth,\n", "        y=y_smooth,\n", "        mode='lines',\n", "        line=dict(\n", "            width=3,\n", "            color='blue'\n", "        ),\n", "        name=f'Power Law Fit<br>E<sub>D</sub> = {a_fit:.3f}x<sup>{b_fit:.3f}</sup> + {c_fit:.3f}<br>R² = {r2:.4f}',\n", "        showlegend=True\n", "    ))\n", "    \n", "    # Apply professional styling\n", "    labels = create_latex_labels()\n", "    layout_style = create_professional_layout()\n", "    title_style = create_professional_title_style()\n", "    axis_style = create_professional_axis_style()\n", "    \n", "    # Update layout\n", "    fig.update_layout(\n", "        title=dict(\n", "            text='',\n", "            **title_style\n", "        ),\n", "        xaxis_title=labels['x_cr'],\n", "        yaxis_title=labels['E_D'],\n", "        width=800,\n", "        height=600,\n", "        **layout_style\n", "    )\n", "    \n", "    # Apply axis styling\n", "    fig.update_xaxes(**axis_style)\n", "    fig.update_yaxes(**axis_style)\n", "    \n", "    # Show the plot\n", "    fig.show()\n", "    \n", "else:\n", "    print(\"Cannot create plot due to fitting failure.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary\n", "\n", "This analysis demonstrates the relationship between Cr substitution ratio and Dirac Point Energy Level using a power law model. The professional styling matches the ARPES analysis GUI standards with Times New Roman fonts, proper borders, and LaTeX-style labels."]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.19"}}, "nbformat": 4, "nbformat_minor": 4}