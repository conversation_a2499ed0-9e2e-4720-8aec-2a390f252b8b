#!/usr/bin/env python3
"""
Enhanced ARPES Analysis GUI - Standalone Window Application
A complete GUI application for ARPES data analysis with advanced Plotly visualization.
Includes all functionality from ThesisAnalysis.ipynb with live plot updates.

Features:
- Interactive Plotly plots with zoom, pan, hover
- Multiple plot modes: E vs kx, kx vs ky, kx vs kz
- Advanced analysis: peak detection, edge detection, Euler characteristic, critical points
- Live plot updates when parameters change
- Canny edge detection with customizable parameters
- Component analysis and labeling
- Custom colorscales including Igor-style rainbow
- Export functionality

Optimized for pyarpesenv anaconda environment.
"""

import os
import sys
import warnings
import time
import json
import hashlib
import numpy as np
import pandas as pd
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from tkinter.ttk import Progressbar
import threading
import webbrowser
import tempfile
from pathlib import Path

# Suppress warnings for cleaner output
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=FutureWarning)

# Handle numpy complex deprecation
if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Try to import required packages with better error handling
missing_packages = []

try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots
except ImportError as e:
    missing_packages.append(f"plotly: {e}")

try:
    from scipy.interpolate import griddata
    from scipy.ndimage import gaussian_filter, maximum_filter
    from scipy.spatial import cKDTree
except ImportError as e:
    missing_packages.append(f"scipy: {e}")

try:
    from skimage.feature import canny
    from skimage.measure import label, euler_number
except ImportError as e:
    missing_packages.append(f"scikit-image: {e}")

try:
    from arpes.load_pxt import read_single_pxt
except ImportError as e:
    missing_packages.append(f"arpes: {e}")
    print("⚠️  Warning: ARPES package not found. Make sure you're in the pyarpesenv environment.")
    print("   Activate with: conda activate pyarpesenv")

try:
    import tkinter.html as tkhtml
    HAS_TKHTML = True
except ImportError:
    try:
        import tkinterhtml as tkhtml
        HAS_TKHTML = True
    except ImportError:
        HAS_TKHTML = False

try:
    import webview
    HAS_WEBVIEW = True
except ImportError:
    HAS_WEBVIEW = False

if missing_packages:
    print("❌ Missing required packages:")
    for pkg in missing_packages:
        print(f"   - {pkg}")
    print("\n📋 To fix this in pyarpesenv environment:")
    print("   conda activate pyarpesenv")
    print("   conda install plotly pandas numpy scipy scikit-image")
    print("   # or use pip if conda doesn't have the package:")
    print("   pip install plotly")

    # Don't exit immediately - let user see the error in GUI
    messagebox.showerror("Missing Dependencies",
                        f"Missing packages: {', '.join([p.split(':')[0] for p in missing_packages])}\n\n"
                        "Please install them in pyarpesenv environment:\n"
                        "conda activate pyarpesenv\n"
                        "conda install plotly pandas numpy scipy scikit-image")
    sys.exit(1)

# Constants
WORK_FUNCTION = 4.5  # eV
V0 = 10  # Inner potential in eV

# Custom colormap data (truncated for brevity - you can add the full array)
igor_data = np.array([
    [57600, 54784, 58112],
    [56561.95, 53415.66, 57121.13],
    [55523.89, 52047.31, 56130.26],
    [54485.84, 50678.96, 55139.39],
    [53447.78, 49310.62, 54148.52],
    [52409.73, 47942.27, 53157.65],
    [51371.67, 46655.25, 52193.88],
    [50333.62, 45428.45, 51250.2],
    [49295.56, 44201.66, 50306.51],
    [48257.51, 42974.87, 49362.82],
    [47219.45, 41748.08, 48419.14],
    [46223.56, 40563.45, 47510.59],
    [45468.61, 39619.77, 46802.82],
    [44713.66, 38676.08, 46095.06],
    [43958.71, 37732.39, 45387.29],
    [43203.77, 36788.71, 44679.53],
    [42448.82, 35845.02, 43971.77],
    [41693.87, 34833.07, 43195.73],
    [40938.92, 33795.01, 42393.6],
    [40183.97, 32756.96, 41591.46],
    [39429.02, 31718.9, 40789.33],
    [38674.07, 30680.85, 39987.2],
    [37919.12, 29670.9, 39171.01],
    [37164.17, 28727.21, 38321.7],
    [36409.22, 27783.53, 37472.38],
    [35654.27, 26839.84, 36623.06],
    [34899.32, 25896.16, 35773.74],
    [34144.38, 24952.47, 34924.42],
    [33512.91, 24173.43, 34239.75],
    [32899.52, 23418.48, 33579.17],
    [32286.12, 22663.53, 32918.59],
    [31672.72, 21908.58, 32258.01],
    [31059.33, 21153.63, 31597.43],
    [30467.01, 20419.77, 30957.93],
    [29900.8, 19712, 30344.53],
    [29334.59, 19004.23, 29731.14],
    [28768.38, 18296.47, 29117.74],
    [28202.16, 17588.71, 28504.35],
    [27641.98, 16886.96, 27901.99],
    [27358.87, 16462.31, 27807.62],
    [27075.77, 16037.65, 27713.26],
    [26792.66, 15612.99, 27618.89],
    [26509.55, 15188.33, 27524.52],
    [26226.45, 14763.67, 27430.15],
    [26027.67, 14479.56, 27448.22],
    [25886.12, 14290.82, 27542.59],
    [25744.56, 14102.09, 27636.96],
    [25603.01, 13913.35, 27731.33],
    [25461.46, 13724.61, 27825.69],
    [25279.75, 13503.75, 27944.16],
    [24902.28, 13126.27, 28180.08],
    [24524.8, 12748.8, 28416],
    [24147.33, 12371.33, 28651.92],
    [23769.85, 11993.85, 28887.84],
    [23392.38, 11616.38, 29123.77],
    [22874.35, 11168.63, 29359.69],
    [22308.14, 10696.78, 29595.61],
    [21741.93, 10224.94, 29831.53],
    [21175.72, 9753.098, 30067.45],
    [20609.51, 9281.255, 30303.37],
    [19952.94, 8899.765, 30539.29],
    [19103.62, 8711.027, 30775.21],
    [18254.31, 8522.29, 31011.14],
    [17404.99, 8333.553, 31247.06],
    [16555.67, 8144.816, 31482.98],
    [15706.35, 7956.079, 31718.9],
    [14688.38, 7893.835, 31828.33],
    [13650.32, 7846.651, 31922.7],
    [12612.27, 7799.467, 32017.07],
    [11574.21, 7752.282, 32111.44],
    [10536.16, 7705.098, 32205.8],
    [9807.31, 7922.949, 32388.52],
    [9429.835, 8441.977, 32671.62],
    [9052.36, 8961.004, 32954.73],
    [8674.887, 9480.031, 33237.84],
    [8297.412, 9999.059, 33520.94],
    [7911.906, 10526.12, 33812.08],
    [7345.694, 11233.88, 34283.92],
    [6779.482, 11941.65, 34755.77],
    [6213.271, 12649.41, 35227.61],
    [5647.059, 13357.18, 35699.45],
    [5080.847, 14064.94, 36171.29],
    [4543.749, 14714.48, 36614.02],
    [4024.722, 15327.87, 37038.68],
    [3505.694, 15941.27, 37463.34],
    [2986.667, 16554.67, 37888],
    [2467.639, 17168.06, 38312.66],
    [1984.753, 17790.49, 38764.42],
    [1654.463, 18451.07, 39330.64],
    [1324.173, 19111.65, 39896.85],
    [993.8823, 19772.23, 40463.06],
    [663.5922, 20432.82, 41029.27],
    [333.302, 21093.4, 41595.48],
    [256, 21464.85, 41944.85],
    [256, 21747.95, 42227.95],
    [256, 22031.06, 42511.06],
    [256, 22314.16, 42794.16],
    [256, 22597.27, 43077.27],
    [239.9373, 23008.88, 43456.75],
    [192.7529, 23669.46, 44022.96],
    [145.5686, 24330.04, 44589.18],
    [98.38432, 24990.62, 45155.39],
    [51.2, 25651.2, 45721.6],
    [4.015687, 26311.78, 46287.81],
    [0, 26972.36, 46897.19],
    [0, 27632.94, 47510.59],
    [0, 28293.52, 48123.98],
    [0, 28954.1, 48737.38],
    [0, 29614.68, 49350.78],
    [0, 30344.53, 50033.44],
    [0, 31146.67, 50788.39],
    [0, 31948.8, 51543.34],
    [0, 32750.93, 52298.29],
    [0, 33553.07, 53053.24],
    [0, 34358.21, 53805.18],
    [0, 35207.53, 54512.94],
    [0, 36056.85, 55220.71],
    [0, 36906.16, 55928.47],
    [0, 37755.48, 56636.23],
    [0, 38604.8, 57344],
    [0, 39062.59, 57208.47],
    [0, 39298.51, 56595.07],
    [0, 39534.43, 55981.68],
    [0, 39770.35, 55368.28],
    [0, 40006.27, 54754.89],
    [0, 40181.96, 54041.1],
    [0, 40134.78, 52955.86],
    [0, 40087.59, 51870.62],
    [0, 40040.41, 50785.38],
    [0, 39993.22, 49700.14],
    [0, 39946.04, 48614.9],
    [0, 39936, 47641.1],
    [0, 39936, 46697.41],
    [0, 39936, 45753.73],
    [0, 39936, 44810.04],
    [0, 39936, 43866.35],
    [0, 39918.93, 42854.4],
    [0, 39871.75, 41721.98],
    [0, 39824.57, 40589.55],
    [0, 39777.38, 39457.13],
    [0, 39730.2, 38324.71],
    [0, 39683.01, 37192.28],
    [0, 39680, 36369.07],
    [0, 39680, 35566.93],
    [0, 39680, 34764.8],
    [0, 39680, 33962.67],
    [0, 39680, 33160.54],
    [0, 39680, 32527.06],
    [0, 39680, 32055.21],
    [0, 39680, 31583.37],
    [0, 39680, 31111.53],
    [0, 39680, 30639.69],
    [0, 39675.98, 30123.67],
    [0, 39628.8, 29132.8],
    [0, 39581.62, 28141.93],
    [0, 39534.43, 27151.06],
    [0, 39487.25, 26160.19],
    [0, 39440.06, 25169.32],
    [0, 39361.76, 24240.69],
    [0, 39267.39, 23344.19],
    [0, 39173.02, 22447.69],
    [0, 39078.65, 21551.18],
    [0, 38984.28, 20654.68],
    [0, 38923.04, 19835.48],
    [0, 38970.23, 19269.27],
    [0, 39017.41, 18703.06],
    [0, 39064.6, 18136.85],
    [0, 39111.78, 17570.63],
    [0, 39158.96, 17004.42],
    [0, 39435.04, 16781.55],
    [0, 39765.33, 16640],
    [0, 40095.62, 16498.45],
    [0, 40425.91, 16356.89],
    [0, 40756.2, 16215.34],
    [993.8823, 41122.64, 16073.79],
    [3589.02, 41547.29, 15932.24],
    [6184.157, 41971.95, 15790.68],
    [8779.294, 42396.61, 15649.13],
    [11374.43, 42821.27, 15507.58],
    [13969.57, 43245.93, 15366.02],
    [15796.71, 43715.77, 15224.47],
    [17589.71, 44187.61, 15082.92],
    [19382.71, 44659.45, 14941.36],
    [21175.72, 45131.29, 14799.81],
    [22968.72, 45603.14, 14658.26],
    [24686.43, 46100.08, 14516.71],
    [26337.88, 46619.11, 14375.15],
    [27989.33, 47138.13, 14233.6],
    [29640.79, 47657.16, 14092.05],
    [31292.23, 48176.19, 13950.49],
    [32933.65, 48705.25, 13798.9],
    [34490.73, 49318.65, 13562.98],
    [36047.81, 49932.05, 13327.06],
    [37604.89, 50545.44, 13091.14],
    [39161.98, 51158.84, 12855.22],
    [40719.06, 51772.23, 12619.29],
    [41922.76, 52225, 12415.5],
    [42960.82, 52602.48, 12226.76],
    [43998.87, 52979.95, 12038.02],
    [45036.93, 53357.43, 11849.29],
    [46074.98, 53734.9, 11660.55],
    [47293.74, 54196.71, 11411.58],
    [49039.56, 54904.47, 10986.92],
    [50785.38, 55612.23, 10562.26],
    [52531.2, 56320, 10137.6],
    [54277.02, 57027.77, 9712.941],
    [56022.84, 57735.53, 9288.282],
    [57494.59, 58325.84, 8785.317],
    [58910.12, 58892.05, 8266.29],
    [60325.65, 59458.26, 7747.263],
    [61741.18, 60024.47, 7228.235],
    [63156.71, 60590.68, 6709.208],
    [64076.3, 60470.21, 6457.224],
    [64265.04, 59337.79, 6598.776],
    [64453.77, 58205.36, 6740.33],
    [64642.51, 57072.94, 6881.882],
    [64831.25, 55940.52, 7023.435],
    [65019.98, 54808.09, 7164.988],
    [64746.92, 53260.05, 7398.902],
    [64463.81, 51702.96, 7634.824],
    [64180.71, 50145.88, 7870.745],
    [63897.6, 48588.8, 8106.667],
    [63614.49, 47031.72, 8342.588],
    [63592.41, 45605.14, 8474.102],
    [63781.14, 44283.98, 8521.286],
    [63969.88, 42962.82, 8568.471],
    [64158.62, 41641.66, 8615.655],
    [64347.36, 40320.5, 8662.839],
    [64415.62, 38993.32, 8704],
    [63660.68, 37624.97, 8704],
    [62905.73, 36256.63, 8704],
    [62150.78, 34888.28, 8704],
    [61395.83, 33519.94, 8704],
    [60640.88, 32151.59, 8704],
    [60283.48, 30882.63, 8704],
    [60094.75, 29655.84, 8704],
    [59906.01, 28429.05, 8704],
    [59717.27, 27202.26, 8704],
    [59528.54, 25975.47, 8704],
    [59339.8, 24722.57, 8704],
    [59151.06, 23401.41, 8704],
    [58962.32, 22080.25, 8704],
    [58773.59, 20759.09, 8704],
    [58584.85, 19437.93, 8704],
    [58396.11, 18116.77, 8704],
    [58287.69, 17197.18, 8704],
    [58193.32, 16347.86, 8704],
    [58098.95, 15498.54, 8704],
    [58004.58, 14649.22, 8704],
    [57910.21, 13799.91, 8704],
    [57795.77, 12267.92, 8704],
    [57654.21, 9814.337, 8704],
    [57512.66, 7360.753, 8704],
    [57371.11, 4907.168, 8704],
    [57229.55, 2453.584, 8704],
    [57088, 0, 8704]])
# Create the array from the provided data


def create_custom_colorscale(rgb_array):
    """Convert RGB array to Plotly colorscale format"""
    n_colors = len(rgb_array)
    colorscale = []
    for i, rgb in enumerate(rgb_array):
        position = i / (n_colors - 1)
        color = f'rgb({int(rgb[0]/255)}, {int(rgb[1]/255)}, {int(rgb[2]/255)})'
        colorscale.append([position, color])
    return colorscale

# Create colorscales
rainbowlightct = create_custom_colorscale(igor_data)
COLORSCALES = {
    'Custom Rainbow': rainbowlightct,
    'Viridis': 'Viridis',
    'Plasma': 'Plasma',
    'Inferno': 'Inferno',
    'Magma': 'Magma',
    'Cividis': 'Cividis',
    'Turbo': 'Turbo'
}

def create_latex_labels():
    """Create LaTeX-style labels for plots"""
    return {
        'kx': 'k<sub>x</sub> (Å<sup>-1</sup>)',
        'ky': 'k<sub>y</sub> (Å<sup>-1</sup>)',
        'kz': 'k<sub>z</sub> (Å<sup>-1</sup>)',
        'E_binding': 'E<sub>b</sub> (eV)',
        'E_kinetic': 'E<sub>k</sub> (eV)',
        'intensity': 'Intensity (arb. units)',
        'normalized_intensity': 'Normalized Intensity',
        'binding_energy': 'Binding Energy (eV)',
        'kinetic_energy': 'Kinetic Energy (eV)',
        'photon_energy': 'hν (eV)',
        'work_function': 'Φ (eV)',
        'angle': 'θ (°)',
        'polar_angle': 'φ (°)',
        'azimuthal_angle': 'α (°)'
    }

def create_professional_layout():
    """Create professional plot layout settings"""
    return {
        'font': {
            'family': 'Times New Roman, serif',
            'size': 21,  # Increased from 18
            'color': 'black'
        },
        'plot_bgcolor': 'white',
        'paper_bgcolor': 'white',
        'margin': dict(l=80, r=80, t=80, b=80),
        'showlegend': True,
        'legend': {
            'font': {
                'family': 'Times New Roman, serif',
                'size': 19,  # Increased from 16
                'color': 'black'
            },
            'bgcolor': 'rgba(255,255,255,0.8)',
            'bordercolor': 'black',
            'borderwidth': 1
        }
    }

def create_professional_title_style():
    """Create professional title styling"""
    return {
        'font': {
            'family': 'Times New Roman, serif',
            'size': 24,  # Increased from 20
            'color': 'black'
        }
    }

def create_professional_axis_style():
    """Create professional axis styling"""
    return {
        'showline': True,
        'linewidth': 2,
        'linecolor': 'black',
        'mirror': True,
        'ticks': 'outside',
        'tickwidth': 2,
        'tickcolor': 'black',
        'tickfont': {
            'family': 'Times New Roman, serif',
            'size': 19,  # Increased from 16
            'color': 'black'
        },
        'title': {
            'font': {
                'family': 'Times New Roman, serif',
                'size': 22,  # Increased from 18
                'color': 'black'
            }
        },
        'showgrid': True,
        'gridwidth': 1,
        'gridcolor': 'lightgray',
        'zeroline': True,
        'zerolinewidth': 2,
        'zerolinecolor': 'black'
    }

def create_professional_colorbar_style():
    """Create professional colorbar styling"""
    return {
        'thickness': 20,
        'len': 0.8,
        'x': 1.02,
        'xanchor': 'left',
        'title': {
            'font': {
                'family': 'Times New Roman, serif',
                'size': 22,  # Increased from 18
                'color': 'black'
            }
        },
        'tickfont': {
            'family': 'Times New Roman, serif',
            'size': 19,  # Increased from 16
            'color': 'black'
        },
        'outlinewidth': 2,
        'outlinecolor': 'black',
        'borderwidth': 2,
        'bordercolor': 'black'
    }

class PlotWindow:
    """Dedicated plot viewing window"""

    def __init__(self, parent):
        self.parent = parent
        self.window = None
        self.plot_file = None

    def create_window(self):
        """Create the plot window"""
        if self.window is None or not self.window.winfo_exists():
            self.window = tk.Toplevel(self.parent)
            self.window.title("ARPES Plot Viewer")
            self.window.geometry("1200x800")

            # Create main frame
            main_frame = ttk.Frame(self.window)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # Title
            title_label = ttk.Label(main_frame, text="Interactive ARPES Plot",
                                   font=('Times New Roman', 14, 'bold'))
            title_label.pack(pady=(0, 10))

            # Control buttons
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill=tk.X, pady=(0, 10))

            refresh_btn = ttk.Button(button_frame, text="Refresh",
                                   command=self.refresh_plot)
            refresh_btn.pack(side=tk.LEFT, padx=(0, 10))

            browser_btn = ttk.Button(button_frame, text="Open in Browser",
                                   command=self.open_in_browser)
            browser_btn.pack(side=tk.LEFT, padx=(0, 10))

            close_btn = ttk.Button(button_frame, text="Close",
                                 command=self.close_window)
            close_btn.pack(side=tk.RIGHT)

            # Plot display area
            self.plot_frame = ttk.LabelFrame(main_frame, text="Plot Display", padding=10)
            self.plot_frame.pack(fill=tk.BOTH, expand=True)

            # Try different approaches for displaying HTML
            self.setup_plot_display()

            # Handle window closing
            self.window.protocol("WM_DELETE_WINDOW", self.close_window)

        return self.window

    def setup_plot_display(self):
        """Setup the plot display area"""
        # Create a text widget to show plot info
        self.info_text = tk.Text(self.plot_frame, height=3, wrap=tk.WORD)
        self.info_text.pack(fill=tk.X, pady=(0, 10))
        self.info_text.insert(tk.END, "Plot will be displayed here. Use 'Refresh' to update the plot.\n")
        self.info_text.insert(tk.END, "For full interactivity, use 'Open in Browser' button.\n")
        self.info_text.insert(tk.END, "Plot file location will be shown below when available.")
        self.info_text.config(state=tk.DISABLED)

        # Create a frame for plot preview (we'll add an image preview)
        self.preview_frame = ttk.Frame(self.plot_frame)
        self.preview_frame.pack(fill=tk.BOTH, expand=True)

        # Add a label to show plot file path
        self.file_label = ttk.Label(self.preview_frame, text="No plot file loaded",
                                   foreground="gray")
        self.file_label.pack(pady=10)

        # Add a canvas for plot preview (if we can implement it)
        self.canvas_frame = ttk.Frame(self.preview_frame)
        self.canvas_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        # Add instructions
        instructions = ttk.Label(self.preview_frame,
                               text="Instructions:\n" +
                                    "• Generate a plot in the main window\n" +
                                    "• Plot info will appear above\n" +
                                    "• Use 'Open in Browser' for full interactivity\n" +
                                    "• Enable 'Live Plot Updates' for automatic refresh",
                               justify=tk.LEFT, foreground="blue")
        instructions.pack(pady=10)

    def update_plot(self, plot_file_path):
        """Update the plot display"""
        self.plot_file = plot_file_path

        if self.window and self.window.winfo_exists():
            # Automatically update the display
            self.update_plot_display()

    def refresh_plot(self):
        """Refresh the plot display"""
        if self.plot_file and os.path.exists(self.plot_file):
            # Update the display without opening browser
            self.update_plot_display()
        else:
            messagebox.showwarning("No Plot", "No plot file available. Generate a plot first.")

    def update_plot_display(self):
        """Update the plot display in the window"""
        if self.plot_file and os.path.exists(self.plot_file):
            # Read the HTML file and extract some info
            try:
                with open(self.plot_file, 'r') as f:
                    content = f.read()

                # Try to extract plot title from HTML
                plot_title = "ARPES Plot"
                if '<title>' in content:
                    start = content.find('<title>') + 7
                    end = content.find('</title>')
                    if end > start:
                        plot_title = content[start:end]

                # Extract some plot info from the HTML
                plot_info = ""
                if 'E vs kx' in content:
                    plot_info = "📊 Type: Energy vs kx dispersion"
                elif 'kx vs ky' in content:
                    plot_info = "📊 Type: kx vs ky constant energy map"
                elif 'kx vs kz' in content:
                    plot_info = "📊 Type: kx vs kz constant energy map"
                else:
                    plot_info = "📊 Type: ARPES plot"

                # Update info text
                self.info_text.config(state=tk.NORMAL)
                self.info_text.delete(1.0, tk.END)
                self.info_text.insert(tk.END, f"✅ Plot loaded: {os.path.basename(self.plot_file)}\n")
                self.info_text.insert(tk.END, f"{plot_info}\n")
                self.info_text.insert(tk.END, f"📁 Location: {self.plot_file}\n")
                self.info_text.insert(tk.END, f"📊 File size: {len(content)/1024:.1f} KB\n")
                self.info_text.insert(tk.END, "🔄 Plot is ready! Click 'Open in Browser' for full interactivity.")
                self.info_text.config(state=tk.DISABLED)

                # Update file label with plot type
                self.file_label.config(text=f"✅ {plot_info.replace('📊 Type: ', '')}: {os.path.basename(self.plot_file)}")

                # Clear canvas frame and add a simple preview message
                for widget in self.canvas_frame.winfo_children():
                    widget.destroy()

                preview_label = ttk.Label(self.canvas_frame,
                                        text=f"📈 {plot_title}\n\n" +
                                             f"{plot_info}\n\n" +
                                             "Click 'Open in Browser' to view the interactive plot\n" +
                                             "with zoom, pan, and hover capabilities.",
                                        justify=tk.CENTER, foreground="darkblue",
                                        font=('Arial', 10))
                preview_label.pack(expand=True)

            except Exception as e:
                self.info_text.config(state=tk.NORMAL)
                self.info_text.delete(1.0, tk.END)
                self.info_text.insert(tk.END, f"❌ Error reading plot file: {e}")
                self.info_text.config(state=tk.DISABLED)

    def open_in_browser(self):
        """Open plot in browser - try to reuse existing tab"""
        if self.plot_file and os.path.exists(self.plot_file):
            # Try to use a specific browser controller to reuse tabs
            try:
                # Get the default browser
                browser = webbrowser.get()
                # Open with new=2 to try to reuse existing window/tab
                browser.open('file://' + self.plot_file, new=0)  # new=0 tries to reuse existing window
            except:
                # Fallback to regular open
                webbrowser.open('file://' + self.plot_file, new=0)
        else:
            messagebox.showwarning("No Plot", "No plot file available. Generate a plot first.")

    def close_window(self):
        """Close the plot window"""
        if self.window:
            self.window.destroy()
            self.window = None

    def show(self):
        """Show the plot window"""
        window = self.create_window()
        window.lift()
        window.focus_force()
        return window


class ARPES3DAnalyzer:
    """Advanced 3D ARPES analysis functionality - EXACTLY matching ThesisAnalysis2.ipynb"""

    def __init__(self, data_loader, progress_callback=None):
        self.data_loader = data_loader
        self.progress_callback = progress_callback
        self.labels = create_latex_labels()
        self.processed_data = None
        self.log_message = None  # Will be set by parent GUI
        self.critical_points = None  # Store computed critical points
        # Binary surface data for cross sections
        self.binary_intensity = None
        self.kx_grid = None
        self.ky_grid = None
        self.energy_grid = None

    def update_progress(self, value, message=""):
        """Update progress bar if callback is provided"""
        if self.progress_callback:
            self.progress_callback(value, message)

    def process_threshold_and_filter(self, threshold=0.1, window_size=3,
                                   min_periods=1, suppress_at='max'):
        """Process data with threshold filtering and smoothing - EXACTLY matching ThesisAnalysis2.ipynb"""
        if not self.data_loader.data_proc:
            return None, "No data loaded"

        self.update_progress(10, "Converting data to 3D format...")

        # Log transformation parameters
        if hasattr(self, 'log_message'):
            self.log_message("Starting k-space transformation for 3D processing (ThesisAnalysis2.ipynb method)", "INFO")
            self.log_message(f"Using work function: {WORK_FUNCTION} eV", "DEBUG")

        # Convert to E-kx-ky space EXACTLY like ThesisAnalysis2.ipynb
        data_EK = []

        for i, df in enumerate(self.data_loader.data_proc):
            hv = self.data_loader.data_attributes[i]['hv']
            polar = np.deg2rad(self.data_loader.data_attributes[i]['polar'])

            # Log scan details
            if hasattr(self, 'log_message'):
                self.log_message(f"🔄 Processing scan {i+1}/{len(self.data_loader.data_proc)}: hν={hv} eV, polar={np.rad2deg(polar):.1f}°", "DEBUG")

            # Get dimensions
            n_energy = len(df.index)
            n_angles = len(df.columns)

            # Create meshgrids for proper broadcasting - EXACTLY like notebook
            E_binding = df.index.values[:, np.newaxis]  # Shape (n_energy, 1)
            theta = np.deg2rad(df.columns.values)        # Shape (n_angles,)

            # Broadcast to 2D grids
            E_binding_grid = np.tile(E_binding, (1, n_angles))  # Shape (n_energy, n_angles)
            theta_grid = np.tile(theta, (n_energy, 1))          # Shape (n_energy, n_angles)

            # Calculate momentum components - EXACTLY like notebook
            E_kinetic = hv - WORK_FUNCTION - E_binding_grid
            k_mag = 0.5123 * np.sqrt(E_binding_grid)  # NOTE: Uses E_binding_grid as in notebook

            kx = k_mag * np.sin(theta_grid)
            ky = k_mag * np.sin(polar)

            # Create DataFrame with equal-length arrays - EXACTLY like notebook
            scan_data = {
                'E_binding': -E_kinetic.flatten(),  # NOTE: Uses -E_kinetic as in notebook
                'kx': kx.flatten(),
                'ky': ky.flatten(),
                'intensity': df.values.flatten()
            }

            data_EK.append(pd.DataFrame(scan_data))

            # Log scan processing results
            if hasattr(self, 'log_message'):
                self.log_message(f"✅ Scan {i+1}: {len(scan_data['E_binding']):,} data points processed", "DEBUG")

        self.update_progress(30, "Combining all scan data...")

        # Combine all DataFrames - convert to the format expected by the rest of the function
        all_data = []
        for scan_df in data_EK:
            for _, row in scan_df.iterrows():
                all_data.append([row['E_binding'], row['kx'], row['ky'], row['intensity']])

        df = pd.DataFrame(all_data, columns=['binding_energy', 'kx', 'ky', 'intensity'])

        # Log transformation results
        if hasattr(self, 'log_message') and len(df) > 0:
            energy_range = (df['binding_energy'].min(), df['binding_energy'].max())
            kx_range = (df['kx'].min(), df['kx'].max())
            ky_range = (df['ky'].min(), df['ky'].max())

            self.log_message(f"📊 3D transformation complete: {len(df):,} total data points", "INFO")
            self.log_message(f"⚡ Binding energy range: {energy_range[0]:.3f} to {energy_range[1]:.3f} eV", "DEBUG")
            self.log_message(f"🔄 kx range: {kx_range[0]:.3f} to {kx_range[1]:.3f} Å⁻¹", "DEBUG")
            self.log_message(f"🔄 ky range: {ky_range[0]:.3f} to {ky_range[1]:.3f} Å⁻¹", "DEBUG")

            # Verify energy coordinate system
            sample_energies = df['binding_energy'].head(10).values
            self.log_message(f"🔍 Sample binding energies: {sample_energies}", "DEBUG")

            # Check for reasonable k-space values
            nonzero_intensity = df[df['intensity'] > 0]
            if len(nonzero_intensity) > 0:
                avg_kx = nonzero_intensity['kx'].mean()
                avg_ky = nonzero_intensity['ky'].mean()
                self.log_message(f"📈 Average k-values (non-zero intensity): kx={avg_kx:.3f}, ky={avg_ky:.3f} Å⁻¹", "DEBUG")

        # Clean data if stored as lists
        if df.applymap(lambda x: isinstance(x, list)).any().any():
            df = df.applymap(lambda y: y[0] if isinstance(y, list) else y)

        self.update_progress(50, "Applying threshold and normalization...")

        # Normalize and threshold
        df['intensity'] = df.groupby('binding_energy')['intensity'].transform(
            lambda x: x/x.max() if x.max() > 0 else 0)
        df.loc[df['intensity'] < threshold, 'intensity'] = 0

        self.update_progress(70, "Applying smoothing...")

        # Apply processing
        df['intensity'] = df.groupby('binding_energy')['intensity'].transform(
            lambda x: x.rolling(window_size, min_periods=min_periods,
                               center=True).mean().fillna(0))

        # Normalize and threshold again
        df['intensity'] = df.groupby('binding_energy')['intensity'].transform(
            lambda x: x/x.max() if x.max() > 0 else 0)
        df.loc[df['intensity'] < threshold, 'intensity'] = 0

        self.update_progress(90, "Applying energy suppression...")

        # Create a mask for non-zero intensities
        nonzero_mask = df['intensity'] > 0

        # Group by kx, ky coordinates
        coords_group = df[nonzero_mask].groupby(['kx', 'ky'])

        # Find the binding energy to keep for each coordinate
        if suppress_at == 'max':
            energy_to_keep_sub = coords_group['binding_energy'].transform('max')
        elif suppress_at == 'min':
            energy_to_keep_sub = coords_group['binding_energy'].transform('min')
        elif suppress_at == 'none':
            # Return processed data without suppression
            self.processed_data = df.set_index(['binding_energy', 'kx', 'ky'])
            self.update_progress(100, "Processing complete!")
            return self.processed_data, "Data processed successfully"
        else:
            energy_to_keep_sub = coords_group['binding_energy'].transform('min')

        # Reindex the result from the non-zero subset to the full DataFrame index
        energy_to_keep = pd.Series(np.nan, index=df.index)
        energy_to_keep.loc[nonzero_mask] = energy_to_keep_sub

        # Create a boolean mask for points to keep
        keep_mask = (df['binding_energy'] == energy_to_keep) & nonzero_mask

        # Set intensity to 0 for all points except those we want to keep
        df.loc[~keep_mask, 'intensity'] = 0

        # Return processed data
        self.processed_data = df.set_index(['binding_energy', 'kx', 'ky'])
        self.update_progress(100, "Processing complete!")

        return self.processed_data, "Data processed successfully"

    def create_energy_surface_notebook_style(self, filename='processed_data.npy', energy_step=None,
                          grid_size=100, intensity_threshold=0.01,
                          colorscale='Viridis', smoothing_sigma=1.0,
                          surface_intensity_threshold=0.1, single_color_mode=False,
                          surface_color='rgb(70,130,180)',
                          kx_range=None, ky_range=None, energy_range=None,
                          kx_offset=0.0, ky_offset=0.0, energy_offset=0.0,
                          show_yellow_plane=False, yellow_plane_energy=None,
                          show_red_plane=False, red_plane_energy=None):
        """
        Create a 3D surface visualization EXACTLY like ThesisAnalysis2.ipynb
        """
        if hasattr(self, 'log_message') and self.log_message:
            self.log_message("🏔️ Creating 3D surface using ThesisAnalysis2.ipynb method", "INFO")

        self.update_progress(10, "Loading processed data...")

        # Load data from processed_data if available, otherwise use current processed_data
        if self.processed_data is not None:
            data = self.processed_data.reset_index().to_numpy()
        else:
            try:
                data = np.load(filename)
            except:
                return None, "No processed data available"

        df = pd.DataFrame(data, columns=['binding_energy', 'kx', 'ky', 'intensity'])

        # Filter by intensity to remove noise and reduce computational load
        df = df[df['intensity'] > intensity_threshold]

        # Apply range filters if specified
        if kx_range is not None:
            df = df[(df['kx'] >= kx_range[0]) & (df['kx'] <= kx_range[1])]

        if ky_range is not None:
            df = df[(df['ky'] >= ky_range[0]) & (df['ky'] <= ky_range[1])]

        if energy_range is not None:
            df = df[(df['binding_energy'] >= energy_range[0]) & (df['binding_energy'] <= energy_range[1])]

        if hasattr(self, 'log_message') and self.log_message:
            self.log_message(f"📊 Data loaded with {len(df)} points after filtering", "DEBUG")

        # Check if we have enough data after filtering
        if len(df) < 10:
            if hasattr(self, 'log_message') and self.log_message:
                self.log_message(f"❌ Only {len(df)} data points remain after filtering", "ERROR")
                self.log_message("💡 Try adjusting the intensity threshold or range filters", "INFO")
            return None, "Very few data points remain after filtering"

        self.update_progress(30, "Creating energy grid...")

        # Define energy values strictly within the specified range - EXACTLY like notebook
        if energy_step is None:
            energy_values = sorted(df['binding_energy'].unique())
        else:
            if energy_range is not None:
                min_energy, max_energy = energy_range
            else:
                min_energy = df['binding_energy'].min()
                max_energy = df['binding_energy'].max()

            energy_values = np.arange(min_energy, max_energy, energy_step)
            if max_energy - energy_values[-1] > 0.01 * energy_step:
                energy_values = np.append(energy_values, max_energy)

        # Create a common grid for all slices - EXACTLY like notebook
        if kx_range is not None:
            kx_min, kx_max = kx_range
        else:
            kx_min, kx_max = df['kx'].min(), df['kx'].max()

        if ky_range is not None:
            ky_min, ky_max = ky_range
        else:
            ky_min, ky_max = df['ky'].min(), df['ky'].max()

        kx_grid = np.linspace(kx_min, kx_max, grid_size)
        ky_grid = np.linspace(ky_min, ky_max, grid_size)
        KX, KY = np.meshgrid(kx_grid, ky_grid)

        self.update_progress(50, "Building 3D intensity grid...")

        # Initialize 3D arrays for surface - EXACTLY like notebook
        Z = np.zeros((len(energy_values), grid_size, grid_size))
        intensity_grid = np.zeros((len(energy_values), grid_size, grid_size))

        if hasattr(self, 'log_message') and self.log_message:
            self.log_message(f"Processing {len(energy_values)} energy slices in range [{energy_values[0]:.3f}, {energy_values[-1]:.3f}]", "DEBUG")

        # Process each energy slice - EXACTLY like notebook
        for i, energy in enumerate(energy_values):
            if i % 10 == 0 and hasattr(self, 'log_message') and self.log_message:
                self.log_message(f"Processing slice {i}/{len(energy_values)}, energy={energy:.3f}", "DEBUG")

            # Filter data for current energy (or energy bin) - EXACTLY like notebook
            if energy_step is None:
                slice_data = df[df['binding_energy'] == energy]
            else:
                # For last slice, include everything up to max_energy
                if i == len(energy_values) - 1:
                    slice_data = df[(df['binding_energy'] >= energy) &
                                  (df['binding_energy'] <= energy_values[-1])]
                else:
                    slice_data = df[(df['binding_energy'] >= energy) &
                                  (df['binding_energy'] < energy_values[i+1])]

            if len(slice_data) > 3:  # Need at least 3 points for interpolation
                # Interpolate intensity values to the grid - EXACTLY like notebook
                intensity_values = griddata(
                    (slice_data['kx'], slice_data['ky']),
                    slice_data['intensity'],
                    (KX, KY),
                    method='linear',
                    fill_value=0
                )

                # Store the interpolated values - EXACTLY like notebook
                Z[i] = np.full((grid_size, grid_size), energy)
                intensity_grid[i] = intensity_values

        self.update_progress(70, "Applying smoothing...")

        # Apply Gaussian smoothing to intensity grid - EXACTLY like notebook
        smoothed_intensity = gaussian_filter(intensity_grid, sigma=smoothing_sigma)

        # Store binary surface data for cross sections
        binary_intensity = (smoothed_intensity >= surface_intensity_threshold).astype(float)
        self.binary_intensity = binary_intensity
        self.kx_grid = kx_grid
        self.ky_grid = ky_grid
        self.energy_grid = energy_values

        if hasattr(self, 'log_message') and self.log_message:
            self.log_message(f"📊 Stored binary surface data for cross sections: shape {binary_intensity.shape}", "DEBUG")

        self.update_progress(90, "Creating 3D visualization...")

        # Create 3D meshgrids - EXACTLY like notebook
        X_3d = np.repeat(KX[np.newaxis, :, :], len(energy_values), axis=0)
        Y_3d = np.repeat(KY[np.newaxis, :, :], len(energy_values), axis=0)

        # Apply offsets to the coordinate grids for visualization - EXACTLY like notebook
        X_3d_display = X_3d + kx_offset
        Y_3d_display = Y_3d + ky_offset
        Z_display = Z + energy_offset

        # Create figure
        fig = go.Figure()

        # Apply the surface intensity threshold - EXACTLY like notebook
        if hasattr(self, 'log_message') and self.log_message:
            self.log_message(f"Applying surface intensity threshold: {surface_intensity_threshold}", "DEBUG")

        intensity_values = smoothed_intensity.flatten()

        # Only include points that are above the threshold - EXACTLY like notebook
        mask = intensity_values >= surface_intensity_threshold

        if hasattr(self, 'log_message') and self.log_message:
            self.log_message(f"Filtered surface contains {np.sum(mask)} points out of {len(mask)}", "INFO")

        # Create either a colored surface or single-color surface based on the toggle - EXACTLY like notebook
        if single_color_mode:
            if hasattr(self, 'log_message') and self.log_message:
                self.log_message("Creating single-color surface visualization...", "DEBUG")

            # For single color mode, we create a binary mask for the isosurface - EXACTLY like notebook
            binary_values = np.zeros_like(smoothed_intensity.flatten())
            binary_values[mask] = 1.0

            fig.add_trace(go.Isosurface(
                x=X_3d_display.flatten(),  # Use offset-adjusted coordinates
                y=Y_3d_display.flatten(),  # Use offset-adjusted coordinates
                z=Z_display.flatten(),     # Use offset-adjusted coordinates
                value=binary_values,
                isomin=0.5,  # Binary threshold
                isomax=1.0,
                surface_count=1,  # Only one surface for binary data
                opacity=0.9,
                colorscale=[[0, 'rgba(0,0,0,0)'], [1, surface_color]],  # Single color for surface
                caps=dict(x_show=False, y_show=False, z_show=False),
                showscale=False  # Hide colorbar
            ))
        else:
            if hasattr(self, 'log_message') and self.log_message:
                self.log_message("Creating intensity-colored surface visualization...", "DEBUG")

            # If we want a colored surface based on intensity - EXACTLY like notebook
            fig.add_trace(go.Isosurface(
                x=X_3d_display.flatten(),  # Use offset-adjusted coordinates
                y=Y_3d_display.flatten(),  # Use offset-adjusted coordinates
                z=Z_display.flatten(),     # Use offset-adjusted coordinates
                value=intensity_values,
                isomin=surface_intensity_threshold,
                isomax=np.max(intensity_values) * 0.9, # Top 10% for high intensity regions
                surface_count=20,
                opacity=0.99999999,
                colorscale=colorscale,
                caps=dict(x_show=False, y_show=False, z_show=False)
            ))

        # Add reference planes if requested
        if show_yellow_plane and yellow_plane_energy is not None:
            self._add_reference_plane_to_fig(fig, yellow_plane_energy + energy_offset,
                                    (kx_min + kx_offset, kx_max + kx_offset),
                                    (ky_min + ky_offset, ky_max + ky_offset),
                                    'yellow', 0.3)

        if show_red_plane and red_plane_energy is not None:
            self._add_reference_plane_to_fig(fig, red_plane_energy + energy_offset,
                                    (kx_min + kx_offset, kx_max + kx_offset),
                                    (ky_min + ky_offset, ky_max + ky_offset),
                                    'red', 0.3)

        # Update layout with range information
        labels = create_latex_labels()
        kx_label = f"kx: [{kx_min:.2f} to {kx_max:.2f}]"
        ky_label = f"ky: [{ky_min:.2f} to {ky_max:.2f}]"
        energy_label = f"E: [{energy_values[0]:.2f} to {energy_values[-1]:.2f}]"
        range_title = f"{kx_label}, {ky_label}, {energy_label}"

        layout_style = create_professional_layout()
        title_style = create_professional_title_style()

        fig.update_layout(
            title=dict(
                text=f"ARPES Data 3D Surface (ThesisAnalysis2.ipynb method)<br><sub>{range_title}</sub>",
                **title_style
            ),
            scene=dict(
                xaxis_title=labels['kx'],
                yaxis_title=labels['ky'],
                zaxis_title=labels['E_binding'],
                aspectratio=dict(x=1, y=1, z=1),  # Equal scale for all axes
                # Explicitly set axis ranges to match our constraints with offsets
                xaxis_range=[kx_min + kx_offset, kx_max + kx_offset],
                yaxis_range=[ky_min + ky_offset, ky_max + ky_offset],
                zaxis_range=[energy_values[0] + energy_offset, energy_values[-1] + energy_offset],
                xaxis=dict(
                    tickfont=dict(family='Times New Roman, serif', size=14, color='black'),  # Slightly larger than original 12
                    title_font=dict(family='Times New Roman, serif', size=16, color='black'),  # Slightly larger than original 14
                    showline=True,
                    linewidth=2,
                    linecolor='black',
                    mirror=True,
                    ticks='outside',
                    tickwidth=2,
                    tickcolor='black',
                    showgrid=True,
                    gridwidth=1,
                    gridcolor='lightgray'
                ),
                yaxis=dict(
                    tickfont=dict(family='Times New Roman, serif', size=14, color='black'),  # Slightly larger than original 12
                    title_font=dict(family='Times New Roman, serif', size=16, color='black'),  # Slightly larger than original 14
                    showline=True,
                    linewidth=2,
                    linecolor='black',
                    mirror=True,
                    ticks='outside',
                    tickwidth=2,
                    tickcolor='black',
                    showgrid=True,
                    gridwidth=1,
                    gridcolor='lightgray'
                ),
                zaxis=dict(
                    tickfont=dict(family='Times New Roman, serif', size=14, color='black'),  # Slightly larger than original 12
                    title_font=dict(family='Times New Roman, serif', size=16, color='black'),  # Slightly larger than original 14
                    showline=True,
                    linewidth=2,
                    linecolor='black',
                    mirror=True,
                    ticks='outside',
                    tickwidth=2,
                    tickcolor='black',
                    showgrid=True,
                    gridwidth=1,
                    gridcolor='lightgray'
                )
            ),
            width=900,
            height=700,
            **layout_style
        )

        return fig, "3D surface created successfully using ThesisAnalysis2.ipynb method"

    def save_binary_surface(self, filename):
        """Save binary surface data to file for use in cross sections"""
        if self.binary_intensity is None:
            return False, "No binary surface data available to save"

        try:
            # Create a dictionary with all the necessary data (avoid nested dicts for pickle issues)
            surface_data = {
                'binary_intensity': self.binary_intensity,
                'kx_grid': self.kx_grid,
                'ky_grid': self.ky_grid,
                'energy_grid': self.energy_grid,
                # Store metadata as separate arrays to avoid pickle issues
                'shape': np.array(self.binary_intensity.shape),
                'kx_range': np.array([self.kx_grid[0], self.kx_grid[-1]]),
                'ky_range': np.array([self.ky_grid[0], self.ky_grid[-1]]),
                'energy_range': np.array([self.energy_grid[0], self.energy_grid[-1]]),
                'created_timestamp': np.array([time.time()])
            }

            # Save using numpy's compressed format
            np.savez_compressed(filename, **surface_data)

            if hasattr(self, 'log_message') and self.log_message:
                self.log_message(f"💾 Binary surface saved to: {filename}", "SUCCESS")
                self.log_message(f"   Shape: {self.binary_intensity.shape}", "DEBUG")
                self.log_message(f"   kx range: [{self.kx_grid[0]:.3f}, {self.kx_grid[-1]:.3f}] Å⁻¹", "DEBUG")
                self.log_message(f"   ky range: [{self.ky_grid[0]:.3f}, {self.ky_grid[-1]:.3f}] Å⁻¹", "DEBUG")
                self.log_message(f"   Energy range: [{self.energy_grid[0]:.3f}, {self.energy_grid[-1]:.3f}] eV", "DEBUG")

            return True, f"Binary surface saved successfully to {filename}"

        except Exception as e:
            if hasattr(self, 'log_message') and self.log_message:
                self.log_message(f"❌ Error saving binary surface: {str(e)}", "ERROR")
            return False, f"Error saving binary surface: {str(e)}"

    def load_binary_surface(self, filename):
        """Load binary surface data from file for use in cross sections"""
        try:
            # Load the data with allow_pickle=False for security
            data = np.load(filename, allow_pickle=False)

            # Extract the arrays
            self.binary_intensity = data['binary_intensity']
            self.kx_grid = data['kx_grid']
            self.ky_grid = data['ky_grid']
            self.energy_grid = data['energy_grid']

            # Extract metadata if available (stored as separate arrays)
            created_time = None
            if 'created_timestamp' in data:
                created_time = data['created_timestamp'][0]  # Extract scalar from array

            if hasattr(self, 'log_message') and self.log_message:
                self.log_message(f"📂 Binary surface loaded from: {filename}", "SUCCESS")
                self.log_message(f"   Shape: {self.binary_intensity.shape}", "DEBUG")
                self.log_message(f"   kx range: [{self.kx_grid[0]:.3f}, {self.kx_grid[-1]:.3f}] Å⁻¹", "DEBUG")
                self.log_message(f"   ky range: [{self.ky_grid[0]:.3f}, {self.ky_grid[-1]:.3f}] Å⁻¹", "DEBUG")
                self.log_message(f"   Energy range: [{self.energy_grid[0]:.3f}, {self.energy_grid[-1]:.3f}] eV", "DEBUG")

                if created_time:
                    import datetime
                    created_str = datetime.datetime.fromtimestamp(created_time).strftime('%Y-%m-%d %H:%M:%S')
                    self.log_message(f"   Created: {created_str}", "DEBUG")

            return True, f"Binary surface loaded successfully from {filename}"

        except Exception as e:
            if hasattr(self, 'log_message') and self.log_message:
                self.log_message(f"❌ Error loading binary surface: {str(e)}", "ERROR")
            return False, f"Error loading binary surface: {str(e)}"

    def compute_critical_points_on_energy_surface(self, filename='processed_data.npy',
                                                 grid_size=100, intensity_threshold=0.01,
                                                 smoothing_sigma=2.0, kx_range=None, ky_range=None,
                                                 energy_range=None, gradient_threshold=0.001,
                                                 min_separation=0.05, edge_buffer=3,
                                                 surface_intensity_threshold=0.1):
        """
        Compute critical points on the 3D energy surface using numerical analysis
        """
        if hasattr(self, 'log_message') and self.log_message:
            self.log_message("🔍 Computing critical points on 3D energy surface", "INFO")

        self.update_progress(10, "Loading processed data for critical point analysis...")

        # Load data from processed_data if available, otherwise use current processed_data
        # Note: This method is called from the GUI, so we need to access the data correctly
        data = None

        # Try multiple ways to get the data
        if hasattr(self, 'processed_data') and self.processed_data is not None:
            try:
                # Debug: Check the structure of processed_data
                if hasattr(self, 'log_message') and self.log_message:
                    self.log_message(f"🔍 Debug processed_data structure:", "DEBUG")
                    self.log_message(f"   Type: {type(self.processed_data)}", "DEBUG")
                    self.log_message(f"   Shape: {self.processed_data.shape}", "DEBUG")
                    self.log_message(f"   Index names: {self.processed_data.index.names}", "DEBUG")
                    self.log_message(f"   Column names: {list(self.processed_data.columns)}", "DEBUG")

                # Reset index to get all columns as regular columns
                data_df = self.processed_data.reset_index()
                if hasattr(self, 'log_message') and self.log_message:
                    self.log_message(f"   After reset_index shape: {data_df.shape}", "DEBUG")
                    self.log_message(f"   After reset_index columns: {list(data_df.columns)}", "DEBUG")

                data = data_df.to_numpy()
                if hasattr(self, 'log_message') and self.log_message:
                    self.log_message(f"📊 Using analyzer's processed_data: {data.shape}", "DEBUG")
            except Exception as e:
                if hasattr(self, 'log_message') and self.log_message:
                    self.log_message(f"⚠️ Error accessing analyzer's processed_data: {e}", "WARNING")

        # If that didn't work, try loading from file
        if data is None:
            try:
                data = np.load(filename)
                if hasattr(self, 'log_message') and self.log_message:
                    self.log_message(f"📊 Loaded data from file: {data.shape}", "DEBUG")
            except Exception as e:
                if hasattr(self, 'log_message') and self.log_message:
                    self.log_message(f"⚠️ Error loading from file: {e}", "WARNING")

        # Final check
        if data is None:
            if hasattr(self, 'log_message') and self.log_message:
                self.log_message(f"❌ No processed data available in analyzer or file", "ERROR")
                self.log_message(f"   Analyzer has processed_data: {hasattr(self, 'processed_data')}", "ERROR")
                if hasattr(self, 'processed_data'):
                    self.log_message(f"   processed_data is None: {self.processed_data is None}", "ERROR")
                    if self.processed_data is not None:
                        self.log_message(f"   processed_data type: {type(self.processed_data)}", "ERROR")
            return None, "No processed data available"

        df = pd.DataFrame(data, columns=['binding_energy', 'kx', 'ky', 'intensity'])

        # Debug: Log initial data statistics
        if hasattr(self, 'log_message') and self.log_message:
            self.log_message(f"📊 Initial data statistics:", "DEBUG")
            self.log_message(f"   Total data points: {len(df):,}", "DEBUG")
            self.log_message(f"   Intensity range: {df['intensity'].min():.6f} to {df['intensity'].max():.6f}", "DEBUG")
            self.log_message(f"   kx range: {df['kx'].min():.3f} to {df['kx'].max():.3f} Å⁻¹", "DEBUG")
            self.log_message(f"   ky range: {df['ky'].min():.3f} to {df['ky'].max():.3f} Å⁻¹", "DEBUG")
            self.log_message(f"   Energy range: {df['binding_energy'].min():.3f} to {df['binding_energy'].max():.3f} eV", "DEBUG")

        # Filter by intensity to remove noise
        df_before_intensity_filter = len(df)
        df = df[df['intensity'] > intensity_threshold]
        df_after_intensity_filter = len(df)

        # Apply range filters if specified
        if kx_range is not None:
            df = df[(df['kx'] >= kx_range[0]) & (df['kx'] <= kx_range[1])]
        if ky_range is not None:
            df = df[(df['ky'] >= ky_range[0]) & (df['ky'] <= ky_range[1])]
        if energy_range is not None:
            df = df[(df['binding_energy'] >= energy_range[0]) & (df['binding_energy'] <= energy_range[1])]

        # Debug: Log filtering results
        if hasattr(self, 'log_message') and self.log_message:
            self.log_message(f"🔍 Data filtering results:", "DEBUG")
            self.log_message(f"   After intensity filter: {df_after_intensity_filter:,} ({100*df_after_intensity_filter/df_before_intensity_filter:.1f}%)", "DEBUG")
            self.log_message(f"   After range filters: {len(df):,} ({100*len(df)/df_before_intensity_filter:.1f}%)", "DEBUG")

        if len(df) < 10:
            if hasattr(self, 'log_message') and self.log_message:
                self.log_message(f"❌ Very few data points remain after filtering: {len(df)}", "ERROR")
                self.log_message(f"   Try lowering intensity threshold or expanding ranges", "ERROR")
            return None, f"Very few data points remain after filtering ({len(df)} points)"

        self.update_progress(30, "Building 3D intensity grid for critical point analysis...")

        # Validate and adjust grid size for critical points analysis
        # Critical points need reasonable grid density - too fine grids fail to interpolate
        max_reasonable_grid_size = 200  # Maximum for critical points analysis
        if grid_size > max_reasonable_grid_size:
            original_grid_size = grid_size
            grid_size = max_reasonable_grid_size
            if hasattr(self, 'log_message') and self.log_message:
                self.log_message(f"⚠️ Grid size reduced for critical points analysis:", "WARNING")
                self.log_message(f"   Original: {original_grid_size} → Adjusted: {grid_size}", "WARNING")
                self.log_message(f"   Reason: Large grids often fail due to insufficient data density per slice", "WARNING")

        # Define grid limits - ensure they don't exceed actual data bounds
        data_kx_min, data_kx_max = df['kx'].min(), df['kx'].max()
        data_ky_min, data_ky_max = df['ky'].min(), df['ky'].max()
        data_energy_min, data_energy_max = df['binding_energy'].min(), df['binding_energy'].max()

        # Use specified ranges but clamp to actual data bounds
        if kx_range is not None:
            kx_min = max(kx_range[0], data_kx_min)
            kx_max = min(kx_range[1], data_kx_max)
        else:
            kx_min, kx_max = data_kx_min, data_kx_max

        if ky_range is not None:
            ky_min = max(ky_range[0], data_ky_min)
            ky_max = min(ky_range[1], data_ky_max)
        else:
            ky_min, ky_max = data_ky_min, data_ky_max

        if energy_range is not None:
            energy_min = max(energy_range[0], data_energy_min)
            energy_max = min(energy_range[1], data_energy_max)
        else:
            energy_min, energy_max = data_energy_min, data_energy_max

        # Log grid parameters
        if hasattr(self, 'log_message') and self.log_message:
            self.log_message(f"🔧 Grid parameters for critical points:", "DEBUG")
            self.log_message(f"   Grid size: {grid_size}x{grid_size}x{grid_size} = {grid_size**3:,} voxels", "DEBUG")
            self.log_message(f"   Data bounds - kx: {data_kx_min:.3f} to {data_kx_max:.3f} Å⁻¹", "DEBUG")
            self.log_message(f"   Data bounds - ky: {data_ky_min:.3f} to {data_ky_max:.3f} Å⁻¹", "DEBUG")
            self.log_message(f"   Data bounds - energy: {data_energy_min:.3f} to {data_energy_max:.3f} eV", "DEBUG")
            self.log_message(f"   Grid range - kx: {kx_min:.3f} to {kx_max:.3f} Å⁻¹", "DEBUG")
            self.log_message(f"   Grid range - ky: {ky_min:.3f} to {ky_max:.3f} Å⁻¹", "DEBUG")
            self.log_message(f"   Grid range - energy: {energy_min:.3f} to {energy_max:.3f} eV", "DEBUG")

        # Create grid arrays
        kx_grid = np.linspace(kx_min, kx_max, grid_size)
        ky_grid = np.linspace(ky_min, ky_max, grid_size)
        energy_grid = np.linspace(energy_max, energy_min, grid_size)  # ARPES convention: high to low energy

        # Debug: Log energy grid details
        if hasattr(self, 'log_message') and self.log_message:
            self.log_message(f"🔧 Energy grid details:", "DEBUG")
            self.log_message(f"   Energy min: {energy_min:.3f} eV", "DEBUG")
            self.log_message(f"   Energy max: {energy_max:.3f} eV", "DEBUG")
            self.log_message(f"   Energy grid first: {energy_grid[0]:.3f} eV", "DEBUG")
            self.log_message(f"   Energy grid last: {energy_grid[-1]:.3f} eV", "DEBUG")

        # Create 3D meshgrids
        KX, KY, E = np.meshgrid(kx_grid, ky_grid, energy_grid, indexing='ij')

        # Build 3D intensity array
        intensity_grid = np.zeros((grid_size, grid_size, grid_size))
        successful_slices = 0
        total_interpolated_points = 0

        # For each energy slice, interpolate intensity - EXACTLY like integrated projections
        for k, energy in enumerate(energy_grid):
            if k % 20 == 0 and hasattr(self, 'log_message') and self.log_message:
                self.log_message(f"Processing slice {k}/{grid_size}, energy={energy:.3f}", "DEBUG")

            # Filter data for current energy bin - EXACTLY like integrated projections
            energy_step = (energy_max - energy_min) / grid_size
            slice_data = df[(df['binding_energy'] >= energy - energy_step/2) &
                          (df['binding_energy'] < energy + energy_step/2)]

            # Debug: Log slice data for first few slices
            if k < 5 and hasattr(self, 'log_message') and self.log_message:
                self.log_message(f"   Slice {k}: energy={energy:.3f}, range=[{energy - energy_step/2:.3f}, {energy + energy_step/2:.3f}], points={len(slice_data)}", "DEBUG")

            if len(slice_data) > 3:
                try:
                    from scipy.interpolate import griddata
                    intensity_values = griddata(
                        (slice_data['kx'], slice_data['ky']),
                        slice_data['intensity'],
                        (KX[:, :, k], KY[:, :, k]),
                        method='linear',
                        fill_value=0
                    )
                    intensity_grid[:, :, k] = np.nan_to_num(intensity_values)
                    successful_slices += 1
                    total_interpolated_points += np.sum(intensity_values > 0)

                    # Debug: Log successful interpolation for first few slices
                    if k < 5 and hasattr(self, 'log_message') and self.log_message:
                        self.log_message(f"   ✅ Slice {k} interpolated: {np.sum(intensity_values > 0)} non-zero points, max={np.max(intensity_values):.6f}", "DEBUG")

                except Exception as e:
                    if k < 5 and hasattr(self, 'log_message') and self.log_message:
                        self.log_message(f"   ❌ Slice {k} interpolation failed: {str(e)}", "DEBUG")

        # Debug: Log intensity grid statistics
        if hasattr(self, 'log_message') and self.log_message:
            grid_min = np.min(intensity_grid)
            grid_max = np.max(intensity_grid)
            grid_mean = np.mean(intensity_grid)
            nonzero_grid_points = np.sum(intensity_grid > 0)

            self.log_message(f"🔍 Intensity grid construction results:", "DEBUG")
            self.log_message(f"   Successful slices: {successful_slices}/{grid_size}", "DEBUG")
            self.log_message(f"   Grid intensity range: {grid_min:.6f} to {grid_max:.6f}", "DEBUG")
            self.log_message(f"   Grid mean intensity: {grid_mean:.6f}", "DEBUG")
            self.log_message(f"   Non-zero grid points: {nonzero_grid_points:,}/{intensity_grid.size:,} ({100*nonzero_grid_points/intensity_grid.size:.1f}%)", "DEBUG")

        self.update_progress(60, "Applying smoothing and computing gradients...")

        # Apply Gaussian smoothing to intensity grid - EXACTLY like ThesisAnalysis2.ipynb
        self.update_progress(70, "Applying 3D smoothing...")
        smoothed_intensity = gaussian_filter(intensity_grid, sigma=smoothing_sigma)

        # Apply binary threshold to create isosurface - EXACTLY like ThesisAnalysis2.ipynb single_color_mode
        self.update_progress(75, "Creating binary thresholded isosurface...")
        if hasattr(self, 'log_message') and self.log_message:
            self.log_message(f"📊 Applying binary threshold {surface_intensity_threshold}...", "DEBUG")

        # This is the exact same binary thresholding as ThesisAnalysis2.ipynb integrated projections
        # Use the same threshold as the projections method for consistency
        binary_intensity = (smoothed_intensity >= surface_intensity_threshold).astype(float)

        # Debug: Analyze the binary surface
        if hasattr(self, 'log_message') and self.log_message:
            intensity_min = np.min(smoothed_intensity)
            intensity_max = np.max(smoothed_intensity)
            intensity_mean = np.mean(smoothed_intensity)
            intensity_std = np.std(smoothed_intensity)
            nonzero_count = np.sum(smoothed_intensity > 0)
            binary_voxels = np.sum(binary_intensity > 0)

            self.log_message(f"🔍 Smoothed intensity grid analysis:", "DEBUG")
            self.log_message(f"   Grid shape: {smoothed_intensity.shape}", "DEBUG")
            self.log_message(f"   Intensity range: {intensity_min:.6f} to {intensity_max:.6f}", "DEBUG")
            self.log_message(f"   Mean intensity: {intensity_mean:.6f} ± {intensity_std:.6f}", "DEBUG")
            self.log_message(f"   Non-zero voxels: {nonzero_count:,}/{smoothed_intensity.size:,} ({100*nonzero_count/smoothed_intensity.size:.1f}%)", "DEBUG")
            self.log_message(f"🔍 Binary isosurface analysis:", "DEBUG")
            self.log_message(f"   Binary threshold: {surface_intensity_threshold:.6f}", "DEBUG")
            self.log_message(f"   Binary surface voxels: {binary_voxels:,}/{binary_intensity.size:,} ({100*binary_voxels/binary_intensity.size:.1f}%)", "DEBUG")

        # Check if we have any surface data
        if np.sum(binary_intensity) == 0:
            if hasattr(self, 'log_message') and self.log_message:
                self.log_message(f"❌ No binary surface voxels found!", "ERROR")
                self.log_message(f"   Max intensity in grid: {intensity_max:.6f}", "ERROR")
                self.log_message(f"   Binary threshold: {surface_intensity_threshold:.6f}", "ERROR")
                self.log_message(f"   Try lowering the surface intensity threshold", "ERROR")
            return None, "No binary surface data found - try lowering surface threshold"

        self.update_progress(80, "Finding critical points using direct surface analysis...")

        # Use direct surface analysis - no interpolation, work directly on binary grid
        if hasattr(self, 'log_message') and self.log_message:
            self.log_message(f"🔍 Using direct surface analysis for critical point detection", "INFO")
            self.log_message(f"   Method: Surface topology analysis on binary grid", "INFO")
            self.log_message(f"   Binary threshold: {surface_intensity_threshold}", "DEBUG")
            self.log_message(f"   Min separation: {min_separation} Å⁻¹", "DEBUG")
            self.log_message(f"   Edge buffer: {edge_buffer} pixels", "DEBUG")

        # Find critical points using energy gradient analysis
        critical_points = self._find_critical_points_energy_gradients(
            binary_intensity, kx_grid, ky_grid, energy_grid,
            gradient_threshold, min_separation, edge_buffer
        )

        if hasattr(self, 'log_message') and self.log_message:
            self.log_message(f"🔍 Found {len(critical_points)} critical points using surface topology", "DEBUG")

        self.update_progress(95, "Finalizing critical points analysis...")

        # Store critical points
        self.critical_points = critical_points

        if hasattr(self, 'log_message') and self.log_message:
            # Count critical points by type
            num_maxima = len([cp for cp in critical_points if cp['type'] == 'maximum'])
            num_minima = len([cp for cp in critical_points if cp['type'] == 'minimum'])
            num_saddles = len([cp for cp in critical_points if cp['type'] == 'saddle'])
            num_critical = len([cp for cp in critical_points if cp['type'] == 'critical_point'])
            num_other = len([cp for cp in critical_points if cp['type'] not in ['maximum', 'minimum', 'saddle', 'critical_point']])

            # Calculate the critical sum (Euler characteristic relation)
            critical_sum = num_minima + num_maxima - num_saddles

            self.log_message(f"🎯 Critical points analysis complete (energy gradient method):", "INFO")
            if num_maxima > 0:
                self.log_message(f"   📈 Maxima: {num_maxima}", "INFO")
            if num_minima > 0:
                self.log_message(f"   📉 Minima: {num_minima}", "INFO")
            if num_saddles > 0:
                self.log_message(f"   🔄 Saddle points: {num_saddles}", "INFO")
            if num_critical > 0:
                self.log_message(f"   🎯 Critical points: {num_critical}", "INFO")
            if num_other > 0:
                self.log_message(f"   ❓ Other features: {num_other}", "INFO")
            self.log_message(f"   📊 Total critical points: {len(critical_points)}", "INFO")
            if critical_sum != 0:
                self.log_message(f"   🧮 Critical sum (minima + maxima - saddles): {critical_sum}", "INFO")

            # Log processing information
            self.log_message(f"   🌊 Processed using energy gradient analysis", "INFO")

            # Log detailed critical point coordinates for verification
            if len(critical_points) > 0:
                self.log_message(f"🔍 CRITICAL POINT COORDINATES:", "INFO")
                self.log_message("=" * 80, "INFO")

                # Group by type for better organization
                cp_types = ['maximum', 'minimum', 'saddle', 'critical_point']

                for cp_type in cp_types:
                    type_points = [cp for cp in critical_points if cp['type'] == cp_type]
                    if type_points:
                        display_name = cp_type.upper().replace('_', ' ')
                        self.log_message(f"📍 {display_name}S ({len(type_points)} found):", "INFO")
                        for i, cp in enumerate(type_points):
                            self.log_message(
                                f"   {i+1:2d}. kx={cp['kx']:7.4f} Å⁻¹, ky={cp['ky']:7.4f} Å⁻¹, "
                                f"E={cp['energy']:7.4f} eV, |∇E|={cp['gradient_magnitude']:.6f}", "INFO"
                            )
                        self.log_message("", "INFO")  # Empty line for readability

                self.log_message("=" * 80, "INFO")

        self.update_progress(100, "Critical points computation complete!")

        return critical_points, f"Found {len(critical_points)} critical points using energy gradient analysis"

    def _find_critical_points_energy_gradients(self, binary_intensity, kx_grid, ky_grid, energy_grid,
                                              gradient_threshold, min_separation, edge_buffer):
        """
        Find critical points where energy gradient with respect to momentum is zero.
        This creates E(kx,ky) surfaces from the binary data and finds ∇E = 0 points.
        """
        critical_points = []
        grid_size = binary_intensity.shape[0]

        if hasattr(self, 'log_message') and self.log_message:
            self.log_message(f"🔍 Finding energy gradient critical points on {grid_size}³ binary grid", "DEBUG")

        # For each energy slice, create an E(kx,ky) surface and find critical points
        self.update_progress(82, "Creating energy surfaces and computing gradients...")

        # Process energy slices to create E(kx,ky) surfaces
        for k in range(edge_buffer, grid_size - edge_buffer, max(1, grid_size // 30)):  # Sample ~30 energy levels
            energy_slice = binary_intensity[:, :, k]

            if np.sum(energy_slice) < 16:  # Need minimum surface area (4x4)
                continue

            # Find surface points at this energy level
            surface_points = np.where(energy_slice > 0.5)

            if len(surface_points[0]) < 16:
                continue

            # Create a local E(kx,ky) surface by treating this energy level as the base
            # and looking at nearby energy variations
            energy_surface = np.full((grid_size, grid_size), np.nan)

            # For each (kx,ky) position in this slice, find the "effective energy"
            # by looking at the energy distribution around this level
            for idx in range(len(surface_points[0])):
                i = surface_points[0][idx]
                j = surface_points[1][idx]

                # Skip boundary points
                if (i <= edge_buffer or i >= grid_size - edge_buffer - 1 or
                    j <= edge_buffer or j >= grid_size - edge_buffer - 1):
                    continue

                # Look at energy profile at this (kx,ky) position
                energy_profile = binary_intensity[i, j, :]

                # Find the "center of mass" of the energy distribution
                energy_indices = np.where(energy_profile > 0.5)[0]
                if len(energy_indices) > 0:
                    # Use weighted average of energy positions
                    weights = energy_profile[energy_indices]
                    if np.sum(weights) > 0:
                        effective_energy_idx = np.average(energy_indices, weights=weights)
                        effective_energy = energy_grid[int(effective_energy_idx)]
                        energy_surface[i, j] = effective_energy

            # Now we have E(kx,ky) surface - find critical points
            valid_mask = ~np.isnan(energy_surface)
            if np.sum(valid_mask) < 9:  # Need at least 3x3 valid region
                continue

            # Fill NaN values with interpolation for gradient calculation
            energy_surface_filled = energy_surface.copy()
            if np.any(~valid_mask):
                # Simple nearest neighbor fill for NaN values
                from scipy.ndimage import distance_transform_edt
                try:
                    # Find nearest valid values
                    indices = distance_transform_edt(~valid_mask, return_distances=False, return_indices=True)
                    energy_surface_filled[~valid_mask] = energy_surface_filled[tuple(indices[:, ~valid_mask])]
                except ImportError:
                    # Fallback: use mean value
                    mean_energy = np.nanmean(energy_surface)
                    energy_surface_filled[~valid_mask] = mean_energy

            # Compute gradients ∇E = (∂E/∂kx, ∂E/∂ky)
            grad_ky, grad_kx = np.gradient(energy_surface_filled)  # Note: np.gradient returns (dy, dx)

            # Convert gradients to physical units (eV/Å⁻¹)
            dkx = kx_grid[1] - kx_grid[0] if len(kx_grid) > 1 else 1.0
            dky = ky_grid[1] - ky_grid[0] if len(ky_grid) > 1 else 1.0
            grad_kx = grad_kx / dkx
            grad_ky = grad_ky / dky

            # Find critical points where |∇E| ≈ 0
            gradient_magnitude = np.sqrt(grad_kx**2 + grad_ky**2)

            # Only consider points that were originally on the surface
            gradient_magnitude[~valid_mask] = np.inf

            # Find local minima in gradient magnitude (critical points)
            critical_mask = gradient_magnitude < gradient_threshold

            # Additional filtering: ensure we're at local minima
            for i in range(1, grid_size - 1):
                for j in range(1, grid_size - 1):
                    if critical_mask[i, j] and valid_mask[i, j]:
                        # Check if this is a local minimum in gradient magnitude
                        local_region = gradient_magnitude[i-1:i+2, j-1:j+2]
                        if gradient_magnitude[i, j] != np.min(local_region):
                            critical_mask[i, j] = False

            # Extract critical points
            critical_indices = np.where(critical_mask)

            for i, j in zip(critical_indices[0], critical_indices[1]):
                # Skip boundary points
                if (i <= edge_buffer or i >= grid_size - edge_buffer - 1 or
                    j <= edge_buffer or j >= grid_size - edge_buffer - 1):
                    continue

                kx_val = kx_grid[i]
                ky_val = ky_grid[j]
                energy_val = energy_surface[i, j]
                grad_mag = gradient_magnitude[i, j]

                # Check minimum separation from existing critical points
                too_close = False
                for existing_cp in critical_points:
                    dist = np.sqrt((kx_val - existing_cp['kx'])**2 + (ky_val - existing_cp['ky'])**2)
                    if dist < min_separation:
                        too_close = True
                        break

                if not too_close:
                    # Compute Hessian matrix for classification
                    hessian = np.zeros((2, 2))

                    # Second derivatives (finite differences)
                    if (i > 0 and i < grid_size - 1 and j > 0 and j < grid_size - 1 and
                        not np.isnan(energy_surface[i-1, j]) and not np.isnan(energy_surface[i+1, j]) and
                        not np.isnan(energy_surface[i, j-1]) and not np.isnan(energy_surface[i, j+1])):

                        hessian[0, 0] = (energy_surface[i+1, j] - 2*energy_surface[i, j] + energy_surface[i-1, j]) / (dkx**2)
                        hessian[1, 1] = (energy_surface[i, j+1] - 2*energy_surface[i, j] + energy_surface[i, j-1]) / (dky**2)

                        # Mixed derivative
                        if (i > 0 and i < grid_size - 1 and j > 0 and j < grid_size - 1 and
                            not np.isnan(energy_surface[i+1, j+1]) and not np.isnan(energy_surface[i+1, j-1]) and
                            not np.isnan(energy_surface[i-1, j+1]) and not np.isnan(energy_surface[i-1, j-1])):

                            hessian[0, 1] = hessian[1, 0] = (
                                energy_surface[i+1, j+1] - energy_surface[i+1, j-1] -
                                energy_surface[i-1, j+1] + energy_surface[i-1, j-1]
                            ) / (4.0 * dkx * dky)

                    # Classify critical point using Hessian eigenvalues
                    try:
                        eigenvalues = np.linalg.eigvals(hessian)
                        hessian_det = np.linalg.det(hessian)
                        hessian_trace = np.trace(hessian)

                        if np.all(eigenvalues > 1e-10):  # Small positive threshold
                            cp_type = 'minimum'
                        elif np.all(eigenvalues < -1e-10):  # Small negative threshold
                            cp_type = 'maximum'
                        else:
                            cp_type = 'saddle'

                    except np.linalg.LinAlgError:
                        cp_type = 'critical_point'
                        hessian_det = 0.0
                        hessian_trace = 0.0
                        eigenvalues = [0.0, 0.0]

                    critical_points.append({
                        'type': cp_type,
                        'kx': kx_val,
                        'ky': ky_val,
                        'energy': energy_val,
                        'gradient_magnitude': grad_mag,
                        'hessian_det': hessian_det,
                        'hessian_trace': hessian_trace,
                        'eigenvalues': eigenvalues.tolist() if hasattr(eigenvalues, 'tolist') else list(eigenvalues)
                    })

        if hasattr(self, 'log_message') and self.log_message:
            self.log_message(f"🔍 Found {len(critical_points)} energy gradient critical points", "DEBUG")

        # Final filtering to remove very close points
        self.update_progress(90, "Filtering critical points...")
        filtered_points = []

        for cp in critical_points:
            too_close = False
            for existing_cp in filtered_points:
                dist = np.sqrt((cp['kx'] - existing_cp['kx'])**2 + (cp['ky'] - existing_cp['ky'])**2)
                if dist < min_separation:
                    too_close = True
                    break

            if not too_close:
                filtered_points.append(cp)

        if hasattr(self, 'log_message') and self.log_message:
            self.log_message(f"🔍 After filtering: {len(filtered_points)} unique critical points", "DEBUG")

        return filtered_points

    def create_integrated_projections_notebook_style(self, filename='processed_data.npy',
                                     grid_size=150,
                                     intensity_threshold=0.01,
                                     smoothing_sigma=1.0,
                                     binary_threshold=0.15,
                                     single_color_mode=False,
                                     kx_range=None,
                                     ky_range=None,
                                     energy_range=None,
                                     kx_offset=0.0,
                                     ky_offset=0.0,
                                     energy_offset=0.0,
                                     colorscale='Viridis',
                                     show_critical_points=False):
        """
        Create integrated projections from processed 3D ARPES surface data - EXACTLY like ThesisAnalysis2.ipynb
        """
        if hasattr(self, 'log_message') and self.log_message:
            self.log_message("📈 Creating integrated projections using ThesisAnalysis2.ipynb method", "INFO")

        self.update_progress(10, "Loading processed data...")

        # Load data from processed_data if available, otherwise use current processed_data
        if self.processed_data is not None:
            data = self.processed_data.reset_index().to_numpy()
        else:
            try:
                data = np.load(filename)
            except:
                return None, "No processed data available"

        df = pd.DataFrame(data, columns=['binding_energy', 'kx', 'ky', 'intensity'])

        # Filter by intensity to remove noise
        df = df[df['intensity'] > intensity_threshold]

        # Apply range filters if specified
        if kx_range is not None:
            df = df[(df['kx'] >= kx_range[0]) & (df['kx'] <= kx_range[1])]

        if ky_range is not None:
            df = df[(df['ky'] >= ky_range[0]) & (df['ky'] <= ky_range[1])]

        if energy_range is not None:
            df = df[(df['binding_energy'] >= energy_range[0]) & (df['binding_energy'] <= energy_range[1])]

        if hasattr(self, 'log_message') and self.log_message:
            self.log_message(f"📊 Data loaded with {len(df)} points after filtering", "DEBUG")

        # Check if we have enough data after filtering
        if len(df) < 10:
            return None, "Very few data points remain after filtering"

        self.update_progress(30, "Creating energy grid...")

        # Define grid limits
        kx_min = kx_range[0] if kx_range is not None else df['kx'].min()
        kx_max = kx_range[1] if kx_range is not None else df['kx'].max()

        ky_min = ky_range[0] if ky_range is not None else df['ky'].min()
        ky_max = ky_range[1] if ky_range is not None else df['ky'].max()

        energy_min = energy_range[0] if energy_range is not None else df['binding_energy'].min()
        energy_max = energy_range[1] if energy_range is not None else df['binding_energy'].max()

        # Create grid arrays
        kx_grid = np.linspace(kx_min, kx_max, grid_size)
        ky_grid = np.linspace(ky_min, ky_max, grid_size)
        energy_grid = np.linspace(energy_max, energy_min, grid_size)

        # Create 3D meshgrids
        KX, KY, E = np.meshgrid(kx_grid, ky_grid, energy_grid, indexing='ij')

        self.update_progress(50, "Building 3D intensity grid...")

        # Build 3D intensity array - this is the key step for continuous surface
        intensity_grid = np.zeros((grid_size, grid_size, grid_size))

        # For each energy slice, interpolate intensity
        for k, energy in enumerate(energy_grid):
            if k % 20 == 0 and hasattr(self, 'log_message') and self.log_message:
                self.log_message(f"Processing slice {k}/{grid_size}, energy={energy:.3f}", "DEBUG")

            # Filter data for current energy bin
            energy_step = (energy_max - energy_min) / grid_size
            slice_data = df[(df['binding_energy'] >= energy - energy_step/2) &
                          (df['binding_energy'] < energy + energy_step/2)]

            if len(slice_data) > 3:
                try:
                    intensity_values = griddata(
                        (slice_data['kx'], slice_data['ky']),
                        slice_data['intensity'],
                        (KX[:, :, k], KY[:, :, k]),
                        method='linear',
                        fill_value=0
                    )
                    intensity_grid[:, :, k] = np.nan_to_num(intensity_values)
                except:
                    pass  # Skip failed interpolations

        self.update_progress(70, "Applying smoothing...")

        # Apply Gaussian smoothing to intensity grid
        smoothed_intensity = gaussian_filter(intensity_grid, sigma=smoothing_sigma)

        self.update_progress(90, "Creating projections...")

        # Apply binary threshold if in single color mode
        if single_color_mode:
            binary_intensity = (smoothed_intensity > binary_threshold).astype(float)
            projection_data = binary_intensity
        else:
            projection_data = smoothed_intensity

        # Create projections by integrating over one dimension
        e_kx_proj = np.sum(projection_data, axis=1)  # Sum over ky (axis 1)
        e_ky_proj = np.sum(projection_data, axis=0)  # Sum over kx (axis 0)
        kx_ky_proj = np.sum(projection_data, axis=2).T  # Sum over energy (axis 2), transpose for correct display

        # Apply offsets for display
        kx_grid_display = kx_grid + kx_offset
        ky_grid_display = ky_grid + ky_offset
        energy_grid_display = energy_grid + energy_offset

        # Create the three projection figures
        projections = {
            'e_kx': (e_kx_proj.T, kx_grid_display, energy_grid_display, 'E vs kx Projection'),
            'e_ky': (e_ky_proj.T, ky_grid_display, energy_grid_display, 'E vs ky Projection'),
            'kx_ky': (kx_ky_proj, kx_grid_display, ky_grid_display, 'kx vs ky Projection')
        }

        # Add critical points data if requested and available
        critical_points_data = None
        if show_critical_points and self.critical_points is not None:
            # Filter critical points to the current ranges
            filtered_critical_points = []
            for cp in self.critical_points:
                # Check if critical point is within the current ranges
                kx_in_range = (kx_range is None or (kx_range[0] <= cp['kx'] <= kx_range[1]))
                ky_in_range = (ky_range is None or (ky_range[0] <= cp['ky'] <= ky_range[1]))
                energy_in_range = (energy_range is None or (energy_range[0] <= cp['energy'] <= energy_range[1]))

                if kx_in_range and ky_in_range and energy_in_range:
                    # Apply offsets to critical point coordinates
                    cp_with_offset = cp.copy()
                    cp_with_offset['kx'] += kx_offset
                    cp_with_offset['ky'] += ky_offset
                    cp_with_offset['energy'] += energy_offset
                    filtered_critical_points.append(cp_with_offset)

            critical_points_data = filtered_critical_points

            if hasattr(self, 'log_message') and self.log_message:
                self.log_message(f"🎯 Including {len(filtered_critical_points)} critical points in projections", "DEBUG")

        # Package projections with critical points data
        projections_with_cp = {
            'projections': projections,
            'critical_points': critical_points_data
        }

        return projections_with_cp, "Integrated projections created successfully using ThesisAnalysis2.ipynb method"

    def _add_reference_plane_to_fig(self, fig, z_value, kx_range, ky_range, color, opacity):
        """Add a reference plane to the 3D plot"""
        x_plane = [kx_range[0], kx_range[1], kx_range[1], kx_range[0]]
        y_plane = [ky_range[0], ky_range[0], ky_range[1], ky_range[1]]
        z_plane = [z_value] * 4

        fig.add_trace(go.Mesh3d(
            x=x_plane + x_plane,  # Duplicate for both sides
            y=y_plane + y_plane,
            z=z_plane + z_plane,
            i=[0, 0, 0, 1],
            j=[1, 2, 3, 2],
            k=[2, 3, 1, 3],
            color=color,
            opacity=opacity,
            name=f'{color.title()} Plane (E={z_value:.2f})'
        ))


class ARPESDataLoader:
    """Data loading and processing class"""

    def __init__(self):
        self.data = None
        self.data_attributes = None
        self.data_proc = None
        self.work_function = WORK_FUNCTION
        self.data_folder = None
        
    def load_pxt_files(self, folder_path=None):
        """Load PXT files from a folder"""
        if folder_path is None:
            folder_path = filedialog.askdirectory(
                title="Select folder containing PXT files"
            )
        
        if not folder_path:
            return False, "No folder selected"

        # Store the data folder path
        self.data_folder = folder_path

        pxt_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
        if not pxt_files:
            return False, "No PXT files found in the selected folder"

        pxt_files.sort()
        data_arrays = []
        attributes = []
        failed_files = []
        
        for i, file in enumerate(pxt_files):
            file_path = os.path.join(folder_path, file)
            try:
                data = read_single_pxt(file_path)
                df = pd.DataFrame(
                    data.values, 
                    columns=data.coords['phi'].values, 
                    index=data.coords['eV'].values
                )
                data_arrays.append(df)
                attributes.append(data.attrs)
            except Exception as e:
                failed_files.append((file, str(e)))

        if not data_arrays:
            return False, "Failed to load any PXT files"

        self.data = data_arrays
        self.data_attributes = attributes
        self._process_data()
        
        success_msg = f"Successfully loaded {len(data_arrays)} PXT files"
        if failed_files:
            success_msg += f"\nFailed to load {len(failed_files)} files"
        
        return True, success_msg
    
    def _process_data(self):
        """Process data to convert to binding energy"""
        if not self.data or not self.data_attributes:
            return
            
        self.data_proc = [df.copy() for df in self.data]
        
        for i in range(len(self.data_proc)):
            hv = self.data_attributes[i]['hv']
            new_index = [hv - self.work_function - abs(idx) for idx in self.data[i].index]
            self.data_proc[i] = self.data_proc[i].set_index(pd.Index(new_index))
            self.data_proc[i].attrs = {'polar': self.data_attributes[i]['polar']}
    
    def get_binding_energy_range(self):
        """Get the full binding energy range across all data"""
        if not self.data_proc:
            return 0, 1
            
        E_binding_values = []
        for i, df in enumerate(self.data_proc):
            hv = self.data_attributes[i]['hv']
            E_kinetic_values = df.index.values.astype(float)
            E_binding_scan = hv - self.work_function - E_kinetic_values
            E_binding_values.extend(E_binding_scan)
        
        E_binding_array = np.array(E_binding_values)
        return E_binding_array.min(), E_binding_array.max()

class ARPESPlotter:
    """Plotting class for ARPES data"""
    
    def __init__(self, data_loader):
        self.data_loader = data_loader
        self.current_fig = None
        
    def moving_average(self, data, kernel_size):
        """Apply moving average filter"""
        if kernel_size <= 1:
            return data
        kernel = np.ones(kernel_size) / kernel_size
        return np.convolve(data, kernel, mode='same')

    def find_peaks_in_intensity(self, intensities, neighborhood_size, threshold, smoothing_sigma):
        """Find peaks in intensity data"""
        intensities_smooth = gaussian_filter(intensities, sigma=smoothing_sigma)
        local_max = maximum_filter(intensities_smooth, size=neighborhood_size) == intensities_smooth
        detected_peaks = (intensities_smooth > threshold) & local_max
        peak_indices = np.argwhere(detected_peaks)
        return peak_indices

    def find_critical_points(self, intensity_grid, sigma=1.0):
        """Find critical points using Hessian analysis"""
        smoothed = gaussian_filter(intensity_grid, sigma=sigma)
        dy, dx = np.gradient(smoothed)
        dyy, dxy = np.gradient(dy)
        dxx, _ = np.gradient(dx)

        hessian_det = dxx * dyy - dxy**2
        maxima = (dxx < 0) & (dyy < 0) & (hessian_det > 0)
        minima = (dxx > 0) & (dyy > 0) & (hessian_det > 0)
        saddles = hessian_det < 0

        return maxima, minima, saddles

    def compute_euler_characteristic(self, intensities, threshold):
        """Compute Euler characteristic from binary intensity data"""
        binary_intensity = intensities >= threshold
        return euler_number(binary_intensity)
        
    def plot_E_vs_kx(self, scan_number, vmin, vmax, kernel_size, x_offset, y_offset,
                     colorscale='Custom Rainbow', show_peaks=False, peak_threshold=0.5,
                     neighborhood_size=5, smoothing_sigma=1.0, show_edges=False,
                     show_components=False, canny_sigma=1.0, canny_low=0.1, canny_high=0.3,
                     euler_threshold=0.5, show_euler_points=False, show_critical_points=False,
                     square_mode=False, hide_titles=False):
        """Plot binding energy vs kx for a single scan"""
        
        if not self.data_loader.data_proc:
            return None, "No data loaded"
            
        if scan_number < 0 or scan_number >= len(self.data_loader.data_proc):
            return None, f"Invalid scan number. Must be between 0 and {len(self.data_loader.data_proc)-1}"

        # Get data for the selected scan
        df = self.data_loader.data_proc[scan_number]
        hv = self.data_loader.data_attributes[scan_number]['hv']

        # Emission angles and kinetic energies
        emission_angles = df.columns.values.astype(float)
        theta_rad = np.deg2rad(emission_angles)
        E_kinetic_values = df.index.values.astype(float)

        # Ensure increasing order
        if np.any(np.diff(E_kinetic_values) < 0):
            E_kinetic_values = E_kinetic_values[::-1]
            df = df.iloc[::-1]

        # Create grids
        theta_grid, E_kinetic_grid = np.meshgrid(theta_rad, E_kinetic_values, indexing='xy')
        E_binding_grid = hv - WORK_FUNCTION - E_kinetic_grid + y_offset
        kx_grid = 0.5123 * np.sqrt(E_kinetic_grid) * np.sin(theta_grid) + x_offset

        # Process intensities
        intensities = df.values
        if kernel_size > 1:
            intensities = np.apply_along_axis(
                lambda m: self.moving_average(m, kernel_size), axis=0, arr=intensities
            )

        # Normalize intensities
        max_intensity = np.nanmax(intensities)
        if max_intensity > 0:
            intensities = intensities / max_intensity

        # Mask invalid data
        valid_mask = np.isfinite(kx_grid) & np.isfinite(E_binding_grid) & np.isfinite(intensities)
        intensities[~valid_mask] = np.nan

        # Create Plotly figure
        fig = go.Figure()

        # Add heatmap
        colorscale_to_use = COLORSCALES.get(colorscale, 'Viridis')
        
        colorbar_style = create_professional_colorbar_style()
        colorbar_style['title'] = "Intensity"

        fig.add_trace(go.Heatmap(
            x=kx_grid[0, :],
            y=E_binding_grid[:, 0],
            z=intensities,
            colorscale=colorscale_to_use,
            zmin=vmin,
            zmax=vmax,
            colorbar=colorbar_style,
            hovertemplate='kx: %{x:.3f} Å⁻¹<br>E_B: %{y:.3f} eV<br>I: %{z:.3f}<extra></extra>'
        ))

        # Prepare intensities for analysis
        intensities_filled = np.nan_to_num(intensities, nan=0.0)

        # Add peaks if requested
        if show_peaks:
            peak_indices = self.find_peaks_in_intensity(
                intensities_filled, neighborhood_size, peak_threshold, smoothing_sigma
            )

            if peak_indices.size > 0:
                peak_kx = [kx_grid[i, j] for i, j in peak_indices]
                peak_E = [E_binding_grid[i, j] for i, j in peak_indices]

                fig.add_trace(go.Scatter(
                    x=peak_kx,
                    y=peak_E,
                    mode='markers',
                    marker=dict(color='red', size=8, symbol='circle'),
                    name='Peaks',
                    hovertemplate='Peak<br>kx: %{x:.3f} Å⁻¹<br>E_B: %{y:.3f} eV<extra></extra>'
                ))

        # Add edge detection if requested
        if show_edges or show_components:
            edges = canny(intensities_filled, sigma=canny_sigma,
                         low_threshold=canny_low, high_threshold=canny_high)

            if show_edges:
                # Find edge coordinates
                edge_coords = np.where(edges)
                if len(edge_coords[0]) > 0:
                    edge_kx = [kx_grid[i, j] for i, j in zip(edge_coords[0], edge_coords[1])]
                    edge_E = [E_binding_grid[i, j] for i, j in zip(edge_coords[0], edge_coords[1])]

                    fig.add_trace(go.Scatter(
                        x=edge_kx,
                        y=edge_E,
                        mode='markers',
                        marker=dict(color='cyan', size=2, symbol='circle'),
                        name='Edges',
                        hovertemplate='Edge<br>kx: %{x:.3f} Å⁻¹<br>E_B: %{y:.3f} eV<extra></extra>'
                    ))

            if show_components:
                labeled_array, num_features = label(edges, connectivity=2, return_num=True)

                for region_label in range(1, min(num_features + 1, 10)):  # Limit to 10 components
                    component_mask = labeled_array == region_label
                    component_coords = np.where(component_mask)

                    if len(component_coords[0]) > 0:
                        comp_kx = [kx_grid[i, j] for i, j in zip(component_coords[0], component_coords[1])]
                        comp_E = [E_binding_grid[i, j] for i, j in zip(component_coords[0], component_coords[1])]

                        fig.add_trace(go.Scatter(
                            x=comp_kx,
                            y=comp_E,
                            mode='markers',
                            marker=dict(color='yellow', size=1, symbol='circle'),
                            name=f'Component {region_label}',
                            hovertemplate=f'Component {region_label}<br>kx: %{{x:.3f}} Å⁻¹<br>E_B: %{{y:.3f}} eV<extra></extra>'
                        ))

        # Add Euler characteristic analysis
        if show_euler_points:
            binary_intensity = intensities_filled >= euler_threshold
            euler_char = self.compute_euler_characteristic(intensities_filled, euler_threshold)

            # Find binary points
            binary_coords = np.where(binary_intensity)
            if len(binary_coords[0]) > 0:
                euler_kx = [kx_grid[i, j] for i, j in zip(binary_coords[0], binary_coords[1])]
                euler_E = [E_binding_grid[i, j] for i, j in zip(binary_coords[0], binary_coords[1])]

                fig.add_trace(go.Scatter(
                    x=euler_kx,
                    y=euler_E,
                    mode='markers',
                    marker=dict(color='white', size=1, symbol='circle',
                               line=dict(color='black', width=0.5)),
                    name=f'Euler Points (χ={euler_char})',
                    hovertemplate=f'Euler Point<br>kx: %{{x:.3f}} Å⁻¹<br>E_B: %{{y:.3f}} eV<br>χ={euler_char}<extra></extra>'
                ))

        # Add critical points if requested
        if show_critical_points:
            maxima, minima, saddles = self.find_critical_points(intensities_filled, sigma=smoothing_sigma)

            # Plot maxima
            max_coords = np.where(maxima)
            if len(max_coords[0]) > 0:
                max_kx = [kx_grid[i, j] for i, j in zip(max_coords[0], max_coords[1])]
                max_E = [E_binding_grid[i, j] for i, j in zip(max_coords[0], max_coords[1])]

                fig.add_trace(go.Scatter(
                    x=max_kx,
                    y=max_E,
                    mode='markers',
                    marker=dict(color='green', size=8, symbol='triangle-up'),
                    name='Maxima',
                    hovertemplate='Maximum<br>kx: %{x:.3f} Å⁻¹<br>E_B: %{y:.3f} eV<extra></extra>'
                ))

            # Plot minima
            min_coords = np.where(minima)
            if len(min_coords[0]) > 0:
                min_kx = [kx_grid[i, j] for i, j in zip(min_coords[0], min_coords[1])]
                min_E = [E_binding_grid[i, j] for i, j in zip(min_coords[0], min_coords[1])]

                fig.add_trace(go.Scatter(
                    x=min_kx,
                    y=min_E,
                    mode='markers',
                    marker=dict(color='blue', size=8, symbol='triangle-down'),
                    name='Minima',
                    hovertemplate='Minimum<br>kx: %{x:.3f} Å⁻¹<br>E_B: %{y:.3f} eV<extra></extra>'
                ))

            # Plot saddles
            saddle_coords = np.where(saddles)
            if len(saddle_coords[0]) > 0:
                saddle_kx = [kx_grid[i, j] for i, j in zip(saddle_coords[0], saddle_coords[1])]
                saddle_E = [E_binding_grid[i, j] for i, j in zip(saddle_coords[0], saddle_coords[1])]

                fig.add_trace(go.Scatter(
                    x=saddle_kx,
                    y=saddle_E,
                    mode='markers',
                    marker=dict(color='orange', size=8, symbol='diamond'),
                    name='Saddles',
                    hovertemplate='Saddle<br>kx: %{x:.3f} Å⁻¹<br>E_B: %{y:.3f} eV<extra></extra>'
                ))

        # Update layout with professional styling
        labels = create_latex_labels()
        layout_style = create_professional_layout()
        title_style = create_professional_title_style()
        axis_style = create_professional_axis_style()
        colorbar_style = create_professional_colorbar_style()

        # Set plot dimensions - square if square mode is enabled
        plot_width = 700 if square_mode else 800
        plot_height = 700 if square_mode else 600

        # Conditionally set title based on hide_titles setting
        layout_dict = {
            'xaxis_title': labels['kx'],
            'yaxis_title': labels['E_binding'],
            'width': plot_width,
            'height': plot_height,
            'yaxis': dict(autorange='reversed'),
            **layout_style
        }

        if not hide_titles:
            layout_dict['title'] = dict(
                text=f'E<sub>b</sub> vs k<sub>x</sub> - Scan {scan_number}',
                **title_style
            )

        fig.update_layout(**layout_dict)

        # Apply professional axis styling
        fig.update_xaxes(**axis_style)
        fig.update_yaxes(**axis_style)

        # Apply square mode if enabled
        if square_mode:
            fig.update_layout(
                xaxis=dict(scaleanchor="y", scaleratio=1),
                yaxis=dict(scaleanchor="x", scaleratio=1)
            )

        # Update colorbar styling for heatmap traces
        for trace in fig.data:
            if hasattr(trace, 'colorbar') and trace.colorbar is not None:
                trace.update(colorbar=colorbar_style)

        self.current_fig = fig
        return fig, "Plot generated successfully"
    
    def plot_kx_vs_ky(self, E_binding, vmin, vmax, kernel_size, x_offset, y_offset,
                      colorscale='Custom Rainbow', use_contours=False, contour_levels=20,
                      show_peaks=False, peak_threshold=0.5, neighborhood_size=5, smoothing_sigma=1.0,
                      show_edges=False, show_components=False, canny_sigma=1.0, canny_low=0.1,
                      canny_high=0.3, euler_threshold=0.5, show_euler_points=False,
                      show_critical_points=False, square_mode=False, hide_titles=False):
        """Plot kx vs ky constant energy map"""
        
        if not self.data_loader.data_proc:
            return None, "No data loaded"

        # Collect data from all scans
        kx_list, ky_list, intensity_list = [], [], []

        for i in range(len(self.data_loader.data_proc)):
            df = self.data_loader.data_proc[i]
            hv = self.data_loader.data_attributes[i]['hv']
            polar_angle = self.data_loader.data_attributes[i]['polar']
            polar_angle_rad = np.deg2rad(polar_angle)

            emission_angles = df.columns.values.astype(float)
            theta_rad = np.deg2rad(emission_angles)

            # Calculate kinetic energy for desired binding energy
            E_kinetic = hv - WORK_FUNCTION - E_binding
            E_kinetic_values = df.index.values.astype(float)
            
            if E_kinetic < E_kinetic_values.min() or E_kinetic > E_kinetic_values.max():
                continue

            k_magnitude = 0.5123 * np.sqrt(E_kinetic)
            kx = k_magnitude * np.sin(theta_rad) + x_offset
            ky = k_magnitude * np.sin(polar_angle_rad) + y_offset

            # Extract intensities
            if E_kinetic in E_kinetic_values:
                intensities = df.loc[E_kinetic].values
            else:
                intensities = df.apply(
                    lambda col: np.interp(E_kinetic, E_kinetic_values, col.values)
                ).values

            if kernel_size > 1:
                intensities = self.moving_average(intensities, kernel_size)

            kx_list.extend(kx)
            ky_list.extend(np.full_like(kx, ky))
            intensity_list.extend(intensities)

        if not kx_list:
            return None, f"No data available for binding energy {E_binding:.2f} eV"

        # Convert to arrays and interpolate
        kx_array = np.array(kx_list)
        ky_array = np.array(ky_list)
        intensity_array = np.array(intensity_list)

        # Create grid for interpolation
        grid_resolution = 150
        kx_grid = np.linspace(kx_array.min(), kx_array.max(), grid_resolution)
        ky_grid = np.linspace(ky_array.min(), ky_array.max(), grid_resolution)
        kx_mesh, ky_mesh = np.meshgrid(kx_grid, ky_grid)

        intensity_grid = griddata(
            points=(kx_array, ky_array),
            values=intensity_array,
            xi=(kx_mesh, ky_mesh),
            method='cubic'
        )

        intensity_grid = np.nan_to_num(intensity_grid)
        max_intensity = intensity_grid.max()
        if max_intensity > 0:
            intensity_grid /= max_intensity

        # Create figure
        fig = go.Figure()
        colorscale_to_use = COLORSCALES.get(colorscale, 'Viridis')

        if use_contours:
            fig.add_trace(go.Contour(
                x=kx_grid,
                y=ky_grid,
                z=intensity_grid,
                colorscale=colorscale_to_use,
                ncontours=contour_levels,
                colorbar=dict(title="Intensity"),
                hovertemplate='kx: %{x:.3f} Å⁻¹<br>ky: %{y:.3f} Å⁻¹<br>I: %{z:.3f}<extra></extra>'
            ))
        else:
            fig.add_trace(go.Heatmap(
                x=kx_grid,
                y=ky_grid,
                z=intensity_grid,
                colorscale=colorscale_to_use,
                zmin=vmin,
                zmax=vmax,
                colorbar=dict(title="Intensity"),
                hovertemplate='kx: %{x:.3f} Å⁻¹<br>ky: %{y:.3f} Å⁻¹<br>I: %{z:.3f}<extra></extra>'
            ))

        # Add analysis features similar to E vs kx plot
        intensities_filled = np.nan_to_num(intensity_grid, nan=0.0)

        # Add peaks if requested
        if show_peaks:
            peak_indices = self.find_peaks_in_intensity(
                intensities_filled, neighborhood_size, peak_threshold, smoothing_sigma
            )

            if peak_indices.size > 0:
                peak_kx_coords = [kx_grid[i] for i, j in peak_indices]
                peak_ky_coords = [ky_grid[j] for i, j in peak_indices]

                fig.add_trace(go.Scatter(
                    x=peak_kx_coords,
                    y=peak_ky_coords,
                    mode='markers',
                    marker=dict(color='red', size=8, symbol='circle'),
                    name='Peaks',
                    hovertemplate='Peak<br>kx: %{x:.3f} Å⁻¹<br>ky: %{y:.3f} Å⁻¹<extra></extra>'
                ))

        # Add edge detection if requested
        if show_edges or show_components:
            edges = canny(intensities_filled, sigma=canny_sigma,
                         low_threshold=canny_low, high_threshold=canny_high)

            if show_edges:
                edge_coords = np.where(edges)
                if len(edge_coords[0]) > 0:
                    edge_kx_coords = [kx_grid[j] for i, j in zip(edge_coords[0], edge_coords[1])]
                    edge_ky_coords = [ky_grid[i] for i, j in zip(edge_coords[0], edge_coords[1])]

                    fig.add_trace(go.Scatter(
                        x=edge_kx_coords,
                        y=edge_ky_coords,
                        mode='markers',
                        marker=dict(color='cyan', size=2, symbol='circle'),
                        name='Edges',
                        hovertemplate='Edge<br>kx: %{x:.3f} Å⁻¹<br>ky: %{y:.3f} Å⁻¹<extra></extra>'
                    ))

            if show_components:
                labeled_array, num_features = label(edges, connectivity=2, return_num=True)

                for region_label in range(1, min(num_features + 1, 10)):
                    component_mask = labeled_array == region_label
                    component_coords = np.where(component_mask)

                    if len(component_coords[0]) > 0:
                        comp_kx_coords = [kx_grid[j] for i, j in zip(component_coords[0], component_coords[1])]
                        comp_ky_coords = [ky_grid[i] for i, j in zip(component_coords[0], component_coords[1])]

                        fig.add_trace(go.Scatter(
                            x=comp_kx_coords,
                            y=comp_ky_coords,
                            mode='markers',
                            marker=dict(color='yellow', size=1, symbol='circle'),
                            name=f'Component {region_label}',
                            hovertemplate=f'Component {region_label}<br>kx: %{{x:.3f}} Å⁻¹<br>ky: %{{y:.3f}} Å⁻¹<extra></extra>'
                        ))

        # Add Euler characteristic analysis
        if show_euler_points:
            binary_intensity = intensities_filled >= euler_threshold
            euler_char = self.compute_euler_characteristic(intensities_filled, euler_threshold)

            binary_coords = np.where(binary_intensity)
            if len(binary_coords[0]) > 0:
                euler_kx_coords = [kx_grid[j] for i, j in zip(binary_coords[0], binary_coords[1])]
                euler_ky_coords = [ky_grid[i] for i, j in zip(binary_coords[0], binary_coords[1])]

                fig.add_trace(go.Scatter(
                    x=euler_kx_coords,
                    y=euler_ky_coords,
                    mode='markers',
                    marker=dict(color='white', size=1, symbol='circle',
                               line=dict(color='black', width=0.5)),
                    name=f'Euler Points (χ={euler_char})',
                    hovertemplate=f'Euler Point<br>kx: %{{x:.3f}} Å⁻¹<br>ky: %{{y:.3f}} Å⁻¹<br>χ={euler_char}<extra></extra>'
                ))

        # Add critical points if requested
        if show_critical_points:
            maxima, minima, saddles = self.find_critical_points(intensities_filled, sigma=smoothing_sigma)

            # Plot maxima
            max_coords = np.where(maxima)
            if len(max_coords[0]) > 0:
                max_kx_coords = [kx_grid[j] for i, j in zip(max_coords[0], max_coords[1])]
                max_ky_coords = [ky_grid[i] for i, j in zip(max_coords[0], max_coords[1])]

                fig.add_trace(go.Scatter(
                    x=max_kx_coords,
                    y=max_ky_coords,
                    mode='markers',
                    marker=dict(color='green', size=8, symbol='triangle-up'),
                    name='Maxima',
                    hovertemplate='Maximum<br>kx: %{x:.3f} Å⁻¹<br>ky: %{y:.3f} Å⁻¹<extra></extra>'
                ))

            # Plot minima
            min_coords = np.where(minima)
            if len(min_coords[0]) > 0:
                min_kx_coords = [kx_grid[j] for i, j in zip(min_coords[0], min_coords[1])]
                min_ky_coords = [ky_grid[i] for i, j in zip(min_coords[0], min_coords[1])]

                fig.add_trace(go.Scatter(
                    x=min_kx_coords,
                    y=min_ky_coords,
                    mode='markers',
                    marker=dict(color='blue', size=8, symbol='triangle-down'),
                    name='Minima',
                    hovertemplate='Minimum<br>kx: %{x:.3f} Å⁻¹<br>ky: %{y:.3f} Å⁻¹<extra></extra>'
                ))

            # Plot saddles
            saddle_coords = np.where(saddles)
            if len(saddle_coords[0]) > 0:
                saddle_kx_coords = [kx_grid[j] for i, j in zip(saddle_coords[0], saddle_coords[1])]
                saddle_ky_coords = [ky_grid[i] for i, j in zip(saddle_coords[0], saddle_coords[1])]

                fig.add_trace(go.Scatter(
                    x=saddle_kx_coords,
                    y=saddle_ky_coords,
                    mode='markers',
                    marker=dict(color='orange', size=8, symbol='diamond'),
                    name='Saddles',
                    hovertemplate='Saddle<br>kx: %{x:.3f} Å⁻¹<br>ky: %{y:.3f} Å⁻¹<extra></extra>'
                ))

        labels = create_latex_labels()
        layout_style = create_professional_layout()
        title_style = create_professional_title_style()
        axis_style = create_professional_axis_style()
        colorbar_style = create_professional_colorbar_style()

        # Set plot dimensions - square if square mode is enabled
        plot_width = 700 if square_mode else 800
        plot_height = 700 if square_mode else 600

        # Conditionally set title based on hide_titles setting
        layout_dict = {
            'xaxis_title': labels['kx'],
            'yaxis_title': labels['ky'],
            'width': plot_width,
            'height': plot_height,
            **layout_style
        }

        if not hide_titles:
            layout_dict['title'] = dict(
                text=f'k<sub>x</sub> vs k<sub>y</sub> at E<sub>b</sub> = {E_binding:.2f} eV',
                **title_style
            )

        fig.update_layout(**layout_dict)

        # Apply professional axis styling
        fig.update_xaxes(**axis_style)
        fig.update_yaxes(**axis_style)

        # Apply square mode if enabled
        if square_mode:
            fig.update_layout(
                xaxis=dict(scaleanchor="y", scaleratio=1),
                yaxis=dict(scaleanchor="x", scaleratio=1)
            )

        # Update colorbar styling for heatmap traces
        for trace in fig.data:
            if hasattr(trace, 'colorbar') and trace.colorbar is not None:
                trace.update(colorbar=colorbar_style)

        self.current_fig = fig
        return fig, "Plot generated successfully"

    def plot_kx_vs_kz(self, E_binding, vmin, vmax, kernel_size, x_offset, y_offset,
                      colorscale='Custom Rainbow', use_contours=False, contour_levels=20,
                      show_peaks=False, peak_threshold=0.5, neighborhood_size=5, smoothing_sigma=1.0,
                      show_edges=False, show_components=False, canny_sigma=1.0, canny_low=0.1,
                      canny_high=0.3, euler_threshold=0.5, show_euler_points=False,
                      show_critical_points=False, square_mode=False, hide_titles=False):
        """Plot kx vs kz constant energy map"""

        if not self.data_loader.data_proc:
            return None, "No data loaded"

        # Collect data from all scans
        kx_list, kz_list, intensity_list = [], [], []

        for i in range(len(self.data_loader.data_proc)):
            df = self.data_loader.data_proc[i]
            hv = self.data_loader.data_attributes[i]['hv']

            emission_angles = df.columns.values.astype(float)
            theta_rad = np.deg2rad(emission_angles)

            # Calculate kinetic energy for desired binding energy
            E_kinetic = hv - WORK_FUNCTION - E_binding
            E_kinetic_values = df.index.values.astype(float)

            if E_kinetic < E_kinetic_values.min() or E_kinetic > E_kinetic_values.max():
                continue

            k_magnitude = 0.5123 * np.sqrt(E_kinetic)
            kx = k_magnitude * np.sin(theta_rad) + x_offset

            # Calculate kz using inner potential V0
            kz = 0.5123 * np.sqrt(E_kinetic * np.cos(theta_rad)**2 + V0) + y_offset

            # Extract intensities
            if E_kinetic in E_kinetic_values:
                intensities = df.loc[E_kinetic].values
            else:
                intensities = df.apply(
                    lambda col: np.interp(E_kinetic, E_kinetic_values, col.values)
                ).values

            if kernel_size > 1:
                intensities = self.moving_average(intensities, kernel_size)

            kx_list.extend(kx)
            kz_list.extend(kz)
            intensity_list.extend(intensities)

        if not kx_list:
            return None, f"No data available for binding energy {E_binding:.2f} eV"

        # Convert to arrays and interpolate
        kx_array = np.array(kx_list)
        kz_array = np.array(kz_list)
        intensity_array = np.array(intensity_list)

        # Create grid for interpolation
        grid_resolution = 150
        kx_grid = np.linspace(kx_array.min(), kx_array.max(), grid_resolution)
        kz_grid = np.linspace(kz_array.min(), kz_array.max(), grid_resolution)
        kx_mesh, kz_mesh = np.meshgrid(kx_grid, kz_grid)

        intensity_grid = griddata(
            points=(kx_array, kz_array),
            values=intensity_array,
            xi=(kx_mesh, kz_mesh),
            method='cubic'
        )

        intensity_grid = np.nan_to_num(intensity_grid)
        max_intensity = intensity_grid.max()
        if max_intensity > 0:
            intensity_grid /= max_intensity

        # Create figure
        fig = go.Figure()
        colorscale_to_use = COLORSCALES.get(colorscale, 'Viridis')

        if use_contours:
            fig.add_trace(go.Contour(
                x=kx_grid,
                y=kz_grid,
                z=intensity_grid,
                colorscale=colorscale_to_use,
                ncontours=contour_levels,
                colorbar=dict(title="Intensity"),
                hovertemplate='kx: %{x:.3f} Å⁻¹<br>kz: %{y:.3f} Å⁻¹<br>I: %{z:.3f}<extra></extra>'
            ))
        else:
            fig.add_trace(go.Heatmap(
                x=kx_grid,
                y=kz_grid,
                z=intensity_grid,
                colorscale=colorscale_to_use,
                zmin=vmin,
                zmax=vmax,
                colorbar=dict(title="Intensity"),
                hovertemplate='kx: %{x:.3f} Å⁻¹<br>kz: %{y:.3f} Å⁻¹<br>I: %{z:.3f}<extra></extra>'
            ))

        # Add analysis features (similar to kx vs ky)
        intensities_filled = np.nan_to_num(intensity_grid, nan=0.0)

        if show_peaks:
            peak_indices = self.find_peaks_in_intensity(
                intensities_filled, neighborhood_size, peak_threshold, smoothing_sigma
            )

            if peak_indices.size > 0:
                peak_kx_coords = [kx_grid[i] for i, j in peak_indices]
                peak_kz_coords = [kz_grid[j] for i, j in peak_indices]

                fig.add_trace(go.Scatter(
                    x=peak_kx_coords,
                    y=peak_kz_coords,
                    mode='markers',
                    marker=dict(color='red', size=8, symbol='circle'),
                    name='Peaks',
                    hovertemplate='Peak<br>kx: %{x:.3f} Å⁻¹<br>kz: %{y:.3f} Å⁻¹<extra></extra>'
                ))

        labels = create_latex_labels()
        layout_style = create_professional_layout()
        title_style = create_professional_title_style()
        axis_style = create_professional_axis_style()
        colorbar_style = create_professional_colorbar_style()

        # Set plot dimensions - square if square mode is enabled
        plot_width = 700 if square_mode else 800
        plot_height = 700 if square_mode else 600

        # Conditionally set title based on hide_titles setting
        layout_dict = {
            'xaxis_title': labels['kx'],
            'yaxis_title': labels['kz'],
            'width': plot_width,
            'height': plot_height,
            **layout_style
        }

        if not hide_titles:
            layout_dict['title'] = dict(
                text=f'k<sub>x</sub> vs k<sub>z</sub> at E<sub>b</sub> = {E_binding:.2f} eV',
                **title_style
            )

        fig.update_layout(**layout_dict)

        # Apply professional axis styling
        fig.update_xaxes(**axis_style)
        fig.update_yaxes(**axis_style)

        # Apply square mode if enabled
        if square_mode:
            fig.update_layout(
                xaxis=dict(scaleanchor="y", scaleratio=1),
                yaxis=dict(scaleanchor="x", scaleratio=1)
            )

        # Update colorbar styling for heatmap traces
        for trace in fig.data:
            if hasattr(trace, 'colorbar') and trace.colorbar is not None:
                trace.update(colorbar=colorbar_style)

        self.current_fig = fig
        return fig, "Plot generated successfully"


class ARPESAnalysisGUI:
    """Main GUI application class"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Enhanced ARPES Analysis Interface")
        self.root.geometry("900x1000")

        # Initialize data components
        self.data_loader = ARPESDataLoader()
        self.plotter = ARPESPlotter(self.data_loader)
        self.analyzer_3d = ARPES3DAnalyzer(self.data_loader, self.update_progress)

        # Connect logging to 3D analyzer (will be set after log_message is defined)
        self._connect_3d_logging = True

        # Create a consistent plot file for reuse
        self.plot_file = tempfile.NamedTemporaryFile(delete=False, suffix='.html',
                                                    prefix='arpes_plot_')
        self.plot_file.close()

        # Initialize plot window
        self.plot_window = PlotWindow(self.root)

        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_message = tk.StringVar(value="Ready")

        # Configuration management
        self.config_dir = Path.home() / ".arpes_gui_configs"
        self.config_dir.mkdir(exist_ok=True)
        self.current_data_folder = None
        self.config_file = None

        # Current settings
        self.settings = {
            'mode': 'E vs kx',
            'scan_number': 0,
            'E_binding': 0.0,
            'vmin': 0.0,
            'vmax': 1.0,
            'kernel_size': 1,
            'x_offset': 0.0,
            'y_offset': 0.0,
            'colorscale': 'Custom Rainbow',
            'show_peaks': False,
            'use_contours': False,
            'contour_levels': 20,
            'peak_threshold': 0.5,
            'neighborhood_size': 5,
            'smoothing_sigma': 1.0,
            'show_edges': False,
            'show_components': False,
            'canny_sigma': 1.0,
            'canny_low': 0.1,
            'canny_high': 0.3,
            'euler_threshold': 0.5,
            'show_euler_points': False,
            'show_critical_points': False,
            'live_update': False,
            'open_in_browser': False,
            'use_plot_window': True,
            'square_mode': False
        }

        self.setup_gui()

        # Setup cleanup on window close
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def on_closing(self):
        """Handle application closing"""
        try:
            # Save current configuration before closing
            if self.current_data_folder:
                self.save_current_config()
                self.log_message("💾 Configuration saved before closing", "INFO")

            # Clean up temporary files
            if hasattr(self, 'plot_file') and os.path.exists(self.plot_file.name):
                os.unlink(self.plot_file.name)

        except Exception as e:
            print(f"Error during cleanup: {e}")
        finally:
            self.root.destroy()

    def update_progress(self, value, message=""):
        """Update progress bar and message"""
        self.progress_var.set(value)
        self.progress_message.set(message)
        if message:
            self.log_message(f"[{value:3.0f}%] {message}")
        self.root.update_idletasks()

    def log_message(self, message, level="INFO"):
        """Add a message to the processing log"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")

        # Color coding for different log levels
        colors = {
            "INFO": "#333333",
            "SUCCESS": "#008000",
            "WARNING": "#FF8C00",
            "ERROR": "#DC143C",
            "DEBUG": "#4169E1"
        }

        formatted_message = f"[{timestamp}] {message}\n"

        # Insert message with appropriate color
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, formatted_message)

        # Apply color to the last line
        line_start = f"{self.log_text.index(tk.END)}-1l linestart"
        line_end = f"{self.log_text.index(tk.END)}-1l lineend"

        # Create tag for this log level if it doesn't exist
        tag_name = f"log_{level.lower()}"
        if tag_name not in self.log_text.tag_names():
            self.log_text.tag_configure(tag_name, foreground=colors.get(level, "#333333"))

        self.log_text.tag_add(tag_name, line_start, line_end)
        self.log_text.config(state=tk.DISABLED)

        # Auto-scroll if enabled
        if self.auto_scroll_var.get():
            self.log_text.see(tk.END)

    def clear_log(self):
        """Clear the processing log"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)
        self.log_message("📝 Log cleared", "INFO")

    def save_log(self):
        """Save the processing log to a file"""
        try:
            filename = filedialog.asksaveasfilename(
                title="Save Processing Log",
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
            )

            if filename:
                log_content = self.log_text.get(1.0, tk.END)
                with open(filename, 'w') as f:
                    f.write(log_content)
                self.log_message(f"💾 Log saved to {Path(filename).name}", "SUCCESS")

        except Exception as e:
            self.log_message(f"❌ Error saving log: {str(e)}", "ERROR")

    def get_folder_hash(self, folder_path):
        """Generate a unique hash for a data folder path"""
        return hashlib.md5(str(folder_path).encode()).hexdigest()[:12]

    def get_config_filename(self, folder_path):
        """Get configuration filename for a data folder"""
        folder_hash = self.get_folder_hash(folder_path)
        folder_name = Path(folder_path).name
        return self.config_dir / f"{folder_name}_{folder_hash}.json"

    def save_current_config(self):
        """Save current parameter settings to configuration file"""
        if not self.current_data_folder:
            return

        try:
            # Collect all current parameter values
            config = {
                'data_folder': str(self.current_data_folder),
                'timestamp': time.time(),
                # 2D parameters
                'mode': self.mode_var.get(),
                'scan_number': int(self.scan_var.get()),
                'E_binding': self.energy_var.get(),
                'vmin': self.vmin_var.get(),
                'vmax': self.vmax_var.get(),
                'kernel_size': int(self.kernel_var.get()),
                'x_offset': self.x_offset_var.get(),
                'y_offset': self.y_offset_var.get(),
                'colorscale': self.colorscale_var.get(),
                'show_peaks': self.show_peaks_var.get(),
                'use_contours': self.use_contours_var.get(),
                'contour_levels': int(self.contour_var.get()),
                'peak_threshold': self.peak_threshold_var.get(),
                'neighborhood_size': int(self.neighbor_var.get()),
                'smoothing_sigma': self.smooth_var.get(),
                'show_edges': self.show_edges_var.get(),
                'show_components': self.show_components_var.get(),
                'canny_sigma': self.canny_sigma_var.get(),
                'canny_low': self.canny_low_var.get(),
                'canny_high': self.canny_high_var.get(),
                'euler_threshold': self.euler_threshold_var.get(),
                'show_euler_points': self.show_euler_var.get(),
                'show_critical_points': self.show_critical_var.get(),
                'live_update': self.live_update_var.get(),
                'open_in_browser': self.open_in_browser_var.get(),
                'use_plot_window': self.use_plot_window_var.get(),
                'square_mode': self.square_mode_var.get(),
            }

            # Add 3D parameters if they exist
            if hasattr(self, 'threshold_3d_var'):
                config.update({
                    # 3D processing parameters
                    'threshold_3d': self.threshold_3d_var.get(),
                    'window_size': int(self.window_size_var.get()),
                    'suppress_at': self.suppress_var.get(),
                    # 3D visualization parameters
                    'energy_step': self.energy_step_var.get(),
                    'grid_size': int(self.grid_size_var.get()),
                    'smoothing_3d': self.smoothing_3d_var.get(),
                    'surface_threshold': self.surface_threshold_var.get(),
                    'kx_offset': self.kx_offset_var.get(),
                    'ky_offset': self.ky_offset_var.get(),
                    'energy_offset': self.energy_offset_var.get(),
                    # 3D range parameters
                    'energy_min': self.energy_min_var.get(),
                    'energy_max': self.energy_max_var.get(),
                    'kx_min': self.kx_min_var.get(),
                    'kx_max': self.kx_max_var.get(),
                    'ky_min': self.ky_min_var.get(),
                    'ky_max': self.ky_max_var.get(),
                    # 3D surface options
                    'single_color': self.single_color_var.get(),
                    'show_yellow_plane': self.show_yellow_plane_var.get(),
                    'yellow_plane_energy': self.yellow_plane_energy_var.get(),
                    'show_red_plane': self.show_red_plane_var.get(),
                    'red_plane_energy': self.red_plane_energy_var.get(),
                })

            # Add cross section offset parameters if they exist
            if hasattr(self, 'cross_kx_offset_var'):
                config.update({
                    'cross_kx_offset': self.cross_kx_offset_var.get(),
                    'cross_ky_offset': self.cross_ky_offset_var.get(),
                    'cross_energy_offset': self.cross_energy_offset_var.get(),
                })

            # Save to file
            config_file = self.get_config_filename(self.current_data_folder)
            with open(config_file, 'w') as f:
                json.dump(config, f, indent=2)

            self.log_message(f"💾 Configuration saved: {config_file.name}", "DEBUG")

        except Exception as e:
            self.log_message(f"❌ Error saving configuration: {str(e)}", "ERROR")

    def load_config_for_folder(self, folder_path):
        """Load configuration for a specific data folder"""
        try:
            config_file = self.get_config_filename(folder_path)
            if not config_file.exists():
                self.log_message(f"📋 No existing configuration found for this folder", "DEBUG")
                return False

            with open(config_file, 'r') as f:
                config = json.load(f)

            self.log_message(f"📂 Loading configuration: {config_file.name}", "INFO")

            # Apply 2D parameters
            if 'mode' in config:
                self.mode_var.set(config['mode'])
            if 'scan_number' in config:
                self.scan_var.set(config['scan_number'])
            if 'E_binding' in config:
                self.energy_var.set(config['E_binding'])
            if 'vmin' in config:
                self.vmin_var.set(config['vmin'])
            if 'vmax' in config:
                self.vmax_var.set(config['vmax'])
            if 'kernel_size' in config:
                self.kernel_var.set(config['kernel_size'])
            if 'x_offset' in config:
                self.x_offset_var.set(config['x_offset'])
            if 'y_offset' in config:
                self.y_offset_var.set(config['y_offset'])
            if 'colorscale' in config:
                self.colorscale_var.set(config['colorscale'])
            if 'show_peaks' in config:
                self.show_peaks_var.set(config['show_peaks'])
            if 'use_contours' in config:
                self.use_contours_var.set(config['use_contours'])
            if 'contour_levels' in config:
                self.contour_var.set(config['contour_levels'])
            if 'peak_threshold' in config:
                self.peak_threshold_var.set(config['peak_threshold'])
            if 'neighborhood_size' in config:
                self.neighbor_var.set(config['neighborhood_size'])
            if 'smoothing_sigma' in config:
                self.smooth_var.set(config['smoothing_sigma'])
            if 'show_edges' in config:
                self.show_edges_var.set(config['show_edges'])
            if 'show_components' in config:
                self.show_components_var.set(config['show_components'])
            if 'canny_sigma' in config:
                self.canny_sigma_var.set(config['canny_sigma'])
            if 'canny_low' in config:
                self.canny_low_var.set(config['canny_low'])
            if 'canny_high' in config:
                self.canny_high_var.set(config['canny_high'])
            if 'euler_threshold' in config:
                self.euler_threshold_var.set(config['euler_threshold'])
            if 'show_euler_points' in config:
                self.show_euler_var.set(config['show_euler_points'])
            if 'show_critical_points' in config:
                self.show_critical_var.set(config['show_critical_points'])
            if 'live_update' in config:
                self.live_update_var.set(config['live_update'])
            if 'open_in_browser' in config:
                self.open_in_browser_var.set(config['open_in_browser'])
            if 'use_plot_window' in config:
                self.use_plot_window_var.set(config['use_plot_window'])
            if 'square_mode' in config:
                self.square_mode_var.set(config['square_mode'])

            # Apply 3D parameters if they exist in config and GUI
            if hasattr(self, 'threshold_3d_var'):
                if 'threshold_3d' in config:
                    self.threshold_3d_var.set(config['threshold_3d'])
                if 'window_size' in config:
                    self.window_size_var.set(config['window_size'])
                if 'suppress_at' in config:
                    self.suppress_var.set(config['suppress_at'])
                if 'energy_step' in config:
                    self.energy_step_var.set(config['energy_step'])
                if 'grid_size' in config:
                    self.grid_size_var.set(config['grid_size'])
                if 'smoothing_3d' in config:
                    self.smoothing_3d_var.set(config['smoothing_3d'])
                if 'surface_threshold' in config:
                    self.surface_threshold_var.set(config['surface_threshold'])
                if 'kx_offset' in config:
                    self.kx_offset_var.set(config['kx_offset'])
                if 'ky_offset' in config:
                    self.ky_offset_var.set(config['ky_offset'])
                if 'energy_offset' in config:
                    self.energy_offset_var.set(config['energy_offset'])
                if 'energy_min' in config:
                    self.energy_min_var.set(config['energy_min'])
                if 'energy_max' in config:
                    self.energy_max_var.set(config['energy_max'])
                if 'kx_min' in config:
                    self.kx_min_var.set(config['kx_min'])
                if 'kx_max' in config:
                    self.kx_max_var.set(config['kx_max'])
                if 'ky_min' in config:
                    self.ky_min_var.set(config['ky_min'])
                if 'ky_max' in config:
                    self.ky_max_var.set(config['ky_max'])
                if 'single_color' in config:
                    self.single_color_var.set(config['single_color'])
                if 'show_yellow_plane' in config:
                    self.show_yellow_plane_var.set(config['show_yellow_plane'])
                if 'yellow_plane_energy' in config:
                    self.yellow_plane_energy_var.set(config['yellow_plane_energy'])
                if 'show_red_plane' in config:
                    self.show_red_plane_var.set(config['show_red_plane'])
                if 'red_plane_energy' in config:
                    self.red_plane_energy_var.set(config['red_plane_energy'])

            # Apply cross section offset parameters if they exist in config and GUI
            if hasattr(self, 'cross_kx_offset_var'):
                if 'cross_kx_offset' in config:
                    self.cross_kx_offset_var.set(config['cross_kx_offset'])
                if 'cross_ky_offset' in config:
                    self.cross_ky_offset_var.set(config['cross_ky_offset'])
                if 'cross_energy_offset' in config:
                    self.cross_energy_offset_var.set(config['cross_energy_offset'])

            # Update labels
            self.update_labels()
            if hasattr(self, 'update_3d_labels'):
                self.update_3d_labels()

            self.log_message("✅ Configuration loaded successfully", "SUCCESS")
            return True

        except Exception as e:
            self.log_message(f"❌ Error loading configuration: {str(e)}", "ERROR")
            return False

    def setup_gui(self):
        """Setup the GUI interface"""

        # Create main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Title
        title_label = ttk.Label(main_frame, text="🔬 Enhanced ARPES Analysis Interface",
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=(0, 10))

        # Create notebook for tabs
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # Create tabs
        self.setup_2d_analysis_tab()
        self.setup_3d_analysis_tab()
        self.setup_3d_cross_sections_tab()

        # Progress bar and logging at the bottom
        progress_frame = ttk.Frame(main_frame)
        progress_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Label(progress_frame, text="Progress:").pack(side=tk.LEFT)
        self.progress_bar = Progressbar(progress_frame, variable=self.progress_var,
                                       maximum=100, length=300)
        self.progress_bar.pack(side=tk.LEFT, padx=(10, 10))

        self.progress_label = ttk.Label(progress_frame, textvariable=self.progress_message)
        self.progress_label.pack(side=tk.LEFT)

        # Processing log section
        log_frame = ttk.LabelFrame(main_frame, text="Processing Log", padding=5)
        log_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))

        # Create scrollable text widget for log
        log_container = ttk.Frame(log_frame)
        log_container.pack(fill=tk.BOTH, expand=True)

        self.log_text = tk.Text(log_container, height=8, wrap=tk.WORD,
                               font=('Consolas', 9), bg='#f8f8f8', fg='#333333')
        log_scrollbar = ttk.Scrollbar(log_container, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

        self.log_text.pack(side="left", fill="both", expand=True)
        log_scrollbar.pack(side="right", fill="y")

        # Log control buttons
        log_controls = ttk.Frame(log_frame)
        log_controls.pack(fill=tk.X, pady=(5, 0))

        ttk.Button(log_controls, text="Clear Log", command=self.clear_log).pack(side=tk.LEFT)
        ttk.Button(log_controls, text="Save Log", command=self.save_log).pack(side=tk.LEFT, padx=(5, 0))

        self.auto_scroll_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(log_controls, text="Auto-scroll", variable=self.auto_scroll_var).pack(side=tk.LEFT, padx=(10, 0))

        # Initialize log
        self.log_message("Enhanced ARPES Analysis GUI initialized")
        self.log_message("Ready to load data and perform analysis")

        # Connect 3D analyzer logging now that log_message is available
        if hasattr(self, '_connect_3d_logging') and self._connect_3d_logging:
            self.analyzer_3d.log_message = self.log_message

    def setup_2d_analysis_tab(self):
        """Setup the 2D analysis tab"""
        tab_2d = ttk.Frame(self.notebook)
        self.notebook.add(tab_2d, text="2D Analysis")

        # Create scrollable frame for 2D analysis
        canvas = tk.Canvas(tab_2d)
        scrollbar = ttk.Scrollbar(tab_2d, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Move existing GUI elements to this tab
        self.setup_2d_controls(scrollable_frame)

    def setup_2d_controls(self, parent_frame):
        """Setup 2D analysis controls"""
        # Data loading section
        data_frame = ttk.LabelFrame(parent_frame, text="Data Loading", padding=10)
        data_frame.pack(fill=tk.X, pady=(0, 10))

        button_frame = ttk.Frame(data_frame)
        button_frame.pack(fill=tk.X)

        self.load_button = ttk.Button(button_frame, text="Load PXT Files",
                                     command=self.load_data)
        self.load_button.pack(side=tk.LEFT, padx=(0, 10))

        self.export_button = ttk.Button(button_frame, text="Export Plot",
                                       command=self.export_plot)
        self.export_button.pack(side=tk.LEFT)

        self.generate_button = ttk.Button(button_frame, text="Generate Plot",
                                         command=self.generate_plot)
        self.generate_button.pack(side=tk.LEFT, padx=(10, 0))

        self.plot_window_button = ttk.Button(button_frame, text="Show Plot Window",
                                            command=self.show_plot_window)
        self.plot_window_button.pack(side=tk.LEFT, padx=(10, 0))

        self.status_label = ttk.Label(data_frame, text="Ready to load data",
                                     foreground="blue")
        self.status_label.pack(pady=(10, 0))

        # Add helpful info
        info_label = ttk.Label(data_frame,
                              text="💡 Tip: Use 'Show Plot Window' for dedicated plot viewing, enable 'Live Plot Updates' for real-time changes",
                              font=('Arial', 8), foreground="gray")
        info_label.pack(pady=(5, 0))

        # Plot settings section
        plot_frame = ttk.LabelFrame(parent_frame, text="Plot Settings", padding=10)
        plot_frame.pack(fill=tk.X, pady=(0, 10))

        # Mode and colorscale
        mode_frame = ttk.Frame(plot_frame)
        mode_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(mode_frame, text="Plot Mode:").pack(side=tk.LEFT)
        self.mode_var = tk.StringVar(value="E vs kx")
        mode_combo = ttk.Combobox(mode_frame, textvariable=self.mode_var,
                                 values=["E vs kx", "kx vs ky", "kx vs kz"], state="readonly", width=15)
        mode_combo.pack(side=tk.LEFT, padx=(10, 20))
        mode_combo.bind('<<ComboboxSelected>>', self.on_setting_change)

        ttk.Label(mode_frame, text="Colorscale:").pack(side=tk.LEFT)
        self.colorscale_var = tk.StringVar(value="Custom Rainbow")
        colorscale_combo = ttk.Combobox(mode_frame, textvariable=self.colorscale_var,
                                       values=list(COLORSCALES.keys()), state="readonly", width=15)
        colorscale_combo.pack(side=tk.LEFT, padx=(10, 0))
        colorscale_combo.bind('<<ComboboxSelected>>', self.on_setting_change)

        # Scan number and binding energy
        scan_frame = ttk.Frame(plot_frame)
        scan_frame.pack(fill=tk.X, pady=(5, 5))

        ttk.Label(scan_frame, text="Scan Number:").pack(side=tk.LEFT)
        self.scan_var = tk.IntVar(value=0)
        self.scan_scale = ttk.Scale(scan_frame, from_=0, to=0, variable=self.scan_var,
                                   orient=tk.HORIZONTAL, length=200, command=self.on_setting_change)
        self.scan_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.scan_label = ttk.Label(scan_frame, text="0")
        self.scan_label.pack(side=tk.LEFT)

        energy_frame = ttk.Frame(plot_frame)
        energy_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Label(energy_frame, text="Binding Energy (eV):").pack(side=tk.LEFT)
        self.energy_var = tk.DoubleVar(value=0.0)
        self.energy_scale = ttk.Scale(energy_frame, from_=-2.0, to=2.0, variable=self.energy_var,
                                     orient=tk.HORIZONTAL, length=200, command=self.on_setting_change)
        self.energy_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.energy_label = ttk.Label(energy_frame, text="0.00")
        self.energy_label.pack(side=tk.LEFT)

        # Display controls section
        display_frame = ttk.LabelFrame(parent_frame, text="Display Controls", padding=10)
        display_frame.pack(fill=tk.X, pady=(0, 10))

        # Intensity range
        intensity_frame = ttk.Frame(display_frame)
        intensity_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(intensity_frame, text="Min Intensity:").pack(side=tk.LEFT)
        self.vmin_var = tk.DoubleVar(value=0.0)
        vmin_scale = ttk.Scale(intensity_frame, from_=0.0, to=1.0, variable=self.vmin_var,
                              orient=tk.HORIZONTAL, length=150, command=self.on_setting_change)
        vmin_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.vmin_label = ttk.Label(intensity_frame, text="0.00")
        self.vmin_label.pack(side=tk.LEFT, padx=(0, 20))

        ttk.Label(intensity_frame, text="Max Intensity:").pack(side=tk.LEFT)
        self.vmax_var = tk.DoubleVar(value=1.0)
        vmax_scale = ttk.Scale(intensity_frame, from_=0.0, to=1.0, variable=self.vmax_var,
                              orient=tk.HORIZONTAL, length=150, command=self.on_setting_change)
        vmax_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.vmax_label = ttk.Label(intensity_frame, text="1.00")
        self.vmax_label.pack(side=tk.LEFT)

        # Offsets
        offset_frame = ttk.Frame(display_frame)
        offset_frame.pack(fill=tk.X, pady=(5, 5))

        ttk.Label(offset_frame, text="X Offset:").pack(side=tk.LEFT)
        self.x_offset_var = tk.DoubleVar(value=0.0)
        x_offset_scale = ttk.Scale(offset_frame, from_=-2.0, to=2.0, variable=self.x_offset_var,
                                  orient=tk.HORIZONTAL, length=150, command=self.on_setting_change)
        x_offset_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.x_offset_label = ttk.Label(offset_frame, text="0.00")
        self.x_offset_label.pack(side=tk.LEFT, padx=(0, 20))

        ttk.Label(offset_frame, text="Y Offset:").pack(side=tk.LEFT)
        self.y_offset_var = tk.DoubleVar(value=0.0)
        y_offset_scale = ttk.Scale(offset_frame, from_=-2.0, to=2.0, variable=self.y_offset_var,
                                  orient=tk.HORIZONTAL, length=150, command=self.on_setting_change)
        y_offset_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.y_offset_label = ttk.Label(offset_frame, text="0.00")
        self.y_offset_label.pack(side=tk.LEFT)

        # Kernel size
        kernel_frame = ttk.Frame(display_frame)
        kernel_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Label(kernel_frame, text="Kernel Size:").pack(side=tk.LEFT)
        self.kernel_var = tk.IntVar(value=1)
        kernel_scale = ttk.Scale(kernel_frame, from_=1, to=21, variable=self.kernel_var,
                                orient=tk.HORIZONTAL, length=200, command=self.on_setting_change)
        kernel_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.kernel_label = ttk.Label(kernel_frame, text="1")
        self.kernel_label.pack(side=tk.LEFT)

        # Analysis options section
        analysis_frame = ttk.LabelFrame(parent_frame, text="Analysis Options", padding=10)
        analysis_frame.pack(fill=tk.X, pady=(0, 10))

        # Checkboxes
        checkbox_frame = ttk.Frame(analysis_frame)
        checkbox_frame.pack(fill=tk.X, pady=(0, 5))

        self.show_peaks_var = tk.BooleanVar(value=False)
        peaks_check = ttk.Checkbutton(checkbox_frame, text="Show Peaks",
                                     variable=self.show_peaks_var, command=self.on_setting_change)
        peaks_check.pack(side=tk.LEFT, padx=(0, 20))

        self.use_contours_var = tk.BooleanVar(value=False)
        contours_check = ttk.Checkbutton(checkbox_frame, text="Use Contours",
                                        variable=self.use_contours_var, command=self.on_setting_change)
        contours_check.pack(side=tk.LEFT)

        # Analysis parameters
        contour_frame = ttk.Frame(analysis_frame)
        contour_frame.pack(fill=tk.X, pady=(5, 5))

        ttk.Label(contour_frame, text="Contour Levels:").pack(side=tk.LEFT)
        self.contour_var = tk.IntVar(value=20)
        contour_scale = ttk.Scale(contour_frame, from_=5, to=50, variable=self.contour_var,
                                 orient=tk.HORIZONTAL, length=200, command=self.on_setting_change)
        contour_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.contour_label = ttk.Label(contour_frame, text="20")
        self.contour_label.pack(side=tk.LEFT)

        peak_frame = ttk.Frame(analysis_frame)
        peak_frame.pack(fill=tk.X, pady=(5, 5))

        ttk.Label(peak_frame, text="Peak Threshold:").pack(side=tk.LEFT)
        self.peak_threshold_var = tk.DoubleVar(value=0.5)
        peak_scale = ttk.Scale(peak_frame, from_=0.0, to=1.0, variable=self.peak_threshold_var,
                              orient=tk.HORIZONTAL, length=200, command=self.on_setting_change)
        peak_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.peak_threshold_label = ttk.Label(peak_frame, text="0.50")
        self.peak_threshold_label.pack(side=tk.LEFT)

        neighbor_frame = ttk.Frame(analysis_frame)
        neighbor_frame.pack(fill=tk.X, pady=(5, 5))

        ttk.Label(neighbor_frame, text="Neighborhood Size:").pack(side=tk.LEFT)
        self.neighbor_var = tk.IntVar(value=5)
        neighbor_scale = ttk.Scale(neighbor_frame, from_=3, to=15, variable=self.neighbor_var,
                                  orient=tk.HORIZONTAL, length=200, command=self.on_setting_change)
        neighbor_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.neighbor_label = ttk.Label(neighbor_frame, text="5")
        self.neighbor_label.pack(side=tk.LEFT)

        smooth_frame = ttk.Frame(analysis_frame)
        smooth_frame.pack(fill=tk.X, pady=(5, 5))

        ttk.Label(smooth_frame, text="Smoothing Sigma:").pack(side=tk.LEFT)
        self.smooth_var = tk.DoubleVar(value=1.0)
        smooth_scale = ttk.Scale(smooth_frame, from_=0.1, to=3.0, variable=self.smooth_var,
                                orient=tk.HORIZONTAL, length=200, command=self.on_setting_change)
        smooth_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.smooth_label = ttk.Label(smooth_frame, text="1.00")
        self.smooth_label.pack(side=tk.LEFT)

        # Advanced analysis section
        advanced_frame = ttk.LabelFrame(parent_frame, text="Advanced Analysis", padding=10)
        advanced_frame.pack(fill=tk.X, pady=(0, 10))

        # Advanced checkboxes
        advanced_checkbox_frame = ttk.Frame(advanced_frame)
        advanced_checkbox_frame.pack(fill=tk.X, pady=(0, 5))

        self.show_edges_var = tk.BooleanVar(value=False)
        edges_check = ttk.Checkbutton(advanced_checkbox_frame, text="Show Edges",
                                     variable=self.show_edges_var, command=self.on_setting_change)
        edges_check.pack(side=tk.LEFT, padx=(0, 20))

        self.show_components_var = tk.BooleanVar(value=False)
        components_check = ttk.Checkbutton(advanced_checkbox_frame, text="Show Components",
                                          variable=self.show_components_var, command=self.on_setting_change)
        components_check.pack(side=tk.LEFT, padx=(0, 20))

        self.show_euler_var = tk.BooleanVar(value=False)
        euler_check = ttk.Checkbutton(advanced_checkbox_frame, text="Show Euler Points",
                                     variable=self.show_euler_var, command=self.on_setting_change)
        euler_check.pack(side=tk.LEFT, padx=(0, 20))

        self.show_critical_var = tk.BooleanVar(value=False)
        critical_check = ttk.Checkbutton(advanced_checkbox_frame, text="Show Critical Points",
                                        variable=self.show_critical_var, command=self.on_setting_change)
        critical_check.pack(side=tk.LEFT)

        # Canny edge detection parameters
        canny_frame = ttk.Frame(advanced_frame)
        canny_frame.pack(fill=tk.X, pady=(5, 5))

        ttk.Label(canny_frame, text="Canny Sigma:").pack(side=tk.LEFT)
        self.canny_sigma_var = tk.DoubleVar(value=1.0)
        canny_sigma_scale = ttk.Scale(canny_frame, from_=0.1, to=3.0, variable=self.canny_sigma_var,
                                     orient=tk.HORIZONTAL, length=120, command=self.on_setting_change)
        canny_sigma_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.canny_sigma_label = ttk.Label(canny_frame, text="1.00")
        self.canny_sigma_label.pack(side=tk.LEFT, padx=(0, 20))

        ttk.Label(canny_frame, text="Low Threshold:").pack(side=tk.LEFT)
        self.canny_low_var = tk.DoubleVar(value=0.1)
        canny_low_scale = ttk.Scale(canny_frame, from_=0.0, to=1.0, variable=self.canny_low_var,
                                   orient=tk.HORIZONTAL, length=120, command=self.on_setting_change)
        canny_low_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.canny_low_label = ttk.Label(canny_frame, text="0.10")
        self.canny_low_label.pack(side=tk.LEFT)

        canny_frame2 = ttk.Frame(advanced_frame)
        canny_frame2.pack(fill=tk.X, pady=(5, 5))

        ttk.Label(canny_frame2, text="High Threshold:").pack(side=tk.LEFT)
        self.canny_high_var = tk.DoubleVar(value=0.3)
        canny_high_scale = ttk.Scale(canny_frame2, from_=0.0, to=1.0, variable=self.canny_high_var,
                                    orient=tk.HORIZONTAL, length=120, command=self.on_setting_change)
        canny_high_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.canny_high_label = ttk.Label(canny_frame2, text="0.30")
        self.canny_high_label.pack(side=tk.LEFT, padx=(0, 20))

        ttk.Label(canny_frame2, text="Euler Threshold:").pack(side=tk.LEFT)
        self.euler_threshold_var = tk.DoubleVar(value=0.5)
        euler_threshold_scale = ttk.Scale(canny_frame2, from_=0.0, to=1.0, variable=self.euler_threshold_var,
                                         orient=tk.HORIZONTAL, length=120, command=self.on_setting_change)
        euler_threshold_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.euler_threshold_label = ttk.Label(canny_frame2, text="0.50")
        self.euler_threshold_label.pack(side=tk.LEFT)

        # Live update checkbox
        live_frame = ttk.Frame(advanced_frame)
        live_frame.pack(fill=tk.X, pady=(5, 0))

        self.live_update_var = tk.BooleanVar(value=False)
        live_check = ttk.Checkbutton(live_frame, text="Live Plot Updates",
                                    variable=self.live_update_var, command=self.on_setting_change)
        live_check.pack(side=tk.LEFT, padx=(0, 20))

        self.open_in_browser_var = tk.BooleanVar(value=False)
        browser_check = ttk.Checkbutton(live_frame, text="Open in Browser",
                                       variable=self.open_in_browser_var, command=self.on_setting_change)
        browser_check.pack(side=tk.LEFT, padx=(0, 20))

        self.use_plot_window_var = tk.BooleanVar(value=True)
        plot_window_check = ttk.Checkbutton(live_frame, text="Use Plot Window",
                                          variable=self.use_plot_window_var, command=self.on_setting_change)
        plot_window_check.pack(side=tk.LEFT, padx=(0, 20))

        self.square_mode_var = tk.BooleanVar(value=False)
        square_mode_check = ttk.Checkbutton(live_frame, text="Square Plot (700×700)",
                                          variable=self.square_mode_var, command=self.on_setting_change)
        square_mode_check.pack(side=tk.LEFT, padx=(0, 20))

        self.hide_titles_var = tk.BooleanVar(value=False)
        hide_titles_check = ttk.Checkbutton(live_frame, text="Hide Plot Titles",
                                          variable=self.hide_titles_var, command=self.on_setting_change)
        hide_titles_check.pack(side=tk.LEFT)

    def setup_3d_analysis_tab(self):
        """Setup the 3D analysis tab"""
        tab_3d = ttk.Frame(self.notebook)
        self.notebook.add(tab_3d, text="3D Analysis")

        # Create scrollable frame for 3D analysis
        canvas = tk.Canvas(tab_3d)
        scrollbar = ttk.Scrollbar(tab_3d, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 3D Analysis controls
        self.setup_3d_controls(scrollable_frame)

    def setup_3d_controls(self, parent_frame):
        """Setup 3D analysis controls"""

        # Data loading section for 3D
        data_3d_frame = ttk.LabelFrame(parent_frame, text="3D Data Loading", padding=10)
        data_3d_frame.pack(fill=tk.X, pady=(0, 10))

        button_3d_frame = ttk.Frame(data_3d_frame)
        button_3d_frame.pack(fill=tk.X)

        self.load_processed_button = ttk.Button(button_3d_frame, text="Load Processed Data",
                                               command=self.load_processed_data)
        self.load_processed_button.pack(side=tk.LEFT, padx=(0, 10))

        self.save_processed_button = ttk.Button(button_3d_frame, text="Save Processed Data",
                                               command=self.save_processed_data)
        self.save_processed_button.pack(side=tk.LEFT)

        # 3D Processing section
        processing_frame = ttk.LabelFrame(parent_frame, text="3D Data Processing", padding=10)
        processing_frame.pack(fill=tk.X, pady=(0, 10))

        # Processing parameters
        params_frame = ttk.Frame(processing_frame)
        params_frame.pack(fill=tk.X, pady=(0, 10))

        # Threshold
        threshold_frame = ttk.Frame(params_frame)
        threshold_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(threshold_frame, text="Threshold:").pack(side=tk.LEFT)
        self.threshold_3d_var = tk.DoubleVar(value=0.1)
        threshold_scale = ttk.Scale(threshold_frame, from_=0.01, to=0.5, variable=self.threshold_3d_var,
                                   orient=tk.HORIZONTAL, length=200, command=self.update_3d_labels)
        threshold_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.threshold_3d_label = ttk.Label(threshold_frame, text="0.10")
        self.threshold_3d_label.pack(side=tk.LEFT)

        # Window size
        window_frame = ttk.Frame(params_frame)
        window_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(window_frame, text="Window Size:").pack(side=tk.LEFT)
        self.window_size_var = tk.IntVar(value=3)
        window_scale = ttk.Scale(window_frame, from_=1, to=10, variable=self.window_size_var,
                                orient=tk.HORIZONTAL, length=200, command=self.update_3d_labels)
        window_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.window_size_label = ttk.Label(window_frame, text="3")
        self.window_size_label.pack(side=tk.LEFT)

        # Suppress at
        suppress_frame = ttk.Frame(params_frame)
        suppress_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(suppress_frame, text="Suppress at:").pack(side=tk.LEFT)
        self.suppress_var = tk.StringVar(value="max")
        suppress_combo = ttk.Combobox(suppress_frame, textvariable=self.suppress_var,
                                     values=["max", "min", "none"], state="readonly", width=10)
        suppress_combo.pack(side=tk.LEFT, padx=(10, 0))

        # Process button
        process_button = ttk.Button(processing_frame, text="Process 3D Data",
                                   command=self.process_3d_data)
        process_button.pack(pady=(10, 0))

        # 3D Visualization Parameters section
        viz_params_frame = ttk.LabelFrame(parent_frame, text="3D Visualization Parameters", padding=10)
        viz_params_frame.pack(fill=tk.X, pady=(0, 10))

        # Energy step and grid size
        energy_grid_frame = ttk.Frame(viz_params_frame)
        energy_grid_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(energy_grid_frame, text="Energy Step:").pack(side=tk.LEFT)
        self.energy_step_var = tk.DoubleVar(value=0.01)
        energy_step_scale = ttk.Scale(energy_grid_frame, from_=0.005, to=0.1, variable=self.energy_step_var,
                                     orient=tk.HORIZONTAL, length=150, command=self.update_3d_labels)
        energy_step_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.energy_step_label = ttk.Label(energy_grid_frame, text="0.01")
        self.energy_step_label.pack(side=tk.LEFT, padx=(0, 20))

        ttk.Label(energy_grid_frame, text="Grid Size:").pack(side=tk.LEFT)
        self.grid_size_var = tk.IntVar(value=200)
        grid_size_scale = ttk.Scale(energy_grid_frame, from_=50, to=500, variable=self.grid_size_var,
                                   orient=tk.HORIZONTAL, length=150, command=self.update_3d_labels)
        grid_size_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.grid_size_label = ttk.Label(energy_grid_frame, text="200")
        self.grid_size_label.pack(side=tk.LEFT)

        # Smoothing and surface threshold
        smooth_surface_frame = ttk.Frame(viz_params_frame)
        smooth_surface_frame.pack(fill=tk.X, pady=(5, 5))

        ttk.Label(smooth_surface_frame, text="Smoothing Sigma:").pack(side=tk.LEFT)
        self.smoothing_3d_var = tk.DoubleVar(value=1.3)
        smoothing_3d_scale = ttk.Scale(smooth_surface_frame, from_=0.1, to=5.0, variable=self.smoothing_3d_var,
                                      orient=tk.HORIZONTAL, length=150, command=self.update_3d_labels)
        smoothing_3d_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.smoothing_3d_label = ttk.Label(smooth_surface_frame, text="1.30")
        self.smoothing_3d_label.pack(side=tk.LEFT, padx=(0, 20))

        ttk.Label(smooth_surface_frame, text="Surface Threshold:").pack(side=tk.LEFT)
        self.surface_threshold_var = tk.DoubleVar(value=0.2)
        surface_threshold_scale = ttk.Scale(smooth_surface_frame, from_=0.01, to=1.0, variable=self.surface_threshold_var,
                                           orient=tk.HORIZONTAL, length=150, command=self.update_3d_labels)
        surface_threshold_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.surface_threshold_label = ttk.Label(smooth_surface_frame, text="0.20")
        self.surface_threshold_label.pack(side=tk.LEFT)

        # 3D Offsets
        offsets_3d_frame = ttk.Frame(viz_params_frame)
        offsets_3d_frame.pack(fill=tk.X, pady=(5, 5))

        ttk.Label(offsets_3d_frame, text="kx Offset:").pack(side=tk.LEFT)
        self.kx_offset_var = tk.DoubleVar(value=0.0)
        kx_offset_scale = ttk.Scale(offsets_3d_frame, from_=-2.0, to=2.0, variable=self.kx_offset_var,
                                   orient=tk.HORIZONTAL, length=120, command=self.update_3d_labels)
        kx_offset_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.kx_offset_label = ttk.Label(offsets_3d_frame, text="0.00")
        self.kx_offset_label.pack(side=tk.LEFT, padx=(0, 15))

        ttk.Label(offsets_3d_frame, text="ky Offset:").pack(side=tk.LEFT)
        self.ky_offset_var = tk.DoubleVar(value=0.0)
        ky_offset_scale = ttk.Scale(offsets_3d_frame, from_=-2.0, to=2.0, variable=self.ky_offset_var,
                                   orient=tk.HORIZONTAL, length=120, command=self.update_3d_labels)
        ky_offset_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.ky_offset_label = ttk.Label(offsets_3d_frame, text="0.00")
        self.ky_offset_label.pack(side=tk.LEFT, padx=(0, 15))

        ttk.Label(offsets_3d_frame, text="Energy Offset:").pack(side=tk.LEFT)
        self.energy_offset_var = tk.DoubleVar(value=0.0)
        energy_offset_scale = ttk.Scale(offsets_3d_frame, from_=-2.0, to=2.0, variable=self.energy_offset_var,
                                       orient=tk.HORIZONTAL, length=120, command=self.update_3d_labels)
        energy_offset_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.energy_offset_label = ttk.Label(offsets_3d_frame, text="0.00")
        self.energy_offset_label.pack(side=tk.LEFT)

        # 3D Range Controls
        range_3d_frame = ttk.LabelFrame(parent_frame, text="3D Range Controls", padding=10)
        range_3d_frame.pack(fill=tk.X, pady=(0, 10))

        # Energy range
        energy_range_frame = ttk.Frame(range_3d_frame)
        energy_range_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(energy_range_frame, text="Energy Min:").pack(side=tk.LEFT)
        self.energy_min_var = tk.DoubleVar(value=-1.1)
        energy_min_scale = ttk.Scale(energy_range_frame, from_=-3.0, to=1.0, variable=self.energy_min_var,
                                    orient=tk.HORIZONTAL, length=150, command=self.update_3d_labels)
        energy_min_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.energy_min_label = ttk.Label(energy_range_frame, text="-1.10")
        self.energy_min_label.pack(side=tk.LEFT, padx=(0, 20))

        ttk.Label(energy_range_frame, text="Energy Max:").pack(side=tk.LEFT)
        self.energy_max_var = tk.DoubleVar(value=0.1)
        energy_max_scale = ttk.Scale(energy_range_frame, from_=-1.0, to=2.0, variable=self.energy_max_var,
                                    orient=tk.HORIZONTAL, length=150, command=self.update_3d_labels)
        energy_max_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.energy_max_label = ttk.Label(energy_range_frame, text="0.10")
        self.energy_max_label.pack(side=tk.LEFT)

        # kx and ky range
        kx_range_frame = ttk.Frame(range_3d_frame)
        kx_range_frame.pack(fill=tk.X, pady=(5, 5))

        ttk.Label(kx_range_frame, text="kx Min:").pack(side=tk.LEFT)
        self.kx_min_var = tk.DoubleVar(value=-0.5)
        kx_min_scale = ttk.Scale(kx_range_frame, from_=-2.0, to=1.0, variable=self.kx_min_var,
                                orient=tk.HORIZONTAL, length=120, command=self.update_3d_labels)
        kx_min_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.kx_min_label = ttk.Label(kx_range_frame, text="-0.50")
        self.kx_min_label.pack(side=tk.LEFT, padx=(0, 15))

        ttk.Label(kx_range_frame, text="kx Max:").pack(side=tk.LEFT)
        self.kx_max_var = tk.DoubleVar(value=0.5)
        kx_max_scale = ttk.Scale(kx_range_frame, from_=-1.0, to=2.0, variable=self.kx_max_var,
                                orient=tk.HORIZONTAL, length=120, command=self.update_3d_labels)
        kx_max_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.kx_max_label = ttk.Label(kx_range_frame, text="0.50")
        self.kx_max_label.pack(side=tk.LEFT, padx=(0, 15))

        ttk.Label(kx_range_frame, text="ky Min:").pack(side=tk.LEFT)
        self.ky_min_var = tk.DoubleVar(value=-0.9)
        ky_min_scale = ttk.Scale(kx_range_frame, from_=-2.0, to=1.0, variable=self.ky_min_var,
                                orient=tk.HORIZONTAL, length=120, command=self.update_3d_labels)
        ky_min_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.ky_min_label = ttk.Label(kx_range_frame, text="-0.90")
        self.ky_min_label.pack(side=tk.LEFT)

        ky_range_frame = ttk.Frame(range_3d_frame)
        ky_range_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Label(ky_range_frame, text="ky Max:").pack(side=tk.LEFT)
        self.ky_max_var = tk.DoubleVar(value=0.1)
        ky_max_scale = ttk.Scale(ky_range_frame, from_=-1.0, to=2.0, variable=self.ky_max_var,
                                orient=tk.HORIZONTAL, length=150, command=self.update_3d_labels)
        ky_max_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.ky_max_label = ttk.Label(ky_range_frame, text="0.10")
        self.ky_max_label.pack(side=tk.LEFT)

        # 3D Surface Options
        surface_options_frame = ttk.LabelFrame(parent_frame, text="3D Surface Options", padding=10)
        surface_options_frame.pack(fill=tk.X, pady=(0, 10))

        # Surface mode and plane options
        surface_mode_frame = ttk.Frame(surface_options_frame)
        surface_mode_frame.pack(fill=tk.X, pady=(0, 5))

        self.single_color_var = tk.BooleanVar(value=True)
        single_color_check = ttk.Checkbutton(surface_mode_frame, text="Single Color Mode",
                                           variable=self.single_color_var)
        single_color_check.pack(side=tk.LEFT, padx=(0, 20))

        self.show_yellow_plane_var = tk.BooleanVar(value=False)
        yellow_plane_check = ttk.Checkbutton(surface_mode_frame, text="Show Yellow Plane",
                                           variable=self.show_yellow_plane_var)
        yellow_plane_check.pack(side=tk.LEFT, padx=(0, 20))

        self.show_red_plane_var = tk.BooleanVar(value=False)
        red_plane_check = ttk.Checkbutton(surface_mode_frame, text="Show Red Plane",
                                        variable=self.show_red_plane_var)
        red_plane_check.pack(side=tk.LEFT, padx=(0, 20))

        # Critical points checkbox for 3D projections
        self.show_critical_points_3d_var = tk.BooleanVar(value=False)
        critical_points_3d_check = ttk.Checkbutton(surface_mode_frame, text="Show Critical Points",
                                                 variable=self.show_critical_points_3d_var)
        critical_points_3d_check.pack(side=tk.LEFT)

        # Plane energy values
        plane_energy_frame = ttk.Frame(surface_options_frame)
        plane_energy_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Label(plane_energy_frame, text="Yellow Plane Energy:").pack(side=tk.LEFT)
        self.yellow_plane_energy_var = tk.DoubleVar(value=0.0)
        yellow_plane_scale = ttk.Scale(plane_energy_frame, from_=-2.0, to=1.0, variable=self.yellow_plane_energy_var,
                                      orient=tk.HORIZONTAL, length=150, command=self.update_3d_labels)
        yellow_plane_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.yellow_plane_energy_label = ttk.Label(plane_energy_frame, text="0.00")
        self.yellow_plane_energy_label.pack(side=tk.LEFT, padx=(0, 20))

        ttk.Label(plane_energy_frame, text="Red Plane Energy:").pack(side=tk.LEFT)
        self.red_plane_energy_var = tk.DoubleVar(value=-1.0)
        red_plane_scale = ttk.Scale(plane_energy_frame, from_=-2.0, to=1.0, variable=self.red_plane_energy_var,
                                   orient=tk.HORIZONTAL, length=150, command=self.update_3d_labels)
        red_plane_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.red_plane_energy_label = ttk.Label(plane_energy_frame, text="-1.00")
        self.red_plane_energy_label.pack(side=tk.LEFT)

        # Critical Points Parameters section
        cp_frame = ttk.LabelFrame(parent_frame, text="Critical Points Parameters", padding=10)
        cp_frame.pack(fill=tk.X, pady=(0, 10))

        # First row of critical point parameters
        cp_row1_frame = ttk.Frame(cp_frame)
        cp_row1_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(cp_row1_frame, text="Intensity Threshold:").pack(side=tk.LEFT)
        self.cp_intensity_threshold_var = tk.DoubleVar(value=0.01)
        cp_intensity_scale = ttk.Scale(cp_row1_frame, from_=0.001, to=0.1, variable=self.cp_intensity_threshold_var,
                                      orient=tk.HORIZONTAL, length=120, command=self.update_3d_labels)
        cp_intensity_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.cp_intensity_threshold_label = ttk.Label(cp_row1_frame, text="0.01")
        self.cp_intensity_threshold_label.pack(side=tk.LEFT, padx=(0, 15))

        ttk.Label(cp_row1_frame, text="Gradient Threshold:").pack(side=tk.LEFT)
        self.cp_gradient_threshold_var = tk.DoubleVar(value=0.01)
        cp_gradient_scale = ttk.Scale(cp_row1_frame, from_=0.005, to=0.1, variable=self.cp_gradient_threshold_var,
                                     orient=tk.HORIZONTAL, length=120, command=self.update_3d_labels)
        cp_gradient_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.cp_gradient_threshold_label = ttk.Label(cp_row1_frame, text="0.01")
        self.cp_gradient_threshold_label.pack(side=tk.LEFT)

        # Second row of critical point parameters
        cp_row2_frame = ttk.Frame(cp_frame)
        cp_row2_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Label(cp_row2_frame, text="Min Separation (Å⁻¹):").pack(side=tk.LEFT)
        self.cp_min_separation_var = tk.DoubleVar(value=0.1)
        cp_separation_scale = ttk.Scale(cp_row2_frame, from_=0.05, to=0.5, variable=self.cp_min_separation_var,
                                       orient=tk.HORIZONTAL, length=120, command=self.update_3d_labels)
        cp_separation_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.cp_min_separation_label = ttk.Label(cp_row2_frame, text="0.1")
        self.cp_min_separation_label.pack(side=tk.LEFT, padx=(0, 15))

        ttk.Label(cp_row2_frame, text="Edge Buffer (pixels):").pack(side=tk.LEFT)
        self.cp_edge_buffer_var = tk.DoubleVar(value=3)
        cp_edge_scale = ttk.Scale(cp_row2_frame, from_=1, to=10, variable=self.cp_edge_buffer_var,
                                 orient=tk.HORIZONTAL, length=120, command=self.update_3d_labels)
        cp_edge_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.cp_edge_buffer_label = ttk.Label(cp_row2_frame, text="3")
        self.cp_edge_buffer_label.pack(side=tk.LEFT)

        # Third row for surface threshold
        cp_row3_frame = ttk.Frame(cp_frame)
        cp_row3_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Label(cp_row3_frame, text="Surface Threshold:").pack(side=tk.LEFT)
        self.cp_surface_threshold_var = tk.DoubleVar(value=0.1)
        cp_surface_scale = ttk.Scale(cp_row3_frame, from_=0.01, to=0.5, variable=self.cp_surface_threshold_var,
                                    orient=tk.HORIZONTAL, length=120, command=self.update_3d_labels)
        cp_surface_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.cp_surface_threshold_label = ttk.Label(cp_row3_frame, text="0.10")
        self.cp_surface_threshold_label.pack(side=tk.LEFT, padx=(0, 15))

        ttk.Label(cp_row3_frame, text="(Binary threshold for surface definition)").pack(side=tk.LEFT)

        # 3D Visualization section
        viz_frame = ttk.LabelFrame(parent_frame, text="3D Visualization", padding=10)
        viz_frame.pack(fill=tk.X, pady=(0, 10))

        # Visualization buttons
        viz_buttons_frame = ttk.Frame(viz_frame)
        viz_buttons_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(viz_buttons_frame, text="3D Scatter Plot",
                  command=self.plot_3d_scatter).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(viz_buttons_frame, text="3D Surface",
                  command=self.plot_3d_surface).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(viz_buttons_frame, text="Projections",
                  command=self.plot_projections).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(viz_buttons_frame, text="Critical Points",
                  command=self.find_critical_points).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(viz_buttons_frame, text="Check Data",
                  command=self.check_data_for_critical_points).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(viz_buttons_frame, text="Test Grid",
                  command=self.test_intensity_grid).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(viz_buttons_frame, text="Save Binary Surface",
                  command=self.save_binary_surface).pack(side=tk.LEFT)

    def setup_3d_cross_sections_tab(self):
        """Setup the 3D Cross Sections tab"""
        tab_cross_sections = ttk.Frame(self.notebook)
        self.notebook.add(tab_cross_sections, text="3D Cross Sections")

        # Create scrollable frame for cross sections
        canvas = tk.Canvas(tab_cross_sections)
        scrollbar = ttk.Scrollbar(tab_cross_sections, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Cross sections controls
        self.setup_cross_sections_controls(scrollable_frame)

    def setup_cross_sections_controls(self, parent_frame):
        """Setup cross sections controls"""

        # Info section
        info_frame = ttk.LabelFrame(parent_frame, text="3D Cross Sections Info", padding=10)
        info_frame.pack(fill=tk.X, pady=(0, 10))

        info_text = ("This tab allows you to view cross-sections of binary surfaces.\n"
                    "You can either:\n"
                    "1. Create a binary surface using '3D Surface' in the 3D Analysis tab, then save it\n"
                    "2. Load a previously saved binary surface file using the controls below\n\n"
                    "Multi-slice mode supports:\n"
                    "• Discrete colors for individual slice identification\n"
                    "• Progressive heatmap coloring using rainbow colorscale for continuous visualization")
        ttk.Label(info_frame, text=info_text, wraplength=600, justify=tk.LEFT).pack()

        # Binary surface file loading section
        file_frame = ttk.LabelFrame(parent_frame, text="Binary Surface File", padding=10)
        file_frame.pack(fill=tk.X, pady=(0, 10))

        # File selection
        file_select_frame = ttk.Frame(file_frame)
        file_select_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(file_select_frame, text="Binary Surface File:").pack(side=tk.LEFT)

        self.surface_file_var = tk.StringVar(value="No file selected")
        self.surface_file_label = ttk.Label(file_select_frame, textvariable=self.surface_file_var,
                                          foreground="gray", width=50)
        self.surface_file_label.pack(side=tk.LEFT, padx=(10, 10))

        ttk.Button(file_select_frame, text="Browse...",
                  command=self.browse_surface_file).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(file_select_frame, text="Load File",
                  command=self.load_surface_file).pack(side=tk.LEFT)

        # File info display
        self.file_info_var = tk.StringVar(value="No file loaded")
        file_info_label = ttk.Label(file_frame, textvariable=self.file_info_var,
                                   foreground="blue", font=('TkDefaultFont', 9))
        file_info_label.pack(pady=(5, 0))

        # Cross section type selection
        type_frame = ttk.LabelFrame(parent_frame, text="Cross Section Type", padding=10)
        type_frame.pack(fill=tk.X, pady=(0, 10))

        self.cross_section_type_var = tk.StringVar(value="energy")

        type_radio_frame = ttk.Frame(type_frame)
        type_radio_frame.pack(fill=tk.X)

        ttk.Radiobutton(type_radio_frame, text="Energy Slices (kx vs ky)",
                       variable=self.cross_section_type_var, value="energy",
                       command=self.update_cross_section_controls).pack(side=tk.LEFT, padx=(0, 20))

        ttk.Radiobutton(type_radio_frame, text="kx Slices (Energy vs ky)",
                       variable=self.cross_section_type_var, value="kx",
                       command=self.update_cross_section_controls).pack(side=tk.LEFT, padx=(0, 20))

        ttk.Radiobutton(type_radio_frame, text="ky Slices (Energy vs kx)",
                       variable=self.cross_section_type_var, value="ky",
                       command=self.update_cross_section_controls).pack(side=tk.LEFT)

        # Multi-slice mode selection
        multi_slice_frame = ttk.LabelFrame(parent_frame, text="Multi-Slice Mode", padding=10)
        multi_slice_frame.pack(fill=tk.X, pady=(0, 10))

        self.multi_slice_var = tk.BooleanVar(value=False)
        multi_slice_check = ttk.Checkbutton(multi_slice_frame, text="Enable Multi-Slice Plotting",
                                          variable=self.multi_slice_var, command=self.update_multi_slice_controls)
        multi_slice_check.pack(anchor=tk.W, pady=(0, 5))

        # Progressive heatmap mode
        self.progressive_heatmap_var = tk.BooleanVar(value=False)
        heatmap_check = ttk.Checkbutton(multi_slice_frame, text="Progressive Heatmap Coloring",
                                      variable=self.progressive_heatmap_var)
        heatmap_check.pack(anchor=tk.W, pady=(0, 5))

        # Add explanation for progressive heatmap
        heatmap_info = ttk.Label(multi_slice_frame,
                               text="Uses rainbow colorscale progression based on slice values (replaces discrete colors)",
                               font=('TkDefaultFont', 8), foreground='gray')
        heatmap_info.pack(anchor=tk.W, pady=(0, 5))

        # Contour mode
        self.contour_mode_var = tk.BooleanVar(value=False)
        contour_check = ttk.Checkbutton(multi_slice_frame, text="Contour Mode (Smooth Connected Shapes)",
                                      variable=self.contour_mode_var)
        contour_check.pack(anchor=tk.W, pady=(0, 5))

        # Add explanation for contour mode
        contour_info = ttk.Label(multi_slice_frame,
                               text="Creates smooth connected contours instead of individual points",
                               font=('TkDefaultFont', 8), foreground='gray')
        contour_info.pack(anchor=tk.W, pady=(0, 10))

        # Multi-slice controls (initially hidden)
        self.multi_slice_controls_frame = ttk.Frame(multi_slice_frame)

        # Number of slices
        num_slices_frame = ttk.Frame(self.multi_slice_controls_frame)
        num_slices_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(num_slices_frame, text="Number of Slices:").pack(side=tk.LEFT)
        self.num_slices_var = tk.IntVar(value=5)
        num_slices_scale = ttk.Scale(num_slices_frame, from_=2, to=20, variable=self.num_slices_var,
                                   orient=tk.HORIZONTAL, length=150, command=self.update_multi_slice_labels)
        num_slices_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.num_slices_label = ttk.Label(num_slices_frame, text="5")
        self.num_slices_label.pack(side=tk.LEFT)

        # Range controls
        range_frame = ttk.Frame(self.multi_slice_controls_frame)
        range_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(range_frame, text="Range Min:").pack(side=tk.LEFT)
        self.range_min_var = tk.DoubleVar(value=0.0)
        self.range_min_scale = ttk.Scale(range_frame, from_=-5.0, to=5.0, variable=self.range_min_var,
                                       orient=tk.HORIZONTAL, length=120, command=self.update_multi_slice_labels)
        self.range_min_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.range_min_label = ttk.Label(range_frame, text="0.00")
        self.range_min_label.pack(side=tk.LEFT, padx=(0, 20))

        ttk.Label(range_frame, text="Range Max:").pack(side=tk.LEFT)
        self.range_max_var = tk.DoubleVar(value=1.0)
        self.range_max_scale = ttk.Scale(range_frame, from_=-5.0, to=5.0, variable=self.range_max_var,
                                       orient=tk.HORIZONTAL, length=120, command=self.update_multi_slice_labels)
        self.range_max_scale.pack(side=tk.LEFT, padx=(10, 10))
        self.range_max_label = ttk.Label(range_frame, text="1.00")
        self.range_max_label.pack(side=tk.LEFT)

        # Cross section controls
        controls_frame = ttk.LabelFrame(parent_frame, text="Cross Section Controls", padding=10)
        controls_frame.pack(fill=tk.X, pady=(0, 10))

        # Coordinate offset controls
        offset_frame = ttk.Frame(controls_frame)
        offset_frame.pack(fill=tk.X, pady=(0, 10))

        # kx offset
        ttk.Label(offset_frame, text="kx Offset:").pack(side=tk.LEFT)
        self.cross_kx_offset_var = tk.DoubleVar(value=0.0)
        self.cross_kx_offset_scale = ttk.Scale(offset_frame, from_=-2.0, to=2.0, variable=self.cross_kx_offset_var,
                                              orient=tk.HORIZONTAL, length=100, command=self.update_cross_offset_labels)
        self.cross_kx_offset_scale.pack(side=tk.LEFT, padx=(5, 5))
        self.cross_kx_offset_label = ttk.Label(offset_frame, text="0.00")
        self.cross_kx_offset_label.pack(side=tk.LEFT, padx=(0, 15))

        # ky offset
        ttk.Label(offset_frame, text="ky Offset:").pack(side=tk.LEFT)
        self.cross_ky_offset_var = tk.DoubleVar(value=0.0)
        self.cross_ky_offset_scale = ttk.Scale(offset_frame, from_=-2.0, to=2.0, variable=self.cross_ky_offset_var,
                                              orient=tk.HORIZONTAL, length=100, command=self.update_cross_offset_labels)
        self.cross_ky_offset_scale.pack(side=tk.LEFT, padx=(5, 5))
        self.cross_ky_offset_label = ttk.Label(offset_frame, text="0.00")
        self.cross_ky_offset_label.pack(side=tk.LEFT, padx=(0, 15))

        # Energy offset
        ttk.Label(offset_frame, text="E Offset:").pack(side=tk.LEFT)
        self.cross_energy_offset_var = tk.DoubleVar(value=0.0)
        self.cross_energy_offset_scale = ttk.Scale(offset_frame, from_=-2.0, to=2.0, variable=self.cross_energy_offset_var,
                                                  orient=tk.HORIZONTAL, length=100, command=self.update_cross_offset_labels)
        self.cross_energy_offset_scale.pack(side=tk.LEFT, padx=(5, 5))
        self.cross_energy_offset_label = ttk.Label(offset_frame, text="0.00")
        self.cross_energy_offset_label.pack(side=tk.LEFT)

        # Slice selection
        slice_frame = ttk.Frame(controls_frame)
        slice_frame.pack(fill=tk.X, pady=(0, 10))

        self.slice_label = ttk.Label(slice_frame, text="Energy Slice:")
        self.slice_label.pack(side=tk.LEFT)

        self.slice_var = tk.IntVar(value=0)
        self.slice_scale = ttk.Scale(slice_frame, from_=0, to=100, variable=self.slice_var,
                                    orient=tk.HORIZONTAL, length=300, command=self.update_cross_section_slice)
        self.slice_scale.pack(side=tk.LEFT, padx=(10, 10))

        self.slice_value_label = ttk.Label(slice_frame, text="0.00")
        self.slice_value_label.pack(side=tk.LEFT, padx=(0, 20))

        # Navigation buttons
        nav_frame = ttk.Frame(slice_frame)
        nav_frame.pack(side=tk.LEFT)

        ttk.Button(nav_frame, text="◀◀", command=self.first_slice, width=4).pack(side=tk.LEFT, padx=1)
        ttk.Button(nav_frame, text="◀", command=self.prev_slice, width=4).pack(side=tk.LEFT, padx=1)
        ttk.Button(nav_frame, text="▶", command=self.next_slice, width=4).pack(side=tk.LEFT, padx=1)
        ttk.Button(nav_frame, text="▶▶", command=self.last_slice, width=4).pack(side=tk.LEFT, padx=1)

        # Display options
        display_frame = ttk.LabelFrame(parent_frame, text="Display Options", padding=10)
        display_frame.pack(fill=tk.X, pady=(0, 10))

        display_options_frame = ttk.Frame(display_frame)
        display_options_frame.pack(fill=tk.X)

        self.show_grid_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(display_options_frame, text="Show Grid",
                       variable=self.show_grid_var).pack(side=tk.LEFT, padx=(0, 20))

        self.show_colorbar_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(display_options_frame, text="Show Colorbar",
                       variable=self.show_colorbar_var).pack(side=tk.LEFT, padx=(0, 20))

        self.invert_colors_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(display_options_frame, text="Invert Colors",
                       variable=self.invert_colors_var).pack(side=tk.LEFT, padx=(0, 20))

        self.use_x_axis_coloring_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(display_options_frame, text="Use X-Axis Coloring",
                       variable=self.use_x_axis_coloring_var).pack(side=tk.LEFT)

        # Generate cross section button
        generate_frame = ttk.Frame(parent_frame)
        generate_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(generate_frame, text="Generate Cross Section",
                  command=self.generate_cross_section).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(generate_frame, text="Auto-Play Slices",
                  command=self.auto_play_slices).pack(side=tk.LEFT, padx=(0, 10))

        self.auto_play_button = ttk.Button(generate_frame, text="Stop Auto-Play",
                                          command=self.stop_auto_play, state=tk.DISABLED)
        self.auto_play_button.pack(side=tk.LEFT)

        # Initialize variables for cross sections
        self.binary_surface_data = None
        self.current_slice_data = None
        self.auto_play_active = False
        self.auto_play_timer = None
        self.surface_grids = None

    def update_labels(self):
        """Update all value labels"""
        self.scan_label.config(text=str(int(self.scan_var.get())))
        self.energy_label.config(text=f"{self.energy_var.get():.2f}")
        self.vmin_label.config(text=f"{self.vmin_var.get():.2f}")
        self.vmax_label.config(text=f"{self.vmax_var.get():.2f}")
        self.x_offset_label.config(text=f"{self.x_offset_var.get():.2f}")
        self.y_offset_label.config(text=f"{self.y_offset_var.get():.2f}")
        self.kernel_label.config(text=str(int(self.kernel_var.get())))
        self.contour_label.config(text=str(int(self.contour_var.get())))
        self.peak_threshold_label.config(text=f"{self.peak_threshold_var.get():.2f}")
        self.neighbor_label.config(text=str(int(self.neighbor_var.get())))
        self.smooth_label.config(text=f"{self.smooth_var.get():.2f}")
        self.canny_sigma_label.config(text=f"{self.canny_sigma_var.get():.2f}")
        self.canny_low_label.config(text=f"{self.canny_low_var.get():.2f}")
        self.canny_high_label.config(text=f"{self.canny_high_var.get():.2f}")
        self.euler_threshold_label.config(text=f"{self.euler_threshold_var.get():.2f}")

    def update_3d_labels(self, event=None):
        """Update 3D parameter labels"""
        if hasattr(self, 'threshold_3d_label'):
            self.threshold_3d_label.config(text=f"{self.threshold_3d_var.get():.2f}")
        if hasattr(self, 'window_size_label'):
            self.window_size_label.config(text=str(int(self.window_size_var.get())))
        if hasattr(self, 'energy_step_label'):
            self.energy_step_label.config(text=f"{self.energy_step_var.get():.3f}")
        if hasattr(self, 'grid_size_label'):
            self.grid_size_label.config(text=str(int(self.grid_size_var.get())))
        if hasattr(self, 'smoothing_3d_label'):
            self.smoothing_3d_label.config(text=f"{self.smoothing_3d_var.get():.2f}")
        if hasattr(self, 'surface_threshold_label'):
            self.surface_threshold_label.config(text=f"{self.surface_threshold_var.get():.2f}")
        if hasattr(self, 'kx_offset_label'):
            self.kx_offset_label.config(text=f"{self.kx_offset_var.get():.2f}")
        if hasattr(self, 'ky_offset_label'):
            self.ky_offset_label.config(text=f"{self.ky_offset_var.get():.2f}")
        if hasattr(self, 'energy_offset_label'):
            self.energy_offset_label.config(text=f"{self.energy_offset_var.get():.2f}")
        if hasattr(self, 'energy_min_label'):
            self.energy_min_label.config(text=f"{self.energy_min_var.get():.2f}")
        if hasattr(self, 'energy_max_label'):
            self.energy_max_label.config(text=f"{self.energy_max_var.get():.2f}")
        if hasattr(self, 'kx_min_label'):
            self.kx_min_label.config(text=f"{self.kx_min_var.get():.2f}")
        if hasattr(self, 'kx_max_label'):
            self.kx_max_label.config(text=f"{self.kx_max_var.get():.2f}")
        if hasattr(self, 'ky_min_label'):
            self.ky_min_label.config(text=f"{self.ky_min_var.get():.2f}")
        if hasattr(self, 'ky_max_label'):
            self.ky_max_label.config(text=f"{self.ky_max_var.get():.2f}")
        if hasattr(self, 'yellow_plane_energy_label'):
            self.yellow_plane_energy_label.config(text=f"{self.yellow_plane_energy_var.get():.2f}")
        if hasattr(self, 'red_plane_energy_label'):
            self.red_plane_energy_label.config(text=f"{self.red_plane_energy_var.get():.2f}")

        # Update critical point parameter labels
        if hasattr(self, 'cp_intensity_threshold_label'):
            self.cp_intensity_threshold_label.config(text=f"{self.cp_intensity_threshold_var.get():.3f}")
        if hasattr(self, 'cp_gradient_threshold_label'):
            self.cp_gradient_threshold_label.config(text=f"{self.cp_gradient_threshold_var.get():.4f}")
        if hasattr(self, 'cp_min_separation_label'):
            self.cp_min_separation_label.config(text=f"{self.cp_min_separation_var.get():.3f}")
        if hasattr(self, 'cp_edge_buffer_label'):
            self.cp_edge_buffer_label.config(text=f"{int(self.cp_edge_buffer_var.get())}")
        if hasattr(self, 'cp_surface_threshold_label'):
            self.cp_surface_threshold_label.config(text=f"{self.cp_surface_threshold_var.get():.2f}")

        # Update cross section offset labels
        if hasattr(self, 'cross_kx_offset_label'):
            self.cross_kx_offset_label.config(text=f"{self.cross_kx_offset_var.get():.2f}")
        if hasattr(self, 'cross_ky_offset_label'):
            self.cross_ky_offset_label.config(text=f"{self.cross_ky_offset_var.get():.2f}")
        if hasattr(self, 'cross_energy_offset_label'):
            self.cross_energy_offset_label.config(text=f"{self.cross_energy_offset_var.get():.2f}")

        # Save configuration if data folder is loaded
        if self.current_data_folder:
            # Cancel any pending save
            if hasattr(self, '_config_save_timer'):
                self.root.after_cancel(self._config_save_timer)
            # Schedule save after 1 second delay to avoid too frequent saves
            self._config_save_timer = self.root.after(1000, self.save_current_config)

    def on_setting_change(self, event=None):
        """Handle any setting change"""
        self.update_labels()
        # Update settings dictionary
        self.settings.update({
            'mode': self.mode_var.get(),
            'scan_number': int(self.scan_var.get()),
            'E_binding': self.energy_var.get(),
            'vmin': self.vmin_var.get(),
            'vmax': self.vmax_var.get(),
            'kernel_size': int(self.kernel_var.get()),
            'x_offset': self.x_offset_var.get(),
            'y_offset': self.y_offset_var.get(),
            'colorscale': self.colorscale_var.get(),
            'show_peaks': self.show_peaks_var.get(),
            'use_contours': self.use_contours_var.get(),
            'contour_levels': int(self.contour_var.get()),
            'peak_threshold': self.peak_threshold_var.get(),
            'neighborhood_size': int(self.neighbor_var.get()),
            'smoothing_sigma': self.smooth_var.get(),
            'show_edges': self.show_edges_var.get(),
            'show_components': self.show_components_var.get(),
            'canny_sigma': self.canny_sigma_var.get(),
            'canny_low': self.canny_low_var.get(),
            'canny_high': self.canny_high_var.get(),
            'euler_threshold': self.euler_threshold_var.get(),
            'show_euler_points': self.show_euler_var.get(),
            'show_critical_points': self.show_critical_var.get(),
            'live_update': self.live_update_var.get(),
            'open_in_browser': self.open_in_browser_var.get(),
            'use_plot_window': self.use_plot_window_var.get(),
            'square_mode': self.square_mode_var.get(),
            'hide_titles': self.hide_titles_var.get()
        })

        # Save configuration if data folder is loaded
        if self.current_data_folder:
            # Cancel any pending save
            if hasattr(self, '_config_save_timer'):
                self.root.after_cancel(self._config_save_timer)
            # Schedule save after 1 second delay to avoid too frequent saves
            self._config_save_timer = self.root.after(1000, self.save_current_config)

        # Auto-generate plot if live update is enabled and data is loaded
        # Add a small delay to prevent too many rapid updates
        if self.settings['live_update'] and self.data_loader.data_proc:
            # Cancel any pending update
            if hasattr(self, '_update_timer'):
                self.root.after_cancel(self._update_timer)
            # Schedule update after 500ms delay
            self._update_timer = self.root.after(500, self._delayed_plot_update)

    def _delayed_plot_update(self):
        """Delayed plot update to prevent too many rapid updates"""
        try:
            self.generate_plot()
        except Exception as e:
            self.status_label.config(text=f"❌ Live update error: {str(e)}", foreground="red")

    def load_data(self):
        """Load PXT data files"""
        self.log_message("📁 Starting data loading process...", "INFO")
        self.status_label.config(text="Loading data...", foreground="orange")
        self.root.update()

        try:
            self.log_message("🔍 Opening file dialog for PXT files", "DEBUG")
            success, message = self.data_loader.load_pxt_files()

            if success:
                self.log_message(f"✅ Data loading successful: {message}", "SUCCESS")

                # Store current data folder for configuration management
                self.current_data_folder = self.data_loader.data_folder
                self.log_message(f"📂 Data folder: {self.current_data_folder}", "DEBUG")

                # Try to load existing configuration for this folder
                config_loaded = self.load_config_for_folder(self.current_data_folder)

                # Update scan range
                max_scans = len(self.data_loader.data_proc) - 1
                self.scan_scale.config(to=max_scans)
                self.log_message(f"📊 Updated scan range: 0 to {max_scans}", "DEBUG")

                # Update energy range
                E_min, E_max = self.data_loader.get_binding_energy_range()
                self.energy_scale.config(from_=E_min, to=E_max)
                if not config_loaded:  # Only set default if no config was loaded
                    self.energy_var.set((E_min + E_max) / 2)
                self.log_message(f"⚡ Energy range: {E_min:.3f} to {E_max:.3f} eV", "DEBUG")
                self.log_message(f"🎯 Current energy setting: {self.energy_var.get():.3f} eV", "DEBUG")

                # Auto-adjust 3D ranges based on data if no config was loaded
                if not config_loaded and hasattr(self, 'energy_min_var'):
                    self.auto_adjust_3d_ranges()

                # Log data statistics
                total_points = sum(len(df) * len(df.columns) for df in self.data_loader.data_proc)
                self.log_message(f"📈 Loaded {len(self.data_loader.data_proc)} scans with {total_points:,} total data points", "INFO")

                self.status_label.config(text=f"✅ {message}", foreground="green")
            else:
                self.log_message(f"❌ Data loading failed: {message}", "ERROR")
                self.status_label.config(text=f"❌ {message}", foreground="red")

        except Exception as e:
            error_msg = f"Error during data loading: {str(e)}"
            self.log_message(f"❌ {error_msg}", "ERROR")
            self.status_label.config(text=f"❌ Error: {str(e)}", foreground="red")

    def auto_adjust_3d_ranges(self):
        """Automatically adjust 3D range parameters based on loaded data"""
        try:
            self.log_message("🔧 Auto-adjusting 3D range parameters based on data", "INFO")

            # Get energy range from data
            E_min, E_max = self.data_loader.get_binding_energy_range()
            energy_margin = (E_max - E_min) * 0.1  # 10% margin

            self.energy_min_var.set(E_min - energy_margin)
            self.energy_max_var.set(E_max + energy_margin)
            self.log_message(f"⚡ Auto-set energy range: {E_min - energy_margin:.2f} to {E_max + energy_margin:.2f} eV", "DEBUG")

            # Estimate k-space ranges (these are rough estimates)
            # For typical ARPES data, kx and ky ranges are usually around ±0.5 to ±2.0 Å⁻¹
            # We'll use conservative estimates that can be adjusted by the user

            # Check if we have any processed 3D data to get better estimates
            if hasattr(self.analyzer_3d, 'processed_data') and self.analyzer_3d.processed_data is not None:
                df = self.analyzer_3d.processed_data.reset_index()
                kx_min, kx_max = df['kx'].min(), df['kx'].max()
                ky_min, ky_max = df['ky'].min(), df['ky'].max()

                kx_margin = (kx_max - kx_min) * 0.1
                ky_margin = (ky_max - ky_min) * 0.1

                self.kx_min_var.set(kx_min - kx_margin)
                self.kx_max_var.set(kx_max + kx_margin)
                self.ky_min_var.set(ky_min - ky_margin)
                self.ky_max_var.set(ky_max + ky_margin)

                self.log_message(f"🔄 Auto-set kx range: {kx_min - kx_margin:.2f} to {kx_max + kx_margin:.2f} Å⁻¹", "DEBUG")
                self.log_message(f"🔄 Auto-set ky range: {ky_min - ky_margin:.2f} to {ky_max + ky_margin:.2f} Å⁻¹", "DEBUG")
            else:
                # Use default k-space ranges
                self.kx_min_var.set(-1.0)
                self.kx_max_var.set(1.0)
                self.ky_min_var.set(-1.0)
                self.ky_max_var.set(1.0)
                self.log_message("🔄 Using default k-space ranges: ±1.0 Å⁻¹", "DEBUG")

            # Update labels
            if hasattr(self, 'update_3d_labels'):
                self.update_3d_labels()

            self.log_message("✅ 3D ranges auto-adjusted successfully", "SUCCESS")

        except Exception as e:
            self.log_message(f"❌ Error auto-adjusting 3D ranges: {str(e)}", "ERROR")

    def generate_plot(self):
        """Generate and display plot"""
        if not self.data_loader.data_proc:
            self.status_label.config(text="❌ No data loaded", foreground="red")
            self.log_message("❌ Cannot generate plot: No data loaded", "ERROR")
            return

        self.log_message(f"🎨 Starting plot generation in {self.settings['mode']} mode", "INFO")
        self.status_label.config(text="Generating plot...", foreground="orange")
        self.root.update()

        try:
            # Log plot parameters
            self.log_message(f"📊 Plot parameters: scan={self.settings['scan_number']}, "
                           f"energy={self.settings['E_binding']:.3f} eV, "
                           f"colorscale={self.settings['colorscale']}", "DEBUG")

            if self.settings['mode'] == 'E vs kx':
                fig, message = self.plotter.plot_E_vs_kx(
                    scan_number=self.settings['scan_number'],
                    vmin=self.settings['vmin'],
                    vmax=self.settings['vmax'],
                    kernel_size=self.settings['kernel_size'],
                    x_offset=self.settings['x_offset'],
                    y_offset=self.settings['y_offset'],
                    colorscale=self.settings['colorscale'],
                    show_peaks=self.settings['show_peaks'],
                    peak_threshold=self.settings['peak_threshold'],
                    neighborhood_size=self.settings['neighborhood_size'],
                    smoothing_sigma=self.settings['smoothing_sigma'],
                    show_edges=self.settings['show_edges'],
                    show_components=self.settings['show_components'],
                    canny_sigma=self.settings['canny_sigma'],
                    canny_low=self.settings['canny_low'],
                    canny_high=self.settings['canny_high'],
                    euler_threshold=self.settings['euler_threshold'],
                    show_euler_points=self.settings['show_euler_points'],
                    show_critical_points=self.settings['show_critical_points'],
                    square_mode=self.settings['square_mode'],
                    hide_titles=self.settings['hide_titles']
                )
            elif self.settings['mode'] == 'kx vs ky':
                fig, message = self.plotter.plot_kx_vs_ky(
                    E_binding=self.settings['E_binding'],
                    vmin=self.settings['vmin'],
                    vmax=self.settings['vmax'],
                    kernel_size=self.settings['kernel_size'],
                    x_offset=self.settings['x_offset'],
                    y_offset=self.settings['y_offset'],
                    colorscale=self.settings['colorscale'],
                    use_contours=self.settings['use_contours'],
                    contour_levels=self.settings['contour_levels'],
                    show_peaks=self.settings['show_peaks'],
                    peak_threshold=self.settings['peak_threshold'],
                    neighborhood_size=self.settings['neighborhood_size'],
                    smoothing_sigma=self.settings['smoothing_sigma'],
                    show_edges=self.settings['show_edges'],
                    show_components=self.settings['show_components'],
                    canny_sigma=self.settings['canny_sigma'],
                    canny_low=self.settings['canny_low'],
                    canny_high=self.settings['canny_high'],
                    euler_threshold=self.settings['euler_threshold'],
                    show_euler_points=self.settings['show_euler_points'],
                    show_critical_points=self.settings['show_critical_points'],
                    square_mode=self.settings['square_mode'],
                    hide_titles=self.settings['hide_titles']
                )
            elif self.settings['mode'] == 'kx vs kz':
                fig, message = self.plotter.plot_kx_vs_kz(
                    E_binding=self.settings['E_binding'],
                    vmin=self.settings['vmin'],
                    vmax=self.settings['vmax'],
                    kernel_size=self.settings['kernel_size'],
                    x_offset=self.settings['x_offset'],
                    y_offset=self.settings['y_offset'],
                    colorscale=self.settings['colorscale'],
                    use_contours=self.settings['use_contours'],
                    contour_levels=self.settings['contour_levels'],
                    show_peaks=self.settings['show_peaks'],
                    peak_threshold=self.settings['peak_threshold'],
                    neighborhood_size=self.settings['neighborhood_size'],
                    smoothing_sigma=self.settings['smoothing_sigma'],
                    show_edges=self.settings['show_edges'],
                    show_components=self.settings['show_components'],
                    canny_sigma=self.settings['canny_sigma'],
                    canny_low=self.settings['canny_low'],
                    canny_high=self.settings['canny_high'],
                    euler_threshold=self.settings['euler_threshold'],
                    show_euler_points=self.settings['show_euler_points'],
                    show_critical_points=self.settings['show_critical_points'],
                    square_mode=self.settings['square_mode'],
                    hide_titles=self.settings['hide_titles']
                )
            else:
                self.status_label.config(text="❌ Plot mode not implemented", foreground="red")
                return

            if fig:
                # Always write to the same file
                fig.write_html(self.plot_file.name)

                # Update plot window if it exists and is enabled
                if self.settings['use_plot_window']:
                    # Auto-open plot window if it's not already open
                    if not (self.plot_window.window and self.plot_window.window.winfo_exists()):
                        self.plot_window.show()

                    self.plot_window.update_plot(self.plot_file.name)
                    self.status_label.config(text="✅ Plot generated and updated in plot window", foreground="green")

                # Also open in browser if requested
                if self.settings['open_in_browser']:
                    try:
                        # Try to reuse existing browser window/tab
                        browser = webbrowser.get()
                        browser.open('file://' + self.plot_file.name, new=0)
                    except:
                        webbrowser.open('file://' + self.plot_file.name, new=0)
                    self.status_label.config(text="✅ Plot generated and opened in browser", foreground="green")

                # If neither option is selected, just save the file
                if not self.settings['use_plot_window'] and not self.settings['open_in_browser']:
                    self.status_label.config(text="✅ Plot generated and saved to file", foreground="green")
            else:
                self.status_label.config(text=f"❌ {message}", foreground="red")

        except Exception as e:
            self.status_label.config(text=f"❌ Error: {str(e)}", foreground="red")

    def show_plot_window(self):
        """Show the dedicated plot window"""
        self.plot_window.show()
        if os.path.exists(self.plot_file.name):
            self.plot_window.update_plot(self.plot_file.name)
            self.status_label.config(text="✅ Plot window opened", foreground="green")
        else:
            self.status_label.config(text="📊 Plot window opened. Generate a plot to view it.", foreground="blue")

    def export_plot(self):
        """Export current plot"""
        if not self.plotter.current_fig:
            self.status_label.config(text="❌ No plot to export", foreground="red")
            return

        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".html",
                filetypes=[("HTML files", "*.html"), ("All files", "*.*")],
                title="Save plot as..."
            )

            if filename:
                self.plotter.current_fig.write_html(filename)
                self.status_label.config(text=f"✅ Plot exported to {Path(filename).name}", foreground="green")

        except Exception as e:
            self.status_label.config(text=f"❌ Export error: {str(e)}", foreground="red")

    def load_processed_data(self):
        """Load previously processed 3D data from .npy file"""
        self.log_message("📂 Opening file dialog to load processed 3D data", "INFO")
        try:
            filename = filedialog.askopenfilename(
                title="Load Processed Data",
                filetypes=[("NumPy files", "*.npy"), ("All files", "*.*")]
            )

            if filename:
                self.log_message(f"📁 Loading processed data from: {Path(filename).name}", "INFO")
                self.update_progress(20, "Loading processed data...")

                # Load data from numpy file
                self.log_message("🔄 Reading numpy array from file", "DEBUG")
                loaded_data = np.load(filename)
                self.log_message(f"📊 Loaded array shape: {loaded_data.shape}", "DEBUG")

                plot_data = pd.DataFrame(loaded_data, columns=['binding_energy', 'kx', 'ky', 'intensity'])
                self.log_message(f"📈 Created DataFrame with {len(plot_data):,} data points", "DEBUG")

                # Set as processed data in analyzer
                self.analyzer_3d.processed_data = plot_data.set_index(['binding_energy', 'kx', 'ky'])

                # Log data statistics
                non_zero_points = len(plot_data[plot_data['intensity'] > 0])
                energy_range = (plot_data['binding_energy'].min(), plot_data['binding_energy'].max())
                kx_range = (plot_data['kx'].min(), plot_data['kx'].max())
                ky_range = (plot_data['ky'].min(), plot_data['ky'].max())

                self.log_message(f"📊 Data statistics: {non_zero_points:,} non-zero points", "INFO")
                self.log_message(f"⚡ Energy range: {energy_range[0]:.3f} to {energy_range[1]:.3f} eV", "DEBUG")
                self.log_message(f"🔄 kx range: {kx_range[0]:.3f} to {kx_range[1]:.3f} Å⁻¹", "DEBUG")
                self.log_message(f"🔄 ky range: {ky_range[0]:.3f} to {ky_range[1]:.3f} Å⁻¹", "DEBUG")

                # Auto-adjust 3D ranges based on loaded processed data
                self.auto_adjust_3d_ranges_from_processed_data(plot_data)

                self.update_progress(100, "Data loaded successfully")
                self.status_label.config(text=f"✅ Loaded processed data: {Path(filename).name}", foreground="green")
                self.log_message("✅ Processed data loaded successfully - ready for 3D visualization", "SUCCESS")

        except Exception as e:
            error_msg = f"Error loading processed data: {str(e)}"
            self.status_label.config(text=f"❌ {error_msg}", foreground="red")
            self.log_message(f"❌ {error_msg}", "ERROR")
            self.update_progress(0, "Error occurred")

    def auto_adjust_3d_ranges_from_processed_data(self, data_df):
        """Auto-adjust 3D ranges based on processed data"""
        try:
            self.log_message("🔧 Auto-adjusting 3D ranges based on processed data", "INFO")

            # Get actual data ranges
            energy_min, energy_max = data_df['binding_energy'].min(), data_df['binding_energy'].max()
            kx_min, kx_max = data_df['kx'].min(), data_df['kx'].max()
            ky_min, ky_max = data_df['ky'].min(), data_df['ky'].max()

            # Add small margins
            energy_margin = (energy_max - energy_min) * 0.05
            kx_margin = (kx_max - kx_min) * 0.05 if kx_max != kx_min else 0.1
            ky_margin = (ky_max - ky_min) * 0.05 if ky_max != ky_min else 0.1

            # Set ranges with margins
            self.energy_min_var.set(energy_min - energy_margin)
            self.energy_max_var.set(energy_max + energy_margin)
            self.kx_min_var.set(kx_min - kx_margin)
            self.kx_max_var.set(kx_max + kx_margin)
            self.ky_min_var.set(ky_min - ky_margin)
            self.ky_max_var.set(ky_max + ky_margin)

            self.log_message(f"⚡ Auto-set energy range: {energy_min - energy_margin:.2f} to {energy_max + energy_margin:.2f} eV", "DEBUG")
            self.log_message(f"🔄 Auto-set kx range: {kx_min - kx_margin:.2f} to {kx_max + kx_margin:.2f} Å⁻¹", "DEBUG")
            self.log_message(f"🔄 Auto-set ky range: {ky_min - ky_margin:.2f} to {ky_max + ky_margin:.2f} Å⁻¹", "DEBUG")

            # Update labels
            if hasattr(self, 'update_3d_labels'):
                self.update_3d_labels()

            self.log_message("✅ 3D ranges auto-adjusted from processed data", "SUCCESS")

        except Exception as e:
            self.log_message(f"❌ Error auto-adjusting ranges from processed data: {str(e)}", "ERROR")

    def save_processed_data(self):
        """Save processed 3D data to .npy file"""
        if self.analyzer_3d.processed_data is None:
            self.log_message("❌ No processed data available to save", "ERROR")
            messagebox.showwarning("No Data", "No processed data to save. Process data first.")
            return

        self.log_message("💾 Opening file dialog to save processed 3D data", "INFO")
        try:
            filename = filedialog.asksaveasfilename(
                title="Save Processed Data",
                defaultextension=".npy",
                filetypes=[("NumPy files", "*.npy"), ("All files", "*.*")]
            )

            if filename:
                self.log_message(f"💾 Saving processed data to: {Path(filename).name}", "INFO")
                self.update_progress(50, "Saving processed data...")

                # Get data statistics before saving
                data_df = self.analyzer_3d.processed_data.reset_index()
                total_points = len(data_df)
                non_zero_points = len(data_df[data_df['intensity'] > 0])
                file_size_mb = data_df.memory_usage(deep=True).sum() / (1024 * 1024)

                self.log_message(f"📊 Saving {total_points:,} total points ({non_zero_points:,} non-zero)", "DEBUG")
                self.log_message(f"💽 Estimated file size: {file_size_mb:.1f} MB", "DEBUG")

                # Save data to numpy file
                data_to_save = data_df.to_numpy()
                np.save(filename, data_to_save)

                # Verify saved file
                actual_size_mb = Path(filename).stat().st_size / (1024 * 1024)
                self.log_message(f"✅ File saved successfully: {actual_size_mb:.1f} MB", "DEBUG")

                self.update_progress(100, "Data saved successfully")
                self.status_label.config(text=f"✅ Saved processed data: {Path(filename).name}", foreground="green")
                self.log_message("✅ Processed data saved successfully", "SUCCESS")

        except Exception as e:
            error_msg = f"Error saving processed data: {str(e)}"
            self.status_label.config(text=f"❌ {error_msg}", foreground="red")
            self.log_message(f"❌ {error_msg}", "ERROR")
            self.update_progress(0, "Error occurred")

    def process_3d_data(self):
        """Process data for 3D analysis"""
        if not self.data_loader.data_proc:
            self.log_message("❌ No raw data loaded for 3D processing", "ERROR")
            messagebox.showwarning("No Data", "Please load PXT data files first.")
            return

        self.log_message("🔄 Starting 3D data processing pipeline", "INFO")
        try:
            # Log processing parameters
            threshold = self.threshold_3d_var.get()
            window_size = int(self.window_size_var.get())
            suppress_at = self.suppress_var.get()

            self.log_message(f"⚙️ Processing parameters: threshold={threshold:.3f}, "
                           f"window_size={window_size}, suppress_at={suppress_at}", "DEBUG")

            self.update_progress(0, "Starting 3D data processing...")

            # Log data input statistics
            total_scans = len(self.data_loader.data_proc)
            self.log_message(f"📊 Processing {total_scans} scans for 3D analysis", "INFO")

            result, message = self.analyzer_3d.process_threshold_and_filter(
                threshold=threshold,
                window_size=window_size,
                suppress_at=suppress_at
            )

            if result is not None:
                # Log processing results
                if hasattr(self.analyzer_3d, 'processed_data') and self.analyzer_3d.processed_data is not None:
                    processed_df = self.analyzer_3d.processed_data.reset_index()
                    total_points = len(processed_df)
                    non_zero_points = len(processed_df[processed_df['intensity'] > 0])

                    self.log_message(f"✅ 3D processing complete: {total_points:,} total points", "SUCCESS")
                    self.log_message(f"📈 Non-zero intensity points: {non_zero_points:,} ({non_zero_points/total_points*100:.1f}%)", "INFO")

                    # Log data ranges
                    energy_range = (processed_df['binding_energy'].min(), processed_df['binding_energy'].max())
                    kx_range = (processed_df['kx'].min(), processed_df['kx'].max())
                    ky_range = (processed_df['ky'].min(), processed_df['ky'].max())

                    self.log_message(f"⚡ Energy range: {energy_range[0]:.3f} to {energy_range[1]:.3f} eV", "DEBUG")
                    self.log_message(f"🔄 kx range: {kx_range[0]:.3f} to {kx_range[1]:.3f} Å⁻¹", "DEBUG")
                    self.log_message(f"🔄 ky range: {ky_range[0]:.3f} to {ky_range[1]:.3f} Å⁻¹", "DEBUG")

                self.status_label.config(text="✅ 3D data processed successfully", foreground="green")
                self.log_message("🎯 3D data ready for visualization and analysis", "SUCCESS")
            else:
                self.status_label.config(text=f"❌ {message}", foreground="red")
                self.log_message(f"❌ 3D processing failed: {message}", "ERROR")

        except Exception as e:
            error_msg = f"Error processing 3D data: {str(e)}"
            self.status_label.config(text=f"❌ {error_msg}", foreground="red")
            self.log_message(f"❌ {error_msg}", "ERROR")
            self.update_progress(0, "Error occurred")

    def plot_3d_scatter(self):
        """Create 3D scatter plot"""
        if self.analyzer_3d.processed_data is None:
            messagebox.showwarning("No Data", "Please process 3D data first.")
            return

        try:
            self.update_progress(50, "Creating 3D scatter plot...")

            # Create 3D scatter plot using plotly
            data = self.analyzer_3d.processed_data.reset_index()
            data = data[data['intensity'] > 0]

            # Downsample for performance
            if len(data) > 10000:
                data = data.sample(n=10000)

            labels = create_latex_labels()
            fig = go.Figure(data=go.Scatter3d(
                x=data['kx'],
                y=data['ky'],
                z=data['binding_energy'],
                mode='markers',
                marker=dict(
                    size=2,
                    color=data['intensity'],
                    colorscale='Viridis',
                    opacity=0.8,
                    colorbar=dict(title=labels['intensity'])
                ),
                hovertemplate='k<sub>x</sub>: %{x:.3f} Å⁻¹<br>' +
                             'k<sub>y</sub>: %{y:.3f} Å⁻¹<br>' +
                             'E<sub>b</sub>: %{z:.3f} eV<br>' +
                             'Intensity: %{marker.color:.3f}<extra></extra>'
            ))

            layout_style = create_professional_layout()
            title_style = create_professional_title_style()

            fig.update_layout(
                title=dict(
                    text='3D ARPES Data Scatter Plot',
                    **title_style
                ),
                scene=dict(
                    xaxis_title=labels['kx'],
                    yaxis_title=labels['ky'],
                    zaxis_title=labels['E_binding'],
                    xaxis=dict(
                        tickfont=dict(family='Times New Roman, serif', size=14, color='black'),  # Slightly larger than original 12
                        title_font=dict(family='Times New Roman, serif', size=16, color='black'),  # Slightly larger than original 14
                        showline=True,
                        linewidth=2,
                        linecolor='black',
                        mirror=True,
                        ticks='outside',
                        tickwidth=2,
                        tickcolor='black',
                        showgrid=True,
                        gridwidth=1,
                        gridcolor='lightgray'
                    ),
                    yaxis=dict(
                        tickfont=dict(family='Times New Roman, serif', size=14, color='black'),  # Slightly larger than original 12
                        title_font=dict(family='Times New Roman, serif', size=16, color='black'),  # Slightly larger than original 14
                        showline=True,
                        linewidth=2,
                        linecolor='black',
                        mirror=True,
                        ticks='outside',
                        tickwidth=2,
                        tickcolor='black',
                        showgrid=True,
                        gridwidth=1,
                        gridcolor='lightgray'
                    ),
                    zaxis=dict(
                        tickfont=dict(family='Times New Roman, serif', size=14, color='black'),  # Slightly larger than original 12
                        title_font=dict(family='Times New Roman, serif', size=16, color='black'),  # Slightly larger than original 14
                        showline=True,
                        linewidth=2,
                        linecolor='black',
                        mirror=True,
                        ticks='outside',
                        tickwidth=2,
                        tickcolor='black',
                        showgrid=True,
                        gridwidth=1,
                        gridcolor='lightgray'
                    )
                ),
                width=900,
                height=700,
                **layout_style
            )

            # Save and display
            fig.write_html(self.plot_file.name)
            self.plot_window.update_plot(self.plot_file.name)
            if not (self.plot_window.window and self.plot_window.window.winfo_exists()):
                self.plot_window.show()

            self.update_progress(100, "3D scatter plot created")
            self.status_label.config(text="✅ 3D scatter plot created", foreground="green")

        except Exception as e:
            self.status_label.config(text=f"❌ Error creating 3D plot: {str(e)}", foreground="red")
            self.update_progress(0, "Error occurred")

    def plot_3d_surface(self):
        """Create 3D surface plot using ThesisAnalysis2.ipynb method"""
        if self.analyzer_3d.processed_data is None:
            messagebox.showwarning("No Data", "Please process 3D data first or load processed data.")
            return

        try:
            self.log_message("🏔️ Starting 3D surface plot generation (ThesisAnalysis2.ipynb method)", "INFO")
            self.update_progress(10, "Creating 3D surface plot...")

            # Get parameters from GUI
            energy_step = self.energy_step_var.get()
            grid_size = int(self.grid_size_var.get())
            smoothing_sigma = self.smoothing_3d_var.get()
            surface_threshold = self.surface_threshold_var.get()
            single_color_mode = self.single_color_var.get()

            # Range parameters
            energy_range = (self.energy_min_var.get(), self.energy_max_var.get())
            kx_range = (self.kx_min_var.get(), self.kx_max_var.get())
            ky_range = (self.ky_min_var.get(), self.ky_max_var.get())

            # Offset parameters
            kx_offset = self.kx_offset_var.get()
            ky_offset = self.ky_offset_var.get()
            energy_offset = self.energy_offset_var.get()

            # Plane parameters
            show_yellow_plane = self.show_yellow_plane_var.get()
            yellow_plane_energy = self.yellow_plane_energy_var.get() if show_yellow_plane else None
            show_red_plane = self.show_red_plane_var.get()
            red_plane_energy = self.red_plane_energy_var.get() if show_red_plane else None

            # Use the notebook-style surface creation method
            fig, message = self.analyzer_3d.create_energy_surface_notebook_style(
                energy_step=energy_step,
                grid_size=grid_size,
                intensity_threshold=0.01,
                smoothing_sigma=smoothing_sigma,
                surface_intensity_threshold=surface_threshold,
                single_color_mode=single_color_mode,
                kx_range=kx_range,
                ky_range=ky_range,
                energy_range=energy_range,
                kx_offset=kx_offset,
                ky_offset=ky_offset,
                energy_offset=energy_offset,
                show_yellow_plane=show_yellow_plane,
                yellow_plane_energy=yellow_plane_energy,
                show_red_plane=show_red_plane,
                red_plane_energy=red_plane_energy
            )

            if fig is None:
                self.log_message(f"❌ Failed to create surface: {message}", "ERROR")
                self.status_label.config(text=f"❌ {message}", foreground="red")
                return

            # Log all parameters
            self.log_message(f"⚙️ Surface parameters: energy_step={energy_step:.3f}, grid_size={grid_size}, "
                           f"smoothing_sigma={smoothing_sigma:.2f}, threshold={surface_threshold:.3f}", "DEBUG")
            self.log_message(f"📊 Energy range: {energy_range[0]:.2f} to {energy_range[1]:.2f} eV", "DEBUG")
            self.log_message(f"📊 kx range: {kx_range[0]:.2f} to {kx_range[1]:.2f} Å⁻¹", "DEBUG")
            self.log_message(f"📊 ky range: {ky_range[0]:.2f} to {ky_range[1]:.2f} Å⁻¹", "DEBUG")
            self.log_message(f"🎨 Visualization: single_color={single_color_mode}, "
                           f"yellow_plane={show_yellow_plane}, red_plane={show_red_plane}", "DEBUG")

            # Save and display
            self.log_message(f"💾 Saving plot to: {Path(self.plot_file.name).name}", "DEBUG")
            fig.write_html(self.plot_file.name)
            self.plot_window.update_plot(self.plot_file.name)
            if not (self.plot_window.window and self.plot_window.window.winfo_exists()):
                self.plot_window.show()

            self.update_progress(100, "3D surface plot created")
            self.status_label.config(text="✅ 3D surface plot created", foreground="green")
            self.log_message("🎉 3D surface plot generation completed successfully", "SUCCESS")

        except Exception as e:
            error_msg = f"Error creating 3D surface: {str(e)}"
            self.status_label.config(text=f"❌ {error_msg}", foreground="red")
            self.log_message(f"❌ {error_msg}", "ERROR")
            self.update_progress(0, "Error occurred")

    def _add_reference_plane(self, fig, z_value, kx_range, ky_range, color, opacity):
        """Add a reference plane to the 3D plot"""
        x_plane = [kx_range[0], kx_range[1], kx_range[1], kx_range[0]]
        y_plane = [ky_range[0], ky_range[0], ky_range[1], ky_range[1]]
        z_plane = [z_value] * 4

        fig.add_trace(go.Mesh3d(
            x=x_plane + x_plane,  # Duplicate for both sides
            y=y_plane + y_plane,
            z=z_plane + z_plane,
            i=[0, 0, 0, 1],
            j=[1, 2, 3, 2],
            k=[2, 3, 1, 3],
            color=color,
            opacity=opacity,
            name=f'{color.title()} Plane (E={z_value:.2f})'
        ))

    def plot_projections(self):
        """Create projection plots using the exact ThesisAnalysis2.ipynb integrated projections method"""
        if self.analyzer_3d.processed_data is None:
            self.log_message("❌ No processed data available for projections", "ERROR")
            messagebox.showwarning("No Data", "Please process 3D data first or load processed data.")
            return

        try:
            self.log_message("📊 Starting integrated projections generation (ThesisAnalysis2.ipynb method)", "INFO")
            self.update_progress(10, "Initializing projections...")

            # Get parameters from GUI
            grid_size = min(int(self.grid_size_var.get()), 120)  # Limit for memory
            smoothing_sigma = self.smoothing_3d_var.get()
            single_color_mode = self.single_color_var.get()

            # Range parameters
            kx_range = (self.kx_min_var.get(), self.kx_max_var.get())
            ky_range = (self.ky_min_var.get(), self.ky_max_var.get())
            energy_range = (self.energy_min_var.get(), self.energy_max_var.get())

            # Only use ranges if they are actually set (not default 0,0)
            kx_range = kx_range if kx_range[0] != kx_range[1] else None
            ky_range = ky_range if ky_range[0] != ky_range[1] else None
            energy_range = energy_range if energy_range[0] != energy_range[1] else None

            # Create integrated projections using the notebook method
            show_critical_points = self.show_critical_points_3d_var.get()
            projections_result, message = self.analyzer_3d.create_integrated_projections_notebook_style(
                grid_size=grid_size,
                intensity_threshold=0.01,
                smoothing_sigma=smoothing_sigma,
                binary_threshold=0.15,
                single_color_mode=single_color_mode,
                kx_range=kx_range,
                ky_range=ky_range,
                energy_range=energy_range,
                kx_offset=0.0,
                ky_offset=0.0,
                energy_offset=0.0,
                colorscale='Viridis',
                show_critical_points=show_critical_points
            )

            if projections_result is None:
                self.log_message(f"❌ Failed to create projections: {message}", "ERROR")
                self.status_label.config(text=f"❌ {message}", foreground="red")
                return

            # Extract projections and critical points data
            projections = projections_result['projections']
            critical_points_data = projections_result['critical_points']

            self.update_progress(80, "Creating visualization...")

            # Create subplots for the three projections
            fig = make_subplots(
                rows=2, cols=2,
                subplot_titles=('E vs kx Projection', 'E vs ky Projection',
                               'kx vs ky Projection', '3D Data Sample'),
                specs=[[{"type": "heatmap"}, {"type": "heatmap"}],
                       [{"type": "heatmap"}, {"type": "scatter3d"}]]
            )

            # Add E vs kx projection
            e_kx_data, kx_grid, energy_grid, title1 = projections['e_kx']
            fig.add_trace(go.Heatmap(
                x=kx_grid,
                y=energy_grid,
                z=e_kx_data,
                colorscale='Viridis',
                name='E vs kx',
                showscale=False
            ), row=1, col=1)

            # Add critical points to E vs kx projection if available
            if critical_points_data and show_critical_points:
                for cp in critical_points_data:
                    fig.add_trace(go.Scatter(
                        x=[cp['kx']],
                        y=[cp['energy']],
                        mode='markers',
                        marker=dict(color='red', size=8, symbol='circle'),
                        name=f'Critical Point ({cp["type"]})',
                        showlegend=False,
                        hovertemplate=f'{cp["type"].title()}<br>kx: {cp["kx"]:.3f} Å⁻¹<br>E: {cp["energy"]:.3f} eV<extra></extra>'
                    ), row=1, col=1)

            # Add E vs ky projection
            e_ky_data, ky_grid, energy_grid, title2 = projections['e_ky']
            fig.add_trace(go.Heatmap(
                x=ky_grid,
                y=energy_grid,
                z=e_ky_data,
                colorscale='Viridis',
                name='E vs ky',
                showscale=False
            ), row=1, col=2)

            # Add critical points to E vs ky projection if available
            if critical_points_data and show_critical_points:
                for cp in critical_points_data:
                    fig.add_trace(go.Scatter(
                        x=[cp['ky']],
                        y=[cp['energy']],
                        mode='markers',
                        marker=dict(color='red', size=8, symbol='circle'),
                        name=f'Critical Point ({cp["type"]})',
                        showlegend=False,
                        hovertemplate=f'{cp["type"].title()}<br>ky: {cp["ky"]:.3f} Å⁻¹<br>E: {cp["energy"]:.3f} eV<extra></extra>'
                    ), row=1, col=2)

            # Add kx vs ky projection
            kx_ky_data, kx_grid, ky_grid, title3 = projections['kx_ky']
            fig.add_trace(go.Heatmap(
                x=kx_grid,
                y=ky_grid,
                z=kx_ky_data,
                colorscale='Viridis',
                name='kx vs ky',
                colorbar=dict(title="Intensity")
            ), row=2, col=1)

            # Add critical points to kx vs ky projection if available
            if critical_points_data and show_critical_points:
                for cp in critical_points_data:
                    fig.add_trace(go.Scatter(
                        x=[cp['kx']],
                        y=[cp['ky']],
                        mode='markers',
                        marker=dict(color='red', size=8, symbol='circle'),
                        name=f'Critical Point ({cp["type"]})',
                        showlegend=False,
                        hovertemplate=f'{cp["type"].title()}<br>kx: {cp["kx"]:.3f} Å⁻¹<br>ky: {cp["ky"]:.3f} Å⁻¹<extra></extra>'
                    ), row=2, col=1)

            # Log critical points information if included
            if critical_points_data and show_critical_points:
                self.log_message(f"🎯 Added {len(critical_points_data)} critical points to projections as red dots", "INFO")

            # Add 3D scatter sample from processed data
            df = self.analyzer_3d.processed_data.reset_index()
            df_sample = df[df['intensity'] > 0.01]  # Filter for visualization
            if len(df_sample) > 3000:
                df_sample = df_sample.sample(n=3000)

            self.log_message(f"📊 Creating 3D scatter sample with {len(df_sample):,} points", "DEBUG")

            fig.add_trace(go.Scatter3d(
                x=df_sample['kx'],
                y=df_sample['ky'],
                z=df_sample['binding_energy'],
                mode='markers',
                marker=dict(
                    size=2,
                    color=df_sample['intensity'],
                    colorscale='Viridis',
                    opacity=0.6
                ),
                name='3D Sample'
            ), row=2, col=2)

            # Update layout
            labels = create_latex_labels()
            layout_style = create_professional_layout()
            title_style = create_professional_title_style()

            fig.update_layout(
                title=dict(
                    text="ARPES Integrated Projections (ThesisAnalysis2.ipynb method)",
                    **title_style
                ),
                height=800,
                width=1200,
                **layout_style
            )

            # Update axis labels
            fig.update_xaxes(title_text=labels['kx'], row=1, col=1)
            fig.update_yaxes(title_text=labels['E_binding'], row=1, col=1)
            fig.update_xaxes(title_text=labels['ky'], row=1, col=2)
            fig.update_yaxes(title_text=labels['E_binding'], row=1, col=2)
            fig.update_xaxes(title_text=labels['kx'], row=2, col=1)
            fig.update_yaxes(title_text=labels['ky'], row=2, col=1)

            # Apply square mode if enabled
            if self.settings.get('square_mode', False):
                # Apply 1:1 aspect ratio to the kx vs ky subplot (row=2, col=1)
                fig.update_xaxes(scaleanchor="y2", scaleratio=1, row=2, col=1)
                fig.update_yaxes(scaleanchor="x2", scaleratio=1, row=2, col=1)

            # Save and display
            self.log_message(f"💾 Saving projections to: {Path(self.plot_file.name).name}", "DEBUG")
            fig.write_html(self.plot_file.name)
            self.plot_window.update_plot(self.plot_file.name)
            if not (self.plot_window.window and self.plot_window.window.winfo_exists()):
                self.plot_window.show()

            self.update_progress(100, "Projections created")
            self.status_label.config(text="✅ Projections created", foreground="green")
            self.log_message("🎉 Integrated projections generation completed successfully", "SUCCESS")

        except Exception as e:
            error_msg = f"Error creating projections: {str(e)}"
            self.status_label.config(text=f"❌ {error_msg}", foreground="red")
            self.log_message(f"❌ {error_msg}", "ERROR")
            self.log_message(f"📋 Full error details: {repr(e)}", "DEBUG")
            self.update_progress(0, "Error occurred")

    def find_critical_points(self):
        """Find and display critical points using the enhanced 3D energy surface method"""
        if self.analyzer_3d.processed_data is None:
            messagebox.showwarning("No Data", "Please process 3D data first or load processed data.")
            return

        try:
            self.log_message("🔍 Starting critical points analysis on 3D energy surface", "INFO")

            # Use the enhanced critical points computation method
            kx_range = (self.kx_min_var.get(), self.kx_max_var.get())
            ky_range = (self.ky_min_var.get(), self.ky_max_var.get())
            energy_range = (self.energy_min_var.get(), self.energy_max_var.get())

            # Debug: Check data availability
            self.log_message(f"🔍 Data availability check:", "DEBUG")
            self.log_message(f"   analyzer_3d.processed_data exists: {hasattr(self.analyzer_3d, 'processed_data')}", "DEBUG")
            if hasattr(self.analyzer_3d, 'processed_data'):
                self.log_message(f"   analyzer_3d.processed_data is None: {self.analyzer_3d.processed_data is None}", "DEBUG")
                if self.analyzer_3d.processed_data is not None:
                    self.log_message(f"   analyzer_3d.processed_data shape: {self.analyzer_3d.processed_data.shape}", "DEBUG")

            critical_points, message = self.analyzer_3d.compute_critical_points_on_energy_surface(
                grid_size=int(self.grid_size_var.get()),
                intensity_threshold=self.cp_intensity_threshold_var.get(),
                smoothing_sigma=self.smoothing_3d_var.get(),
                kx_range=kx_range,
                ky_range=ky_range,
                energy_range=energy_range,
                gradient_threshold=self.cp_gradient_threshold_var.get(),
                min_separation=self.cp_min_separation_var.get(),
                edge_buffer=int(self.cp_edge_buffer_var.get()),
                surface_intensity_threshold=self.cp_surface_threshold_var.get()
            )

            if critical_points is None:
                self.status_label.config(text=f"❌ {message}", foreground="red")
                self.log_message(f"❌ Critical points analysis failed: {message}", "ERROR")
                return

            # Print detailed critical points information to log
            self.log_message("🎯 CRITICAL POINTS ANALYSIS RESULTS:", "INFO")
            self.log_message("=" * 60, "INFO")

            # Print algorithm parameters used
            self.log_message("🔧 ALGORITHM PARAMETERS:", "INFO")
            self.log_message(f"   Intensity threshold: {self.cp_intensity_threshold_var.get():.4f} (data filtering)", "INFO")
            self.log_message(f"   Surface threshold: {self.cp_surface_threshold_var.get():.3f} (binary surface)", "INFO")
            self.log_message(f"   Gradient threshold: {self.cp_gradient_threshold_var.get():.6f}", "INFO")
            self.log_message(f"   Min separation: {self.cp_min_separation_var.get():.3f} Å⁻¹", "INFO")
            self.log_message(f"   Edge buffer: {int(self.cp_edge_buffer_var.get())} pixels", "INFO")
            self.log_message(f"   Grid size: {int(self.grid_size_var.get())}", "INFO")
            self.log_message(f"   Smoothing sigma: {self.smoothing_3d_var.get():.2f}", "INFO")
            self.log_message("", "INFO")

            # Group by type and print details
            for cp_type in ['maximum', 'minimum', 'saddle']:
                cp_subset = [cp for cp in critical_points if cp['type'] == cp_type]
                if cp_subset:
                    self.log_message(f"📈 {cp_type.upper()}S ({len(cp_subset)} found):", "INFO")
                    for i, cp in enumerate(cp_subset[:10]):  # Limit to first 10 for readability
                        grad_mag = cp.get('gradient_magnitude', 0)
                        hess_det = cp.get('hessian_det', 0)
                        self.log_message(f"   {i+1:2d}. kx={cp['kx']:6.3f} Å⁻¹, ky={cp['ky']:6.3f} Å⁻¹, "
                                       f"E={cp['energy']:6.3f} eV, "
                                       f"∇={grad_mag:.6f}, H={hess_det:.6f}", "INFO")
                    if len(cp_subset) > 10:
                        self.log_message(f"   ... and {len(cp_subset)-10} more {cp_type}s", "INFO")
                    self.log_message("", "INFO")  # Empty line for readability

            # Summary statistics
            num_maxima = len([cp for cp in critical_points if cp['type'] == 'maximum'])
            num_minima = len([cp for cp in critical_points if cp['type'] == 'minimum'])
            num_saddles = len([cp for cp in critical_points if cp['type'] == 'saddle'])

            self.log_message("📊 SUMMARY STATISTICS:", "INFO")
            self.log_message(f"   Total critical points: {len(critical_points)}", "INFO")
            self.log_message(f"   Maxima: {num_maxima}", "INFO")
            self.log_message(f"   Minima: {num_minima}", "INFO")
            self.log_message(f"   Saddles: {num_saddles}", "INFO")
            self.log_message("=" * 50, "INFO")

            # Convert to DataFrame for visualization
            cp_df = pd.DataFrame(critical_points)

            # Create 3D scatter plot of critical points
            fig = go.Figure()

            # Plot different types of critical points
            for cp_type in ['maximum', 'minimum', 'saddle']:
                cp_subset = cp_df[cp_df['type'] == cp_type]
                if len(cp_subset) > 0:
                    colors = {'maximum': 'red', 'minimum': 'blue', 'saddle': 'orange'}
                    symbols = {'maximum': 'circle', 'minimum': 'square', 'saddle': 'diamond'}

                    fig.add_trace(go.Scatter3d(
                        x=cp_subset['kx'],
                        y=cp_subset['ky'],
                        z=cp_subset['energy'],
                        mode='markers',
                        marker=dict(
                            size=8,
                            color=colors[cp_type],
                            symbol=symbols[cp_type],
                            opacity=0.8
                        ),
                        name=f'{cp_type.title()} ({len(cp_subset)})',
                        hovertemplate=f'{cp_type.title()}<br>' +
                                     'kx: %{x:.3f} Å⁻¹<br>' +
                                     'ky: %{y:.3f} Å⁻¹<br>' +
                                     'E: %{z:.3f} eV<extra></extra>'
                    ))

            # Update layout
            labels = create_latex_labels()
            layout_style = create_professional_layout()
            title_style = create_professional_title_style()

            fig.update_layout(
                title=dict(
                    text=f"Critical Points Analysis ({len(critical_points)} points found)",
                    **title_style
                ),
                scene=dict(
                    xaxis_title=labels['kx'],
                    yaxis_title=labels['ky'],
                    zaxis_title=labels['E_binding'],
                    aspectratio=dict(x=1, y=1, z=1),
                    camera=dict(
                        eye=dict(x=1.5, y=1.5, z=1.5)
                    ),
                    xaxis=dict(
                        tickfont=dict(family='Times New Roman, serif', size=14, color='black'),  # Slightly larger than original 12
                        title_font=dict(family='Times New Roman, serif', size=16, color='black'),  # Slightly larger than original 14
                        showline=True,
                        linewidth=2,
                        linecolor='black',
                        mirror=True,
                        ticks='outside',
                        tickwidth=2,
                        tickcolor='black',
                        showgrid=True,
                        gridwidth=1,
                        gridcolor='lightgray'
                    ),
                    yaxis=dict(
                        tickfont=dict(family='Times New Roman, serif', size=14, color='black'),  # Slightly larger than original 12
                        title_font=dict(family='Times New Roman, serif', size=16, color='black'),  # Slightly larger than original 14
                        showline=True,
                        linewidth=2,
                        linecolor='black',
                        mirror=True,
                        ticks='outside',
                        tickwidth=2,
                        tickcolor='black',
                        showgrid=True,
                        gridwidth=1,
                        gridcolor='lightgray'
                    ),
                    zaxis=dict(
                        tickfont=dict(family='Times New Roman, serif', size=14, color='black'),  # Slightly larger than original 12
                        title_font=dict(family='Times New Roman, serif', size=16, color='black'),  # Slightly larger than original 14
                        showline=True,
                        linewidth=2,
                        linecolor='black',
                        mirror=True,
                        ticks='outside',
                        tickwidth=2,
                        tickcolor='black',
                        showgrid=True,
                        gridwidth=1,
                        gridcolor='lightgray'
                    )
                ),
                width=900,
                height=700,
                **layout_style
            )

            # Save and display
            fig.write_html(self.plot_file.name)
            self.plot_window.update_plot(self.plot_file.name)
            if not (self.plot_window.window and self.plot_window.window.winfo_exists()):
                self.plot_window.show()

            self.update_progress(100, "Critical points analysis complete")
            self.status_label.config(text=f"✅ Found {len(critical_points)} critical points", foreground="green")

        except Exception as e:
            self.status_label.config(text=f"❌ Error finding critical points: {str(e)}", foreground="red")
            self.update_progress(0, "Error occurred")

    def check_data_for_critical_points(self):
        """Analyze processed data and suggest optimal critical points parameters"""
        if self.analyzer_3d.processed_data is None:
            messagebox.showwarning("No Data", "Please process 3D data first or load processed data.")
            return

        try:
            self.log_message("🔍 ANALYZING DATA FOR CRITICAL POINTS OPTIMIZATION", "INFO")
            self.log_message("=" * 60, "INFO")

            # Get processed data
            df = self.analyzer_3d.processed_data.reset_index()

            # Basic data statistics
            self.log_message("📊 DATA OVERVIEW:", "INFO")
            self.log_message(f"   Total data points: {len(df):,}", "INFO")
            self.log_message(f"   Intensity range: {df['intensity'].min():.6f} to {df['intensity'].max():.6f}", "INFO")
            self.log_message(f"   kx range: {df['kx'].min():.3f} to {df['kx'].max():.3f} Å⁻¹", "INFO")
            self.log_message(f"   ky range: {df['ky'].min():.3f} to {df['ky'].max():.3f} Å⁻¹", "INFO")
            self.log_message(f"   Energy range: {df['binding_energy'].min():.3f} to {df['binding_energy'].max():.3f} eV", "INFO")
            self.log_message("", "INFO")

            # Intensity distribution analysis
            self.log_message("📈 INTENSITY DISTRIBUTION ANALYSIS:", "INFO")
            intensity_percentiles = np.percentile(df['intensity'], [1, 5, 10, 25, 50, 75, 90, 95, 99])
            percentile_labels = ['1%', '5%', '10%', '25%', '50%', '75%', '90%', '95%', '99%']

            for label, value in zip(percentile_labels, intensity_percentiles):
                self.log_message(f"   {label:>3} percentile: {value:.6f}", "INFO")

            # Current parameter analysis
            current_threshold = self.cp_intensity_threshold_var.get()
            points_above_threshold = len(df[df['intensity'] > current_threshold])
            percentage_above = 100 * points_above_threshold / len(df)

            self.log_message("", "INFO")
            self.log_message("🎯 CURRENT PARAMETER ANALYSIS:", "INFO")
            self.log_message(f"   Current intensity threshold: {current_threshold:.6f}", "INFO")
            self.log_message(f"   Points above threshold: {points_above_threshold:,} ({percentage_above:.1f}%)", "INFO")

            # Parameter recommendations
            self.log_message("", "INFO")
            self.log_message("💡 RECOMMENDED PARAMETERS:", "INFO")

            # Suggest intensity threshold (5-10% of max intensity) - for data filtering
            max_intensity = df['intensity'].max()
            suggested_intensity_threshold = max_intensity * 0.05  # 5% of max
            self.log_message(f"   Intensity threshold: {suggested_intensity_threshold:.6f} (5% of max, for data filtering)", "INFO")

            # Suggest surface threshold based on intensity distribution
            # Use a more conservative approach - start with 5% of max, but check percentiles
            percentile_95 = np.percentile(df['intensity'], 95)
            percentile_90 = np.percentile(df['intensity'], 90)

            # Choose threshold that captures significant intensity features
            if percentile_90 > max_intensity * 0.1:
                suggested_surface_threshold = max_intensity * 0.1  # 10% of max
            elif percentile_95 > max_intensity * 0.05:
                suggested_surface_threshold = max_intensity * 0.05  # 5% of max
            else:
                suggested_surface_threshold = max_intensity * 0.01  # 1% of max

            self.log_message(f"   Surface threshold: {suggested_surface_threshold:.6f} (for binary surface definition)", "INFO")
            self.log_message(f"     (Based on intensity distribution: 95th percentile = {percentile_95:.6f})", "INFO")

            # Suggest gradient threshold based on data density - more restrictive
            data_density = len(df) / ((df['kx'].max() - df['kx'].min()) * (df['ky'].max() - df['ky'].min()))
            if data_density > 1000:
                suggested_gradient_threshold = 0.01  # Increased from 0.001
            elif data_density > 100:
                suggested_gradient_threshold = 0.02  # Increased from 0.005
            else:
                suggested_gradient_threshold = 0.05  # Increased from 0.01
            self.log_message(f"   Gradient threshold: {suggested_gradient_threshold:.6f} (based on data density: {data_density:.1f} pts/Å⁻²)", "INFO")

            # Suggest minimum separation based on k-space range - more restrictive
            kx_range = df['kx'].max() - df['kx'].min()
            ky_range = df['ky'].max() - df['ky'].min()
            avg_k_range = (kx_range + ky_range) / 2
            suggested_min_separation = avg_k_range / 10  # 10% of average k-range (increased from 5%)
            self.log_message(f"   Min separation: {suggested_min_separation:.3f} Å⁻¹ (10% of avg k-range)", "INFO")

            # Grid size recommendation
            suggested_grid_size = min(200, max(50, int(np.sqrt(len(df) / 100))))
            self.log_message(f"   Grid size: {suggested_grid_size} (based on data density)", "INFO")

            self.log_message("", "INFO")
            self.log_message("🔧 APPLY RECOMMENDATIONS:", "INFO")

            # Ask user if they want to apply recommendations
            apply_recommendations = messagebox.askyesno(
                "Apply Recommendations",
                f"Apply recommended parameters?\n\n"
                f"Intensity threshold: {suggested_intensity_threshold:.6f}\n"
                f"Surface threshold: {suggested_surface_threshold:.3f}\n"
                f"Gradient threshold: {suggested_gradient_threshold:.6f}\n"
                f"Min separation: {suggested_min_separation:.3f} Å⁻¹\n"
                f"Grid size: {suggested_grid_size}\n\n"
                f"This will update the current parameter values."
            )

            if apply_recommendations:
                self.cp_intensity_threshold_var.set(suggested_intensity_threshold)
                self.cp_surface_threshold_var.set(suggested_surface_threshold)
                self.cp_gradient_threshold_var.set(suggested_gradient_threshold)
                self.cp_min_separation_var.set(suggested_min_separation)
                self.grid_size_var.set(suggested_grid_size)
                self.update_3d_labels()

                self.log_message("✅ Recommended parameters applied successfully!", "SUCCESS")
                self.status_label.config(text="✅ Parameters optimized for critical points analysis", foreground="green")
            else:
                self.log_message("ℹ️ Recommendations not applied - parameters unchanged", "INFO")
                self.status_label.config(text="ℹ️ Data analysis complete - see log for recommendations", foreground="blue")

            self.log_message("=" * 60, "INFO")

        except Exception as e:
            self.status_label.config(text=f"❌ Error analyzing data: {str(e)}", foreground="red")
            self.log_message(f"❌ Data analysis error: {str(e)}", "ERROR")

    def test_intensity_grid(self):
        """Quick test of intensity grid construction for debugging"""
        if self.analyzer_3d.processed_data is None:
            messagebox.showwarning("No Data", "Please process 3D data first or load processed data.")
            return

        try:
            self.log_message("🧪 TESTING INTENSITY GRID CONSTRUCTION", "INFO")
            self.log_message("=" * 50, "INFO")

            # Get current parameters
            grid_size = int(self.grid_size_var.get())
            intensity_threshold = self.cp_intensity_threshold_var.get()
            surface_threshold = self.cp_surface_threshold_var.get()

            # Get processed data
            df = self.analyzer_3d.processed_data.reset_index()

            # Apply intensity filtering
            df_filtered = df[df['intensity'] > intensity_threshold]

            self.log_message(f"📊 DATA FILTERING TEST:", "INFO")
            self.log_message(f"   Original data points: {len(df):,}", "INFO")
            self.log_message(f"   After intensity filter ({intensity_threshold:.6f}): {len(df_filtered):,}", "INFO")
            self.log_message(f"   Intensity range: {df['intensity'].min():.6f} to {df['intensity'].max():.6f}", "INFO")

            if len(df_filtered) < 10:
                self.log_message(f"❌ Very few points after filtering - try lowering intensity threshold", "ERROR")
                return

            # Test grid construction with a smaller grid for speed
            test_grid_size = min(50, grid_size)

            # Define grid limits
            kx_min, kx_max = df_filtered['kx'].min(), df_filtered['kx'].max()
            ky_min, ky_max = df_filtered['ky'].min(), df_filtered['ky'].max()
            energy_min, energy_max = df_filtered['binding_energy'].min(), df_filtered['binding_energy'].max()

            self.log_message(f"🔧 GRID CONSTRUCTION TEST (size: {test_grid_size}):", "INFO")
            self.log_message(f"   kx range: {kx_min:.3f} to {kx_max:.3f} Å⁻¹", "INFO")
            self.log_message(f"   ky range: {ky_min:.3f} to {ky_max:.3f} Å⁻¹", "INFO")
            self.log_message(f"   Energy range: {energy_min:.3f} to {energy_max:.3f} eV", "INFO")

            # Create test grid
            kx_grid = np.linspace(kx_min, kx_max, test_grid_size)
            ky_grid = np.linspace(ky_min, ky_max, test_grid_size)
            energy_grid = np.linspace(energy_max, energy_min, test_grid_size)

            KX, KY, E = np.meshgrid(kx_grid, ky_grid, energy_grid, indexing='ij')
            intensity_grid = np.zeros((test_grid_size, test_grid_size, test_grid_size))

            successful_slices = 0

            # Build test grid
            for k, energy in enumerate(energy_grid):
                energy_step = (energy_max - energy_min) / test_grid_size
                slice_data = df_filtered[(df_filtered['binding_energy'] >= energy - energy_step/2) &
                                       (df_filtered['binding_energy'] < energy + energy_step/2)]

                if len(slice_data) > 3:
                    try:
                        from scipy.interpolate import griddata
                        intensity_values = griddata(
                            (slice_data['kx'], slice_data['ky']),
                            slice_data['intensity'],
                            (KX[:, :, k], KY[:, :, k]),
                            method='linear',
                            fill_value=0
                        )
                        intensity_grid[:, :, k] = np.nan_to_num(intensity_values)
                        successful_slices += 1
                    except:
                        pass

            # Analyze grid results
            grid_min = np.min(intensity_grid)
            grid_max = np.max(intensity_grid)
            grid_mean = np.mean(intensity_grid)
            nonzero_points = np.sum(intensity_grid > 0)

            self.log_message(f"📈 GRID ANALYSIS RESULTS:", "INFO")
            self.log_message(f"   Successful slices: {successful_slices}/{test_grid_size}", "INFO")
            self.log_message(f"   Grid intensity range: {grid_min:.6f} to {grid_max:.6f}", "INFO")
            self.log_message(f"   Grid mean intensity: {grid_mean:.6f}", "INFO")
            self.log_message(f"   Non-zero grid points: {nonzero_points:,}/{intensity_grid.size:,} ({100*nonzero_points/intensity_grid.size:.1f}%)", "INFO")

            # Test surface thresholds
            self.log_message(f"🎯 SURFACE THRESHOLD TESTS:", "INFO")
            for test_threshold in [surface_threshold, grid_max * 0.1, grid_max * 0.05, grid_max * 0.01]:
                if grid_max > 0:
                    test_mask = intensity_grid > test_threshold
                    test_voxels = np.sum(test_mask)
                    percentage = 100 * test_voxels / intensity_grid.size
                    self.log_message(f"   Threshold {test_threshold:.6f}: {test_voxels:,} voxels ({percentage:.1f}%)", "INFO")
                else:
                    self.log_message(f"   Grid max is 0 - no valid intensity data in grid!", "ERROR")

            self.log_message("=" * 50, "INFO")

            if grid_max == 0:
                self.log_message("❌ DIAGNOSIS: Intensity grid is empty!", "ERROR")
                self.log_message("   This suggests an issue with data interpolation or filtering", "ERROR")
                self.log_message("   Try lowering the intensity threshold significantly", "ERROR")
            elif nonzero_points < intensity_grid.size * 0.01:
                self.log_message("⚠️ DIAGNOSIS: Very sparse intensity grid", "WARNING")
                self.log_message("   Consider lowering intensity threshold or increasing grid density", "WARNING")
            else:
                self.log_message("✅ DIAGNOSIS: Grid construction appears successful", "SUCCESS")
                optimal_threshold = grid_max * 0.05
                self.log_message(f"   Suggested surface threshold: {optimal_threshold:.6f}", "SUCCESS")

            self.status_label.config(text="✅ Grid test complete - see log for results", foreground="green")

        except Exception as e:
            self.status_label.config(text=f"❌ Error testing grid: {str(e)}", foreground="red")
            self.log_message(f"❌ Grid test error: {str(e)}", "ERROR")

    def save_binary_surface(self):
        """Save binary surface data to file for cross sections"""
        if not hasattr(self.analyzer_3d, 'binary_intensity') or self.analyzer_3d.binary_intensity is None:
            messagebox.showwarning("No Binary Surface",
                                 "No binary surface data available to save.\n"
                                 "Please create a 3D surface first using the '3D Surface' button.")
            return

        try:
            # Open file dialog to choose save location
            filename = filedialog.asksaveasfilename(
                title="Save Binary Surface Data",
                defaultextension=".npz",
                filetypes=[
                    ("NumPy Compressed Archive", "*.npz"),
                    ("All Files", "*.*")
                ],
                initialdir=self.current_data_folder if hasattr(self, 'current_data_folder') and self.current_data_folder else None
            )

            if filename:
                # Use the analyzer's save method
                success, message = self.analyzer_3d.save_binary_surface(filename)

                if success:
                    self.status_label.config(text="✅ Binary surface saved successfully", foreground="green")
                    self.log_message(f"✅ Binary surface saved to: {filename}", "SUCCESS")
                    messagebox.showinfo("Save Successful",
                                      f"Binary surface data saved successfully!\n\n"
                                      f"File: {filename}\n\n"
                                      f"You can now load this file in the Cross Sections tab.")
                else:
                    self.status_label.config(text=f"❌ Error saving: {message}", foreground="red")
                    self.log_message(f"❌ Save failed: {message}", "ERROR")
                    messagebox.showerror("Save Failed", f"Failed to save binary surface:\n{message}")

        except Exception as e:
            error_msg = f"Error saving binary surface: {str(e)}"
            self.status_label.config(text=f"❌ {error_msg}", foreground="red")
            self.log_message(f"❌ {error_msg}", "ERROR")
            messagebox.showerror("Save Error", error_msg)

    def browse_surface_file(self):
        """Browse for binary surface file"""
        filename = filedialog.askopenfilename(
            title="Select Binary Surface File",
            filetypes=[
                ("NumPy Compressed Archive", "*.npz"),
                ("All Files", "*.*")
            ],
            initialdir=self.current_data_folder if hasattr(self, 'current_data_folder') and self.current_data_folder else None
        )

        if filename:
            self.surface_file_var.set(filename)
            self.surface_file_label.config(foreground="black")
            self.file_info_var.set("File selected - click 'Load File' to load")
            self.log_message(f"📁 Selected binary surface file: {filename}", "DEBUG")

    def load_surface_file(self):
        """Load binary surface file for cross sections"""
        filename = self.surface_file_var.get()

        if filename == "No file selected" or not filename:
            messagebox.showwarning("No File Selected", "Please select a binary surface file first.")
            return

        if not os.path.exists(filename):
            messagebox.showerror("File Not Found", f"The selected file does not exist:\n{filename}")
            return

        try:
            # Use the analyzer's load method
            success, message = self.analyzer_3d.load_binary_surface(filename)

            if success:
                # Update local references for cross sections
                self.binary_surface_data = self.analyzer_3d.binary_intensity
                self.surface_grids = (self.analyzer_3d.kx_grid, self.analyzer_3d.ky_grid, self.analyzer_3d.energy_grid)

                # Update file info display
                shape = self.binary_surface_data.shape
                kx_range = (self.analyzer_3d.kx_grid[0], self.analyzer_3d.kx_grid[-1])
                ky_range = (self.analyzer_3d.ky_grid[0], self.analyzer_3d.ky_grid[-1])
                energy_range = (self.analyzer_3d.energy_grid[0], self.analyzer_3d.energy_grid[-1])

                info_text = (f"Loaded: {shape[0]}×{shape[1]}×{shape[2]} grid | "
                           f"kx: [{kx_range[0]:.2f}, {kx_range[1]:.2f}] | "
                           f"ky: [{ky_range[0]:.2f}, {ky_range[1]:.2f}] | "
                           f"E: [{energy_range[0]:.2f}, {energy_range[1]:.2f}] eV")
                self.file_info_var.set(info_text)

                # Update cross section controls
                self.update_cross_section_controls()

                self.status_label.config(text="✅ Binary surface loaded successfully", foreground="green")
                self.log_message(f"✅ Binary surface loaded from: {filename}", "SUCCESS")
                messagebox.showinfo("Load Successful",
                                  f"Binary surface loaded successfully!\n\n"
                                  f"Grid size: {shape[0]}×{shape[1]}×{shape[2]}\n"
                                  f"You can now generate cross sections.")
            else:
                self.status_label.config(text=f"❌ Error loading: {message}", foreground="red")
                self.log_message(f"❌ Load failed: {message}", "ERROR")
                messagebox.showerror("Load Failed", f"Failed to load binary surface:\n{message}")

        except Exception as e:
            error_msg = f"Error loading binary surface: {str(e)}"
            self.status_label.config(text=f"❌ {error_msg}", foreground="red")
            self.log_message(f"❌ {error_msg}", "ERROR")
            messagebox.showerror("Load Error", error_msg)

    def update_multi_slice_controls(self):
        """Show/hide multi-slice controls based on checkbox"""
        if self.multi_slice_var.get():
            self.multi_slice_controls_frame.pack(fill=tk.X, pady=(0, 10))
            self.update_multi_slice_labels()
        else:
            self.multi_slice_controls_frame.pack_forget()

    def update_multi_slice_labels(self, *args):
        """Update multi-slice control labels"""
        num_slices = int(self.num_slices_var.get())
        range_min = self.range_min_var.get()
        range_max = self.range_max_var.get()

        self.num_slices_label.config(text=str(num_slices))
        self.range_min_label.config(text=f"{range_min:.2f}")
        self.range_max_label.config(text=f"{range_max:.2f}")

    def update_cross_offset_labels(self, event=None):
        """Update cross section offset labels"""
        if hasattr(self, 'cross_kx_offset_label'):
            self.cross_kx_offset_label.config(text=f"{self.cross_kx_offset_var.get():.2f}")
        if hasattr(self, 'cross_ky_offset_label'):
            self.cross_ky_offset_label.config(text=f"{self.cross_ky_offset_var.get():.2f}")
        if hasattr(self, 'cross_energy_offset_label'):
            self.cross_energy_offset_label.config(text=f"{self.cross_energy_offset_var.get():.2f}")

    def get_offset_coordinate_grids(self):
        """Get coordinate grids with offsets applied"""
        if not hasattr(self, 'surface_grids') or self.surface_grids is None:
            return None, None, None

        kx_grid, ky_grid, energy_grid = self.surface_grids

        # Apply offsets
        kx_offset = self.cross_kx_offset_var.get()
        ky_offset = self.cross_ky_offset_var.get()
        energy_offset = self.cross_energy_offset_var.get()

        kx_grid_offset = kx_grid + kx_offset
        ky_grid_offset = ky_grid + ky_offset
        energy_grid_offset = energy_grid + energy_offset

        return kx_grid_offset, ky_grid_offset, energy_grid_offset

    def update_cross_section_controls(self):
        """Update cross section controls based on selected type"""
        if not hasattr(self, 'binary_surface_data') or self.binary_surface_data is None:
            return

        section_type = self.cross_section_type_var.get()

        # Update slice controls based on section type (corrected for data shape: energy, kx, ky)
        if section_type == "energy":
            self.slice_label.config(text="Energy Slice:")
            max_slices = self.binary_surface_data.shape[0] - 1  # Energy is 1st dimension
            # Set free-form range controls for energy multi-slice
            if hasattr(self, 'range_min_var') and hasattr(self, 'range_min_scale'):
                self.range_min_scale.config(from_=-2.0, to=2.0)
                self.range_max_scale.config(from_=-2.0, to=2.0)
                if not hasattr(self, '_ranges_initialized') or not self._ranges_initialized:
                    self.range_min_var.set(-2.0)
                    self.range_max_var.set(2.0)
        elif section_type == "kx":
            self.slice_label.config(text="k<sub>x</sub> Slice:")
            max_slices = self.binary_surface_data.shape[2] - 1  # kx is 3rd dimension in (energy, ky, kx)
            # Set free-form range controls for kx multi-slice
            if hasattr(self, 'range_min_var') and hasattr(self, 'range_min_scale'):
                self.range_min_scale.config(from_=-2.0, to=2.0)
                self.range_max_scale.config(from_=-2.0, to=2.0)
                if not hasattr(self, '_ranges_initialized') or not self._ranges_initialized:
                    self.range_min_var.set(-2.0)
                    self.range_max_var.set(2.0)
        elif section_type == "ky":
            self.slice_label.config(text="k<sub>y</sub> Slice:")
            max_slices = self.binary_surface_data.shape[1] - 1  # ky is 2nd dimension in (energy, ky, kx)
            # Set free-form range controls for ky multi-slice
            if hasattr(self, 'range_min_var') and hasattr(self, 'range_min_scale'):
                self.range_min_scale.config(from_=-2.0, to=2.0)
                self.range_max_scale.config(from_=-2.0, to=2.0)
                if not hasattr(self, '_ranges_initialized') or not self._ranges_initialized:
                    self.range_min_var.set(-2.0)
                    self.range_max_var.set(2.0)

        # Mark ranges as initialized to prevent overwriting user settings
        self._ranges_initialized = True

        self.slice_scale.config(to=max_slices)
        self.slice_var.set(max_slices // 2)  # Start in the middle

        # Update multi-slice labels if they exist
        if hasattr(self, 'update_multi_slice_labels'):
            self.update_multi_slice_labels()

        self.update_cross_section_slice()

    def update_cross_section_slice(self, event=None):
        """Update the slice value label"""
        if not hasattr(self, 'binary_surface_data') or self.binary_surface_data is None:
            self.slice_value_label.config(text="No data")
            return

        slice_idx = int(self.slice_var.get())
        section_type = self.cross_section_type_var.get()

        # Get the actual coordinate value for this slice using offset coordinates
        kx_grid_offset, ky_grid_offset, energy_grid_offset = self.get_offset_coordinate_grids()
        if kx_grid_offset is not None:
            if section_type == "energy":
                value = energy_grid_offset[slice_idx]
                self.slice_value_label.config(text=f"{value:.3f} eV")
            elif section_type == "kx":
                # For kx plots, we fix kx and vary ky, so show the fixed kx value
                value = kx_grid_offset[slice_idx]
                self.slice_value_label.config(text=f"{value:.3f} Å⁻¹")
            elif section_type == "ky":
                # For ky plots, we fix ky and vary kx, so show the fixed ky value
                value = ky_grid_offset[slice_idx]
                self.slice_value_label.config(text=f"{value:.3f} Å⁻¹")
        else:
            self.slice_value_label.config(text=f"Slice {slice_idx}")

    def first_slice(self):
        """Go to first slice"""
        self.slice_var.set(0)
        self.update_cross_section_slice()

    def prev_slice(self):
        """Go to previous slice"""
        current = self.slice_var.get()
        if current > 0:
            self.slice_var.set(current - 1)
            self.update_cross_section_slice()

    def next_slice(self):
        """Go to next slice"""
        current = self.slice_var.get()
        max_val = int(self.slice_scale.cget('to'))
        if current < max_val:
            self.slice_var.set(current + 1)
            self.update_cross_section_slice()

    def last_slice(self):
        """Go to last slice"""
        max_val = int(self.slice_scale.cget('to'))
        self.slice_var.set(max_val)
        self.update_cross_section_slice()

    def generate_cross_section(self):
        """Generate and display the current cross section"""
        try:
            # Check if we have binary surface data
            if not hasattr(self, 'binary_surface_data') or self.binary_surface_data is None:
                # Try to get it from the 3D analyzer
                if hasattr(self.analyzer_3d, 'binary_intensity') and self.analyzer_3d.binary_intensity is not None:
                    self.binary_surface_data = self.analyzer_3d.binary_intensity
                    self.surface_grids = (self.analyzer_3d.kx_grid, self.analyzer_3d.ky_grid, self.analyzer_3d.energy_grid)
                    self.log_message("📊 Retrieved binary surface data from 3D analyzer", "INFO")
                    self.update_cross_section_controls()
                else:
                    self.status_label.config(text="❌ No binary surface data available. Create 3D surface first.", foreground="red")
                    self.log_message("❌ No binary surface data found. Please create a 3D surface first in the 3D Analysis tab.", "ERROR")
                    return

            section_type = self.cross_section_type_var.get()

            # Get offset coordinate grids
            kx_grid_offset, ky_grid_offset, energy_grid_offset = self.get_offset_coordinate_grids()
            if kx_grid_offset is None:
                self.status_label.config(text="❌ No coordinate grids available", foreground="red")
                return

            # Check if multi-slice mode is enabled
            if self.multi_slice_var.get():
                # Check if progressive heatmap mode is enabled
                if self.progressive_heatmap_var.get():
                    self._generate_progressive_heatmap_cross_section()
                else:
                    self._generate_multi_slice_cross_section()
                return

            # Single slice mode
            slice_idx = int(self.slice_var.get())
            self.log_message(f"🔍 Generating {section_type} cross section at slice {slice_idx}", "INFO")

            # Extract the cross section with corrected axis mapping
            # Data shape: (energy, kx, ky) based on how it's built in create_energy_surface_notebook_style
            if section_type == "energy":
                # Energy slice: kx vs ky at fixed energy
                cross_section = self.binary_surface_data[slice_idx, :, :]
                x_grid, y_grid = kx_grid_offset, ky_grid_offset
                x_label, y_label = "k<sub>x</sub> (Å⁻¹)", "k<sub>y</sub> (Å⁻¹)"
                title = f"Energy Cross Section at E = {energy_grid_offset[slice_idx]:.3f} eV"

            elif section_type == "kx":
                # kx slice: Energy vs ky at fixed kx
                # Data shape: (energy, ky, kx), so slice at [:, :, slice_idx] gives (energy, ky)
                cross_section = self.binary_surface_data[:, :, slice_idx]  # Shape: (energy, ky) - fixed kx
                x_grid, y_grid = ky_grid_offset, energy_grid_offset  # ky on x-axis, Energy on y-axis
                x_label, y_label = "k<sub>y</sub> (Å⁻¹)", "E - E<sub>F</sub> (eV)"
                title = f"k<sub>x</sub> Cross Section: Energy vs k<sub>y</sub> at k<sub>x</sub> = {kx_grid_offset[slice_idx]:.3f} Å⁻¹"

            elif section_type == "ky":
                # ky slice: Energy vs kx at fixed ky
                # Data shape: (energy, ky, kx), so slice at [:, slice_idx, :] gives (energy, kx)
                cross_section = self.binary_surface_data[:, slice_idx, :]  # Shape: (energy, kx) - fixed ky
                x_grid, y_grid = kx_grid_offset, energy_grid_offset  # kx on x-axis, Energy on y-axis
                x_label, y_label = "k<sub>x</sub> (Å⁻¹)", "E - E<sub>F</sub> (eV)"
                title = f"k<sub>y</sub> Cross Section: Energy vs k<sub>x</sub> at k<sub>y</sub> = {ky_grid_offset[slice_idx]:.3f} Å⁻¹"

            # Add offset information to title if offsets are non-zero
            kx_offset = self.cross_kx_offset_var.get()
            ky_offset = self.cross_ky_offset_var.get()
            energy_offset = self.cross_energy_offset_var.get()
            if kx_offset != 0.0 or ky_offset != 0.0 or energy_offset != 0.0:
                title += f" (Offsets: kx={kx_offset:.2f}, ky={ky_offset:.2f}, E={energy_offset:.2f})"

            # Create the plot
            import plotly.graph_objects as go

            # Apply display options
            colorscale = 'Greys_r' if self.invert_colors_var.get() else 'Greys'

            # Make zero intensity regions transparent
            cross_section_masked = np.where(cross_section > 0.5, cross_section, np.nan)

            fig = go.Figure()

            # Check if X-Axis Coloring is enabled
            if self.use_x_axis_coloring_var.get():
                # Use scatter plot with x-axis based coloring
                y_indices, x_indices = np.where(~np.isnan(cross_section_masked))
                if len(x_indices) > 0:
                    x_coords = x_grid[x_indices]
                    y_coords = y_grid[y_indices]
                    intensities = cross_section_masked[y_indices, x_indices]

                    # Color based on x-axis position
                    fig.add_trace(go.Scatter(
                        x=x_coords,
                        y=y_coords,
                        mode='markers',
                        marker=dict(
                            color=x_coords,  # Color based on x-axis position
                            colorscale='Viridis',
                            size=4,
                            opacity=0.8,
                            colorbar=dict(title=x_label) if self.show_colorbar_var.get() else None,
                            showscale=self.show_colorbar_var.get()
                        ),
                        hovertemplate=f'{x_label}: %{{x:.3f}}<br>{y_label}: %{{y:.3f}}<br>Intensity: %{{marker.color:.3f}}<extra></extra>'
                    ))
            else:
                # Default heatmap behavior
                fig.add_trace(go.Heatmap(
                    z=cross_section_masked,
                    x=x_grid,
                    y=y_grid,
                    colorscale=colorscale,
                    showscale=self.show_colorbar_var.get(),
                    hovertemplate=f'{x_label}: %{{x:.3f}}<br>{y_label}: %{{y:.3f}}<br>Intensity: %{{z:.3f}}<extra></extra>',
                    connectgaps=False  # Don't interpolate across NaN values
                ))

            fig.update_layout(
                title=title,
                xaxis_title=x_label,
                yaxis_title=y_label,
                font=dict(family="Times New Roman", size=19),
                showlegend=False,
                plot_bgcolor='white',
                paper_bgcolor='white'
            )

            # Add grid if requested
            if self.show_grid_var.get():
                fig.update_xaxes(showgrid=True, gridwidth=1, gridcolor='lightgray')
                fig.update_yaxes(showgrid=True, gridwidth=1, gridcolor='lightgray')

            # Add border
            fig.update_xaxes(showline=True, linewidth=2, linecolor='black', mirror=True)
            fig.update_yaxes(showline=True, linewidth=2, linecolor='black', mirror=True)

            # Save and display the plot
            fig.write_html(self.plot_file.name)

            if self.settings['use_plot_window']:
                if not (self.plot_window.window and self.plot_window.window.winfo_exists()):
                    self.plot_window.show()
                self.plot_window.update_plot(self.plot_file.name)

            if self.settings['open_in_browser']:
                import webbrowser
                webbrowser.open('file://' + self.plot_file.name, new=0)

            self.status_label.config(text=f"✅ Cross section generated: {section_type} slice {slice_idx}", foreground="green")
            self.log_message(f"✅ Cross section plot generated successfully", "SUCCESS")

        except Exception as e:
            self.status_label.config(text=f"❌ Error generating cross section: {str(e)}", foreground="red")
            self.log_message(f"❌ Error generating cross section: {str(e)}", "ERROR")

    def _generate_multi_slice_cross_section(self):
        """Generate multi-slice cross section with different colors"""
        try:
            section_type = self.cross_section_type_var.get()

            # Get offset coordinate grids
            kx_grid_offset, ky_grid_offset, energy_grid_offset = self.get_offset_coordinate_grids()
            if kx_grid_offset is None:
                self.status_label.config(text="❌ No coordinate grids available", foreground="red")
                return

            # Get multi-slice parameters
            num_slices = int(self.num_slices_var.get())
            range_min = self.range_min_var.get()
            range_max = self.range_max_var.get()

            # Determine which axis we're slicing over and create slice indices based on offset coordinates
            if section_type == "energy":
                # Multiple energy slices: varying energy, fixed kx vs ky plots
                slice_axis = "energy"
                slice_grid = energy_grid_offset
                slice_indices = np.linspace(0, len(energy_grid_offset)-1, num_slices, dtype=int)
                # Ensure indices don't exceed array bounds
                slice_indices = np.clip(slice_indices, 0, len(energy_grid_offset)-1)
                # Filter by range using offset coordinates
                energy_values = energy_grid_offset[slice_indices]
                valid_mask = (energy_values >= range_min) & (energy_values <= range_max)
                slice_indices = slice_indices[valid_mask]

            elif section_type == "kx":
                # Multiple kx slices: varying kx, fixed Energy vs ky plots
                slice_axis = "kx"  # We're slicing along kx axis
                slice_grid = kx_grid_offset
                slice_indices = np.linspace(0, len(kx_grid_offset)-1, num_slices, dtype=int)
                # Ensure indices don't exceed array bounds
                slice_indices = np.clip(slice_indices, 0, len(kx_grid_offset)-1)
                # Filter by range using offset coordinates
                kx_values = kx_grid_offset[slice_indices]
                valid_mask = (kx_values >= range_min) & (kx_values <= range_max)
                slice_indices = slice_indices[valid_mask]

            elif section_type == "ky":
                # Multiple ky slices: varying ky, fixed Energy vs kx plots
                slice_axis = "ky"  # We're slicing along ky axis
                slice_grid = ky_grid_offset
                slice_indices = np.linspace(0, len(ky_grid_offset)-1, num_slices, dtype=int)
                # Ensure indices don't exceed array bounds
                slice_indices = np.clip(slice_indices, 0, len(ky_grid_offset)-1)
                # Filter by range using offset coordinates
                ky_values = ky_grid_offset[slice_indices]
                valid_mask = (ky_values >= range_min) & (ky_values <= range_max)
                slice_indices = slice_indices[valid_mask]

            if len(slice_indices) == 0:
                self.status_label.config(text="❌ No slices in specified range", foreground="red")
                self.log_message("❌ No slices found in the specified range", "ERROR")
                return

            self.log_message(f"🔍 Generating {len(slice_indices)} multi-slice {section_type} cross sections", "INFO")

            # Create color palette
            import plotly.colors as pc
            all_colors = (pc.qualitative.Set1 + pc.qualitative.Set2 + pc.qualitative.Set3 +
                         pc.qualitative.Dark2 + pc.qualitative.Pastel1)
            colors = [all_colors[i % len(all_colors)] for i in range(len(slice_indices))]

            # Create the plot
            import plotly.graph_objects as go
            fig = go.Figure()

            # Generate each cross section
            for i, slice_idx in enumerate(slice_indices):
                if section_type == "energy":
                    # Energy slice: kx vs ky at fixed energy
                    cross_section = self.binary_surface_data[slice_idx, :, :]
                    x_grid, y_grid = kx_grid_offset, ky_grid_offset
                    x_label, y_label = "k<sub>x</sub> (Å⁻¹)", "k<sub>y</sub> (Å⁻¹)"
                    slice_value = energy_grid_offset[slice_idx]
                    slice_label = f"E = {slice_value:.3f} eV"
                    title = f"Multi-Energy Cross Sections (k<sub>x</sub> vs k<sub>y</sub>)"

                elif section_type == "kx":
                    # kx slice: Energy vs ky at fixed kx
                    cross_section = self.binary_surface_data[:, :, slice_idx]  # Fixed kx index
                    x_grid, y_grid = ky_grid_offset, energy_grid_offset
                    x_label, y_label = "k<sub>y</sub> (Å⁻¹)", "E - E<sub>F</sub> (eV)"
                    slice_value = kx_grid_offset[slice_idx]
                    slice_label = f"k<sub>x</sub> = {slice_value:.3f} Å⁻¹"
                    title = f"Multi-k<sub>x</sub> Cross Sections (Energy vs k<sub>y</sub>)"

                elif section_type == "ky":
                    # ky slice: Energy vs kx at fixed ky
                    cross_section = self.binary_surface_data[:, slice_idx, :]  # Fixed ky index
                    x_grid, y_grid = kx_grid_offset, energy_grid_offset
                    x_label, y_label = "k<sub>x</sub> (Å⁻¹)", "E - E<sub>F</sub> (eV)"
                    slice_value = ky_grid_offset[slice_idx]
                    slice_label = f"k<sub>y</sub> = {slice_value:.3f} Å⁻¹"
                    title = f"Multi-k<sub>y</sub> Cross Sections (Energy vs k<sub>x</sub>)"

                # Add offset information to title if offsets are non-zero (only for first slice to avoid repetition)
                kx_offset = self.cross_kx_offset_var.get()
                ky_offset = self.cross_ky_offset_var.get()
                energy_offset = self.cross_energy_offset_var.get()
                if i == 0 and (kx_offset != 0.0 or ky_offset != 0.0 or energy_offset != 0.0):
                    title += f" (Offsets: kx={kx_offset:.2f}, ky={ky_offset:.2f}, E={energy_offset:.2f})"

                # Make zero intensity regions transparent
                cross_section_masked = np.where(cross_section > 0.5, cross_section, np.nan)

                # Only add trace if there are non-zero values
                if np.any(~np.isnan(cross_section_masked)):
                    # Check if contour mode is enabled
                    if self.contour_mode_var.get():
                        # Create contour plot for smooth connected shapes
                        self._add_contour_trace(fig, cross_section_masked, x_grid, y_grid,
                                              colors[i], slice_label, x_label, y_label, i)
                    else:
                        # Create scatter plot for this slice (better for multislice visualization)
                        # Find non-zero points
                        y_indices, x_indices = np.where(~np.isnan(cross_section_masked))
                        if len(x_indices) > 0:
                            x_coords = x_grid[x_indices]
                            y_coords = y_grid[y_indices]
                            intensities = cross_section_masked[y_indices, x_indices]

                            # Check if X-Axis Coloring is enabled
                            if self.use_x_axis_coloring_var.get():
                                # Shift cross-sections to different x-axis positions (columns)
                                x_range = x_coords.max() - x_coords.min() if len(x_coords) > 1 else 1.0
                                x_spacing = x_range * 1.2  # 20% spacing between cross-sections
                                x_coords_shifted = x_coords + (i * x_spacing)

                                # Color based on shifted x-axis position
                                marker_color = x_coords_shifted
                                colorscale = 'Viridis'
                                showscale = (i == 0 and self.show_colorbar_var.get())  # Only show colorbar for first trace
                            else:
                                # Default behavior: no shifting, use slice-based colors
                                x_coords_shifted = x_coords
                                marker_color = colors[i]
                                colorscale = None
                                showscale = False

                            fig.add_trace(go.Scatter(
                                x=x_coords_shifted,
                                y=y_coords,
                                mode='markers',
                                marker=dict(
                                    color=marker_color,
                                    colorscale=colorscale,
                                    size=4,
                                    opacity=0.8,
                                    symbol='circle',
                                    showscale=showscale,
                                    colorbar=dict(title=x_label) if showscale else None
                                ),
                                name=slice_label,
                                hovertemplate=f'{slice_label}<br>{x_label}: %{{x:.3f}}<br>{y_label}: %{{y:.3f}}<extra></extra>'
                            ))
                            self.log_message(f"   Added slice: {slice_label} ({len(x_coords)} points)", "DEBUG")
                        else:
                            self.log_message(f"   Skipped slice: {slice_label} (no valid points)", "DEBUG")
                else:
                    self.log_message(f"   Skipped slice: {slice_label} (no data points)", "DEBUG")

            # Configure layout based on contour mode and x-axis coloring
            if self.contour_mode_var.get():
                # In contour mode, hide legend since contours are visually distinct
                fig.update_layout(
                    title=title,
                    xaxis_title=x_label,
                    yaxis_title=y_label,
                    font=dict(family="Times New Roman", size=19),
                    showlegend=False,
                    plot_bgcolor='white',
                    paper_bgcolor='white',
                    width=1000,
                    height=700
                )
            elif self.use_x_axis_coloring_var.get():
                # In x-axis coloring mode, hide legend since color represents position
                fig.update_layout(
                    title=title + " (X-Axis Coloring Mode)",
                    xaxis_title=x_label,
                    yaxis_title=y_label,
                    font=dict(family="Times New Roman", size=19),
                    showlegend=False,  # Hide legend since color represents x-position
                    plot_bgcolor='white',
                    paper_bgcolor='white',
                    width=1000,
                    height=700
                )
            else:
                # In scatter mode, show legend for slice identification
                fig.update_layout(
                    title=title,
                    xaxis_title=x_label,
                    yaxis_title=y_label,
                    font=dict(family="Times New Roman", size=19),
                    showlegend=True,
                    legend=dict(
                        x=1.02,
                        y=1,
                        bgcolor='rgba(255,255,255,0.9)',
                        bordercolor='black',
                        borderwidth=1,
                        font=dict(size=17)
                    ),
                    plot_bgcolor='white',
                    paper_bgcolor='white',
                    width=1000,  # Make plot wider to accommodate legend
                    height=700
                )

            # Add grid if requested
            if self.show_grid_var.get():
                fig.update_xaxes(showgrid=True, gridwidth=1, gridcolor='lightgray')
                fig.update_yaxes(showgrid=True, gridwidth=1, gridcolor='lightgray')

            # Add border
            fig.update_xaxes(showline=True, linewidth=2, linecolor='black', mirror=True)
            fig.update_yaxes(showline=True, linewidth=2, linecolor='black', mirror=True)

            # Save and display the plot
            fig.write_html(self.plot_file.name)

            if self.settings['use_plot_window']:
                if not (self.plot_window.window and self.plot_window.window.winfo_exists()):
                    self.plot_window.show()
                self.plot_window.update_plot(self.plot_file.name)

            if self.settings['open_in_browser']:
                import webbrowser
                webbrowser.open('file://' + self.plot_file.name, new=0)

            self.status_label.config(text=f"✅ Multi-slice cross sections generated: {len(slice_indices)} {section_type} slices", foreground="green")
            self.log_message(f"✅ Multi-slice cross section plot generated successfully with {len(slice_indices)} slices", "SUCCESS")

        except Exception as e:
            self.status_label.config(text=f"❌ Error generating multi-slice cross section: {str(e)}", foreground="red")
            self.log_message(f"❌ Error generating multi-slice cross section: {str(e)}", "ERROR")

    def _generate_progressive_heatmap_cross_section(self):
        """Generate progressive heatmap cross section with continuous coloring"""
        try:
            section_type = self.cross_section_type_var.get()

            # Get offset coordinate grids
            kx_grid_offset, ky_grid_offset, energy_grid_offset = self.get_offset_coordinate_grids()
            if kx_grid_offset is None:
                self.status_label.config(text="❌ No coordinate grids available", foreground="red")
                return

            # Get multi-slice parameters
            num_slices = int(self.num_slices_var.get())
            range_min = self.range_min_var.get()
            range_max = self.range_max_var.get()

            # Try to use more slices for smoother heatmap (up to 50 for continuous effect)
            max_continuous_slices = 50
            if num_slices < max_continuous_slices:
                # Use more slices for smoother appearance
                continuous_slices = max_continuous_slices
                self.log_message(f"🌈 Using {continuous_slices} slices for continuous heatmap effect", "INFO")
            else:
                continuous_slices = num_slices

            # Initialize variables
            slice_indices = None
            slice_values = None
            value_label = ""

            # Determine which axis we're slicing over and create slice indices
            if section_type == "energy":
                # Multiple energy slices: varying energy, fixed kx vs ky plots
                slice_axis = "energy"
                slice_grid = energy_grid_offset
                slice_indices = np.linspace(0, len(energy_grid_offset)-1, continuous_slices, dtype=int)
                slice_indices = np.clip(slice_indices, 0, len(energy_grid_offset)-1)
                # Filter by range using offset coordinates
                energy_values = energy_grid_offset[slice_indices]
                valid_mask = (energy_values >= range_min) & (energy_values <= range_max)
                slice_indices = slice_indices[valid_mask]
                slice_values = energy_values[valid_mask]
                value_label = "Energy (eV)"

            elif section_type == "kx":
                # Multiple kx slices: varying kx, fixed Energy vs ky plots
                slice_axis = "kx"
                slice_grid = kx_grid_offset
                slice_indices = np.linspace(0, len(kx_grid_offset)-1, continuous_slices, dtype=int)
                slice_indices = np.clip(slice_indices, 0, len(kx_grid_offset)-1)
                # Filter by range using offset coordinates
                kx_values = kx_grid_offset[slice_indices]
                valid_mask = (kx_values >= range_min) & (kx_values <= range_max)
                slice_indices = slice_indices[valid_mask]
                slice_values = kx_values[valid_mask]
                value_label = "k<sub>x</sub> (Å⁻¹)"  # Colorbar represents the kx values we're slicing over

            elif section_type == "ky":
                # Multiple ky slices: varying ky, fixed Energy vs kx plots
                slice_axis = "ky"
                slice_grid = ky_grid_offset
                slice_indices = np.linspace(0, len(ky_grid_offset)-1, continuous_slices, dtype=int)
                slice_indices = np.clip(slice_indices, 0, len(ky_grid_offset)-1)
                # Filter by range using offset coordinates
                ky_values = ky_grid_offset[slice_indices]
                valid_mask = (ky_values >= range_min) & (ky_values <= range_max)
                slice_indices = slice_indices[valid_mask]
                slice_values = ky_values[valid_mask]
                value_label = "k<sub>y</sub> (Å⁻¹)"  # Colorbar represents the ky values we're slicing over

            if slice_indices is None or len(slice_indices) == 0:
                self.status_label.config(text="❌ No slices in specified range", foreground="red")
                self.log_message("❌ No slices found in the specified range", "ERROR")
                return

            self.log_message(f"🌈 Generating progressive heatmap with {len(slice_indices)} {section_type} cross sections", "INFO")

            # Normalize slice values for colormap (0 to 1)
            if slice_values is not None and len(slice_values) > 1:
                normalized_values = (slice_values - slice_values.min()) / (slice_values.max() - slice_values.min())
            else:
                normalized_values = np.array([0.5])  # Single slice gets middle color

            # Use the custom rainbow colorscale
            from plotly.colors import sample_colorscale
            rainbow_colors = []
            for norm_val in normalized_values:
                # Sample the custom rainbow colorscale
                color_rgb = sample_colorscale(rainbowlightct, [norm_val])[0]
                rainbow_colors.append(color_rgb)

            # Create the plot
            import plotly.graph_objects as go
            fig = go.Figure()

            # Generate each cross section
            for i, slice_idx in enumerate(slice_indices):
                if section_type == "energy":
                    # Energy slice: kx vs ky at fixed energy
                    cross_section = self.binary_surface_data[slice_idx, :, :]
                    x_grid, y_grid = kx_grid_offset, ky_grid_offset
                    x_label, y_label = "k<sub>x</sub> (Å⁻¹)", "k<sub>y</sub> (Å⁻¹)"
                    slice_value = energy_grid_offset[slice_idx]
                    slice_label = f"E = {slice_value:.3f} eV"
                    title = f"Progressive Heatmap: Energy Cross Sections (k<sub>x</sub> vs k<sub>y</sub>)"

                elif section_type == "kx":
                    # kx slice: Energy vs ky at fixed kx
                    cross_section = self.binary_surface_data[:, :, slice_idx]  # Fixed kx index
                    x_grid, y_grid = ky_grid_offset, energy_grid_offset
                    x_label, y_label = "k<sub>y</sub> (Å⁻¹)", "E - E<sub>F</sub> (eV)"
                    slice_value = kx_grid_offset[slice_idx]  # The kx value we're slicing at
                    slice_label = f"k<sub>x</sub> = {slice_value:.3f} Å⁻¹"
                    title = f"Progressive Heatmap: k<sub>x</sub> Cross Sections (Energy vs k<sub>y</sub>)"

                elif section_type == "ky":
                    # ky slice: Energy vs kx at fixed ky
                    cross_section = self.binary_surface_data[:, slice_idx, :]  # Fixed ky index
                    x_grid, y_grid = kx_grid_offset, energy_grid_offset
                    x_label, y_label = "k<sub>x</sub> (Å⁻¹)", "E - E<sub>F</sub> (eV)"
                    slice_value = ky_grid_offset[slice_idx]  # The ky value we're slicing at
                    slice_label = f"k<sub>y</sub> = {slice_value:.3f} Å⁻¹"
                    title = f"Progressive Heatmap: k<sub>y</sub> Cross Sections (Energy vs k<sub>x</sub>)"

                # Add offset information to title (only for first slice to avoid repetition)
                kx_offset = self.cross_kx_offset_var.get()
                ky_offset = self.cross_ky_offset_var.get()
                energy_offset = self.cross_energy_offset_var.get()
                if i == 0 and (kx_offset != 0.0 or ky_offset != 0.0 or energy_offset != 0.0):
                    title += f" (Offsets: kx={kx_offset:.2f}, ky={ky_offset:.2f}, E={energy_offset:.2f})"

                # Make zero intensity regions transparent
                cross_section_masked = np.where(cross_section > 0.5, cross_section, np.nan)

                # Only add trace if there are non-zero values
                if np.any(~np.isnan(cross_section_masked)):
                    # Check if contour mode is enabled
                    if self.contour_mode_var.get():
                        # Create contour plot for smooth connected shapes
                        self._add_contour_trace(fig, cross_section_masked, x_grid, y_grid,
                                              rainbow_colors[i], slice_label, x_label, y_label, i)
                    else:
                        # Original scatter plot mode
                        # Find non-zero points
                        y_indices, x_indices = np.where(~np.isnan(cross_section_masked))
                        if len(x_indices) > 0:
                            x_coords = x_grid[x_indices]
                            y_coords = y_grid[y_indices]
                            intensities = cross_section_masked[y_indices, x_indices]

                            # Check if X-Axis Coloring is enabled
                            if self.use_x_axis_coloring_var.get():
                                # Shift cross-sections to different x-axis positions (columns)
                                x_range = x_coords.max() - x_coords.min() if len(x_coords) > 1 else 1.0
                                x_spacing = x_range * 1.2  # 20% spacing between cross-sections
                                x_coords_shifted = x_coords + (i * x_spacing)

                                # Color based on shifted x-axis position
                                marker_color = x_coords_shifted
                                colorscale = 'Viridis'
                                showscale = (i == 0 and self.show_colorbar_var.get())  # Only show colorbar for first trace
                            else:
                                # Default progressive heatmap behavior: no shifting, use rainbow colors
                                x_coords_shifted = x_coords
                                marker_color = rainbow_colors[i]
                                colorscale = None
                                showscale = False

                            fig.add_trace(go.Scatter(
                                x=x_coords_shifted,
                                y=y_coords,
                                mode='markers',
                                marker=dict(
                                    color=marker_color,
                                    colorscale=colorscale,
                                    size=4,
                                    opacity=0.8,
                                    symbol='circle',
                                    showscale=showscale,
                                    colorbar=dict(title=x_label) if showscale else None
                                ),
                                name=slice_label,
                                showlegend=False,  # Hide individual slice legends in heatmap mode
                                hovertemplate=f'{slice_label}<br>{x_label}: %{{x:.3f}}<br>{y_label}: %{{y:.3f}}<extra></extra>'
                            ))
                            self.log_message(f"   Added heatmap slice: {slice_label} ({len(x_coords)} points)", "DEBUG")
                        else:
                            self.log_message(f"   Skipped slice: {slice_label} (no valid points)", "DEBUG")
                else:
                    self.log_message(f"   Skipped slice: {slice_label} (no data points)", "DEBUG")

            # Add a colorbar to show the progression (only if not using x-axis coloring)
            # Create a dummy trace for the colorbar
            if not self.use_x_axis_coloring_var.get() and slice_values is not None and len(slice_values) > 1:
                fig.add_trace(go.Scatter(
                    x=[None], y=[None],
                    mode='markers',
                    marker=dict(
                        colorscale=rainbowlightct,
                        cmin=slice_values.min(),
                        cmax=slice_values.max(),
                        colorbar=dict(
                            title=dict(
                                text=value_label,
                                font=dict(size=17, family="Times New Roman")
                            ),
                            tickfont=dict(size=15, family="Times New Roman"),
                            thickness=25,
                            len=0.8,
                            x=1.02,  # Position to the right
                            xanchor="left",
                            borderwidth=2,
                            bordercolor="black"
                        ),
                        showscale=True
                    ),
                    showlegend=False,
                    hoverinfo='skip'
                ))

            # Update layout based on x-axis coloring mode
            if self.use_x_axis_coloring_var.get():
                layout_title = title + " (X-Axis Coloring Mode)"
            else:
                layout_title = title

            fig.update_layout(
                title=layout_title,
                xaxis_title=x_label,
                yaxis_title=y_label,
                font=dict(family="Times New Roman", size=19),
                showlegend=False,  # Hide legend in progressive heatmap mode
                plot_bgcolor='white',
                paper_bgcolor='white',
                width=1000,  # Keep width for colorbar
                height=700
            )

            # Add grid if requested
            if self.show_grid_var.get():
                fig.update_xaxes(showgrid=True, gridwidth=1, gridcolor='lightgray')
                fig.update_yaxes(showgrid=True, gridwidth=1, gridcolor='lightgray')

            # Add border
            fig.update_xaxes(showline=True, linewidth=2, linecolor='black', mirror=True)
            fig.update_yaxes(showline=True, linewidth=2, linecolor='black', mirror=True)

            # Save and display the plot
            fig.write_html(self.plot_file.name)

            if self.settings['use_plot_window']:
                if not (self.plot_window.window and self.plot_window.window.winfo_exists()):
                    self.plot_window.show()
                self.plot_window.update_plot(self.plot_file.name)

            if self.settings['open_in_browser']:
                import webbrowser
                webbrowser.open('file://' + self.plot_file.name, new=0)

            self.status_label.config(text=f"✅ Progressive heatmap generated: {len(slice_indices)} {section_type} slices", foreground="green")
            self.log_message(f"✅ Progressive heatmap cross section plot generated successfully with {len(slice_indices)} slices", "SUCCESS")

        except Exception as e:
            self.status_label.config(text=f"❌ Error generating progressive heatmap: {str(e)}", foreground="red")
            self.log_message(f"❌ Error generating progressive heatmap: {str(e)}", "ERROR")

    def _add_contour_trace(self, fig, cross_section_masked, x_grid, y_grid, color, slice_label, x_label, y_label, slice_index):
        """Add a contour trace for smooth connected shapes"""
        try:
            import plotly.graph_objects as go
            from scipy.interpolate import griddata
            from scipy.ndimage import gaussian_filter

            # Find non-zero points
            y_indices, x_indices = np.where(~np.isnan(cross_section_masked))

            if len(x_indices) < 10:  # Need sufficient points for meaningful contours
                self.log_message(f"   Skipped contour for {slice_label}: insufficient points ({len(x_indices)})", "DEBUG")
                return

            x_coords = x_grid[x_indices]
            y_coords = y_grid[y_indices]
            intensities = cross_section_masked[y_indices, x_indices]

            # Create a regular grid for interpolation
            x_min, x_max = x_coords.min(), x_coords.max()
            y_min, y_max = y_coords.min(), y_coords.max()

            # Add some padding to avoid edge effects
            x_range = x_max - x_min
            y_range = y_max - y_min
            x_padding = x_range * 0.1
            y_padding = y_range * 0.1

            x_min -= x_padding
            x_max += x_padding
            y_min -= y_padding
            y_max += y_padding

            # Create grid with higher resolution for smoother contours
            grid_resolution = 100
            xi = np.linspace(x_min, x_max, grid_resolution)
            yi = np.linspace(y_min, y_max, grid_resolution)
            xi_grid, yi_grid = np.meshgrid(xi, yi)

            # Interpolate the intensity values onto the regular grid
            try:
                # Use nearest neighbor first to avoid extrapolation issues
                zi = griddata((x_coords, y_coords), intensities, (xi_grid, yi_grid), method='nearest', fill_value=0)

                # Apply Gaussian smoothing for better contours
                zi_smooth = gaussian_filter(zi, sigma=1.0)

                # Create contour levels - use a threshold for binary data
                contour_level = 0.5  # Threshold for binary surface

                # Extract RGB values from the color string
                if color.startswith('rgb(') and color.endswith(')'):
                    rgb_values = color[4:-1].split(',')
                    r, g, b = [int(val.strip()) for val in rgb_values]
                    hex_color = f'#{r:02x}{g:02x}{b:02x}'
                else:
                    hex_color = color

                # Add filled contour trace for better visibility
                fig.add_trace(go.Contour(
                    x=xi,
                    y=yi,
                    z=zi_smooth,
                    contours=dict(
                        start=contour_level,
                        end=contour_level + 0.1,
                        size=0.05,
                        coloring='fill',
                        showlabels=False
                    ),
                    colorscale=[[0, 'rgba(0,0,0,0)'], [1, color.replace('rgb', 'rgba').replace(')', ',0.6)')]],
                    showscale=False,
                    name=slice_label,
                    showlegend=False,
                    hovertemplate=f'{slice_label}<br>{x_label}: %{{x:.3f}}<br>{y_label}: %{{y:.3f}}<extra></extra>'
                ))

                # Add contour lines for definition
                fig.add_trace(go.Contour(
                    x=xi,
                    y=yi,
                    z=zi_smooth,
                    contours=dict(
                        start=contour_level,
                        end=contour_level,
                        size=0.1,
                        coloring='lines',
                        showlabels=False
                    ),
                    line=dict(
                        color=hex_color,
                        width=2
                    ),
                    showscale=False,
                    name=slice_label + "_line",
                    showlegend=False,
                    hoverinfo='skip'
                ))

                self.log_message(f"   Added contour for {slice_label} ({len(x_coords)} points)", "DEBUG")

            except Exception as interp_error:
                self.log_message(f"   Interpolation failed for {slice_label}: {interp_error}", "DEBUG")
                # Fall back to scatter plot
                fig.add_trace(go.Scatter(
                    x=x_coords,
                    y=y_coords,
                    mode='markers',
                    marker=dict(
                        color=color,
                        size=3,
                        opacity=0.8,
                        symbol='circle'
                    ),
                    name=slice_label,
                    showlegend=False,
                    hovertemplate=f'{slice_label}<br>{x_label}: %{{x:.3f}}<br>{y_label}: %{{y:.3f}}<extra></extra>'
                ))

        except Exception as e:
            self.log_message(f"   Error creating contour for {slice_label}: {e}", "ERROR")
            # Fall back to scatter plot if contour fails
            y_indices, x_indices = np.where(~np.isnan(cross_section_masked))
            if len(x_indices) > 0:
                x_coords = x_grid[x_indices]
                y_coords = y_grid[y_indices]

                fig.add_trace(go.Scatter(
                    x=x_coords,
                    y=y_coords,
                    mode='markers',
                    marker=dict(
                        color=color,
                        size=3,
                        opacity=0.8,
                        symbol='circle'
                    ),
                    name=slice_label,
                    showlegend=False,
                    hovertemplate=f'{slice_label}<br>{x_label}: %{{x:.3f}}<br>{y_label}: %{{y:.3f}}<extra></extra>'
                ))

    def auto_play_slices(self):
        """Start auto-playing through slices"""
        if self.auto_play_active:
            return

        self.auto_play_active = True
        self.auto_play_button.config(state=tk.NORMAL)
        self.log_message("▶️ Starting auto-play of cross sections", "INFO")
        self._auto_play_next()

    def stop_auto_play(self):
        """Stop auto-playing slices"""
        self.auto_play_active = False
        self.auto_play_button.config(state=tk.DISABLED)
        if self.auto_play_timer:
            self.root.after_cancel(self.auto_play_timer)
            self.auto_play_timer = None
        self.log_message("⏹️ Auto-play stopped", "INFO")

    def _auto_play_next(self):
        """Auto-play next slice"""
        if not self.auto_play_active:
            return

        current = self.slice_var.get()
        max_val = int(self.slice_scale.cget('to'))

        if current >= max_val:
            # Reached the end, start over
            self.slice_var.set(0)
        else:
            self.slice_var.set(current + 1)

        self.update_cross_section_slice()
        self.generate_cross_section()

        # Schedule next update (1 second delay)
        if self.auto_play_active:
            self.auto_play_timer = self.root.after(1000, self._auto_play_next)

    def run(self):
        """Start the GUI application"""
        try:
            self.root.mainloop()
        finally:
            # Clean up temporary plot file
            try:
                if os.path.exists(self.plot_file.name):
                    os.unlink(self.plot_file.name)
            except:
                pass


def main():
    """Main function to run the application"""
    print("Starting Enhanced ARPES Analysis GUI...")
    print("Features: Live updates, advanced analysis, multiple plot modes")
    print("Make sure you have the required packages installed:")
    print("   pip install plotly pandas numpy scipy scikit-image")
    print("New in this version:")
    print("   - Live plot updates when parameters change")
    print("   - Edge detection and component analysis")
    print("   - Euler characteristic calculation")
    print("   - Critical point detection")
    print("   - kx vs kz plotting mode")
    print("   - Enhanced peak detection")
    print()

    try:
        app = ARPESAnalysisGUI()
        app.run()
    except Exception as e:
        print(f"Error starting application: {e}")
        messagebox.showerror("Error", f"Failed to start application:\n{e}")


if __name__ == "__main__":
    main()
