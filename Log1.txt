[18:07:44] Enhanced ARPES Analysis GUI initialized
[18:07:44] Ready to load data and perform analysis
[18:07:47] 📂 Opening file dialog to load processed 3D data
[18:07:48] 📁 Loading processed data from: processed_data3.npy
[18:07:48] [ 20%] Loading processed data...
[18:07:48] 🔄 Reading numpy array from file
[18:07:49] 📊 Loaded array shape: (17028000, 4)
[18:07:49] 📈 Created DataFrame with 17,028,000 data points
[18:07:52] 📊 Data statistics: 4,421,371 non-zero points
[18:07:52] ⚡ Energy range: -2.500 to -0.000 eV
[18:07:52] 🔄 kx range: -1.516 to 1.396 Å⁻¹
[18:07:52] 🔄 ky range: -1.098 to 0.315 Å⁻¹
[18:07:52] 🔧 Auto-adjusting 3D ranges based on processed data
[18:07:52] ⚡ Auto-set energy range: -2.62 to 0.12 eV
[18:07:52] 🔄 Auto-set kx range: -1.66 to 1.54 Å⁻¹
[18:07:52] 🔄 Auto-set ky range: -1.17 to 0.39 Å⁻¹
[18:07:52] ✅ 3D ranges auto-adjusted from processed data
[18:07:52] [100%] Data loaded successfully
[18:07:52] ✅ Processed data loaded successfully - ready for 3D visualization
[18:07:58] 🔍 Starting critical points analysis on 3D energy surface
[18:07:58] 🔍 Data availability check:
[18:07:58]    analyzer_3d.processed_data exists: True
[18:07:58]    analyzer_3d.processed_data is None: False
[18:07:58]    analyzer_3d.processed_data shape: (17028000, 1)
[18:07:58] 🔍 Computing critical points on 3D energy surface
[18:07:58] [ 10%] Loading processed data for critical point analysis...
[18:07:58] 🔍 Debug processed_data structure:
[18:07:58]    Type: <class 'pandas.core.frame.DataFrame'>
[18:07:58]    Shape: (17028000, 1)
[18:07:58]    Index names: ['binding_energy', 'kx', 'ky']
[18:07:58]    Column names: ['intensity']
[18:07:58]    After reset_index shape: (17028000, 4)
[18:07:58]    After reset_index columns: ['binding_energy', 'kx', 'ky', 'intensity']
[18:07:58] 📊 Using analyzer's processed_data: (17028000, 4)
[18:07:58] 📊 Initial data statistics:
[18:07:58]    Total data points: 17,028,000
[18:07:58]    Intensity range: 0.000000 to 1.000000
[18:07:58]    kx range: -1.516 to 1.396 Å⁻¹
[18:07:58]    ky range: -1.098 to 0.315 Å⁻¹
[18:07:58]    Energy range: -2.500 to -0.000 eV
[18:07:58] 🔍 Data filtering results:
[18:07:58]    After intensity filter: 4,421,371 (26.0%)
[18:07:58]    After range filters: 4,421,371 (26.0%)
[18:07:58] [ 30%] Building 3D intensity grid for critical point analysis...
[18:07:58] 🔧 Grid parameters for critical points:
[18:07:58]    Grid size: 200x200x200 = 8,000,000 voxels
[18:07:58]    Data bounds - kx: -1.515 to 1.395 Å⁻¹
[18:07:58]    Data bounds - ky: -1.098 to 0.315 Å⁻¹
[18:07:58]    Data bounds - energy: -2.500 to -0.000 eV
[18:07:58]    Grid range - kx: -1.515 to 1.395 Å⁻¹
[18:07:58]    Grid range - ky: -1.098 to 0.315 Å⁻¹
[18:07:58]    Grid range - energy: -2.500 to -0.000 eV
[18:07:58] 🔧 Energy grid details:
[18:07:58]    Energy min: -2.500 eV
[18:07:58]    Energy max: -0.000 eV
[18:07:58]    Energy grid first: -0.000 eV
[18:07:58]    Energy grid last: -2.500 eV
[18:07:58] Processing slice 0/200, energy=-0.000
[18:07:58]    Slice 0: energy=-0.000, range=[-0.007, 0.006], points=6701
[18:07:58]    ✅ Slice 0 interpolated: 28576 non-zero points, max=0.973731
[18:07:58]    Slice 1: energy=-0.013, range=[-0.019, -0.007], points=18010
[18:07:59]    ✅ Slice 1 interpolated: 28932 non-zero points, max=0.979794
[18:07:59]    Slice 2: energy=-0.025, range=[-0.032, -0.019], points=19286
[18:07:59]    ✅ Slice 2 interpolated: 28935 non-zero points, max=0.965374
[18:07:59]    Slice 3: energy=-0.038, range=[-0.044, -0.032], points=20369
[18:07:59]    ✅ Slice 3 interpolated: 29320 non-zero points, max=0.988805
[18:07:59]    Slice 4: energy=-0.051, range=[-0.057, -0.044], points=21483
[18:07:59]    ✅ Slice 4 interpolated: 30151 non-zero points, max=0.990138
[18:08:02] Processing slice 20/200, energy=-0.252
[18:08:04] Processing slice 40/200, energy=-0.503
[18:08:07] Processing slice 60/200, energy=-0.754
[18:08:11] Processing slice 80/200, energy=-1.005
[18:08:18] Processing slice 100/200, energy=-1.256
[18:08:25] Processing slice 120/200, energy=-1.508
[18:08:31] Processing slice 140/200, energy=-1.759
[18:08:35] Processing slice 160/200, energy=-2.010
[18:08:39] Processing slice 180/200, energy=-2.261
[18:08:45] 🔍 Intensity grid construction results:
[18:08:45]    Successful slices: 200/200
[18:08:45]    Grid intensity range: 0.000000 to 0.999889
[18:08:45]    Grid mean intensity: 0.124977
[18:08:45]    Non-zero grid points: 5,223,278/8,000,000 (65.3%)
[18:08:45] [ 60%] Applying smoothing and computing gradients...
[18:08:45] [ 70%] Creating binary surface for critical point analysis...
[18:08:45] 🔍 Smoothed intensity grid analysis:
[18:08:45]    Grid shape: (200, 200, 200)
[18:08:45]    Intensity range: 0.000000 to 0.975787
[18:08:45]    Mean intensity: 0.124977 ± 0.147597
[18:08:45]    Non-zero voxels: 6,289,036/8,000,000 (78.6%)
[18:08:45] 🔍 Binary surface analysis:
[18:08:45]    Surface threshold: 0.100000
[18:08:45]    Binary surface voxels: 4,912,253/8,000,000 (61.4%)
[18:08:45] [ 80%] Computing energy surface from binary data...
[18:08:45] 🔍 Energy surface construction:
[18:08:45]    Valid surface points: 39324/40000 (98.3%)
[18:08:45] [ 90%] Finding critical points on energy surface...
[18:08:45] 🔍 Searching for critical points with parameters:
[18:08:45]    Gradient threshold: 0.01
[18:08:45]    Min separation: 0.05 Å⁻¹
[18:08:45]    Edge buffer: 3 pixels
[18:08:45] 🔍 Found 21682 critical point candidates
[18:08:50] [ 90%] Finalizing critical points analysis...
[18:08:50] 🎯 Critical points analysis complete:
[18:08:50]    📈 Maxima: 114
[18:08:50]    📉 Minima: 122
[18:08:50]    🔄 Saddles: 497
[18:08:50]    📊 Total: 733 critical points
[18:08:50] [100%] Critical points computation complete!
[18:08:50] 🎯 CRITICAL POINTS ANALYSIS RESULTS:
[18:08:50] ============================================================
[18:08:50] 🔧 ALGORITHM PARAMETERS:
[18:08:50]    Intensity threshold: 0.0100 (data filtering)
[18:08:50]    Surface threshold: 0.100 (binary surface)
[18:08:50]    Gradient threshold: 0.010000
[18:08:50]    Min separation: 0.050 Å⁻¹
[18:08:50]    Edge buffer: 3 pixels
[18:08:50]    Grid size: 200
[18:08:50]    Smoothing sigma: 1.30
[18:08:50] 
[18:08:50] 📈 MAXIMUMS (114 found):
[18:08:50] [  0%] Error occurred

