#!/usr/bin/env python3
"""
Test script for the progressive heatmap feature in the ARPES GUI.
This script tests the key functionality without requiring the full GUI.
"""

import numpy as np
import sys
import os

# Add the current directory to the path to import the GUI module
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_progressive_heatmap_logic():
    """Test the core logic of the progressive heatmap feature"""
    print("Testing Progressive Heatmap Logic...")
    
    # Test 1: Verify colorscale sampling
    try:
        from plotly.colors import sample_colorscale
        
        # Import the rainbow colorscale from the GUI module
        from arpes_analysis_gui import rainbowlightct
        
        # Test sampling colors at different positions
        test_positions = [0.0, 0.25, 0.5, 0.75, 1.0]
        colors = []
        
        for pos in test_positions:
            color = sample_colorscale(rainbowlightct, [pos])[0]
            colors.append(color)
            print(f"Position {pos}: {color}")
        
        # Verify we get different colors
        unique_colors = set(colors)
        if len(unique_colors) > 1:
            print("✅ Colorscale sampling works - different colors generated")
        else:
            print("❌ Colorscale sampling failed - all colors are the same")
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing colorscale: {e}")
        return False
    
    # Test 2: Verify slice value normalization
    try:
        # Test different slice value ranges
        test_cases = [
            np.array([0.0, 1.0, 2.0, 3.0, 4.0]),  # Regular range
            np.array([-2.0, -1.0, 0.0, 1.0, 2.0]),  # Negative to positive
            np.array([100.0, 101.0, 102.0]),  # Large values
            np.array([0.001, 0.002, 0.003])  # Small values
        ]
        
        for i, slice_values in enumerate(test_cases):
            if len(slice_values) > 1:
                normalized = (slice_values - slice_values.min()) / (slice_values.max() - slice_values.min())
                
                # Check that normalization is correct
                if np.isclose(normalized.min(), 0.0) and np.isclose(normalized.max(), 1.0):
                    print(f"✅ Test case {i+1}: Normalization correct")
                else:
                    print(f"❌ Test case {i+1}: Normalization failed")
                    print(f"   Min: {normalized.min()}, Max: {normalized.max()}")
            else:
                print(f"✅ Test case {i+1}: Single value handled correctly")
                
    except Exception as e:
        print(f"❌ Error testing normalization: {e}")
        return False
    
    # Test 3: Verify slice index generation
    try:
        # Test continuous slice generation
        total_slices = 20
        max_continuous = 50
        
        # Case 1: fewer slices than max continuous
        if total_slices < max_continuous:
            continuous_slices = max_continuous
            print(f"✅ Continuous slices increased from {total_slices} to {continuous_slices}")
        else:
            continuous_slices = total_slices
            print(f"✅ Continuous slices kept at {continuous_slices}")
            
        # Test slice index generation
        grid_length = 100
        slice_indices = np.linspace(0, grid_length-1, continuous_slices, dtype=int)
        slice_indices = np.clip(slice_indices, 0, grid_length-1)
        
        if len(slice_indices) == continuous_slices:
            print(f"✅ Generated {len(slice_indices)} slice indices correctly")
        else:
            print(f"❌ Expected {continuous_slices} indices, got {len(slice_indices)}")
            
    except Exception as e:
        print(f"❌ Error testing slice generation: {e}")
        return False
    
    print("\n🌈 Progressive Heatmap Logic Test Complete!")
    return True

def test_feature_integration():
    """Test that the feature integrates properly with the GUI structure"""
    print("\nTesting Feature Integration...")

    try:
        # Import the main GUI class
        from arpes_analysis_gui import ARPESAnalysisGUI

        # Check that the new methods exist
        if hasattr(ARPESAnalysisGUI, '_generate_progressive_heatmap_cross_section'):
            print("✅ Progressive heatmap method exists in GUI class")
        else:
            print("❌ Progressive heatmap method not found in GUI class")
            return False

        if hasattr(ARPESAnalysisGUI, '_add_contour_trace'):
            print("✅ Contour trace method exists in GUI class")
        else:
            print("❌ Contour trace method not found in GUI class")
            return False

        # Check that the new variables exist in the setup
        # We can't easily test this without instantiating the GUI, so we'll check the source
        with open('arpes_analysis_gui.py', 'r') as f:
            content = f.read()

        if 'progressive_heatmap_var' in content:
            print("✅ Progressive heatmap variable found in source code")
        else:
            print("❌ Progressive heatmap variable not found in source code")
            return False

        if 'contour_mode_var' in content:
            print("✅ Contour mode variable found in source code")
        else:
            print("❌ Contour mode variable not found in source code")
            return False

        if 'Progressive Heatmap Coloring' in content:
            print("✅ Progressive heatmap checkbox text found in source code")
        else:
            print("❌ Progressive heatmap checkbox text not found in source code")
            return False

        if 'Contour Mode' in content:
            print("✅ Contour mode checkbox text found in source code")
        else:
            print("❌ Contour mode checkbox text not found in source code")
            return False

    except Exception as e:
        print(f"❌ Error testing integration: {e}")
        return False

    print("✅ Feature Integration Test Complete!")
    return True

def main():
    """Run all tests"""
    print("=" * 60)
    print("PROGRESSIVE HEATMAP FEATURE TEST")
    print("=" * 60)
    
    success = True
    
    # Run logic tests
    if not test_progressive_heatmap_logic():
        success = False
    
    # Run integration tests
    if not test_feature_integration():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 ALL TESTS PASSED! Progressive heatmap feature is ready.")
    else:
        print("❌ SOME TESTS FAILED! Please check the implementation.")
    print("=" * 60)
    
    return success

if __name__ == "__main__":
    main()
