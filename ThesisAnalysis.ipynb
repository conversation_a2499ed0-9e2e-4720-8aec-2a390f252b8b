{"cells": [{"cell_type": "code", "execution_count": null, "id": "31c015ee", "metadata": {}, "outputs": [], "source": ["import os\n", "import numpy as np\n", "import xarray as xr\n", "import plotly.graph_objects as go\n", "import plotly.express as px\n", "from plotly.subplots import make_subplots\n", "import plotly.colors as pcolors\n", "from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Tab, Accordion, HTML\n", "from IPython.display import display\n", "import tkinter as tk\n", "from tkinter import filedialog, messagebox\n", "from arpes.load_pxt import read_single_pxt\n", "import warnings\n", "from skimage.feature import canny\n", "import pandas as pd\n", "\n", "igor_data = np.array([\n", "    [57600, 54784, 58112],\n", "    [56561.95, 53415.66, 57121.13],\n", "    [55523.89, 52047.31, 56130.26],\n", "    [54485.84, 50678.96, 55139.39],\n", "    [53447.78, 49310.62, 54148.52],\n", "    [52409.73, 47942.27, 53157.65],\n", "    [51371.67, 46655.25, 52193.88],\n", "    [50333.62, 45428.45, 51250.2],\n", "    [49295.56, 44201.66, 50306.51],\n", "    [48257.51, 42974.87, 49362.82],\n", "    [47219.45, 41748.08, 48419.14],\n", "    [46223.56, 40563.45, 47510.59],\n", "    [45468.61, 39619.77, 46802.82],\n", "    [44713.66, 38676.08, 46095.06],\n", "    [43958.71, 37732.39, 45387.29],\n", "    [43203.77, 36788.71, 44679.53],\n", "    [42448.82, 35845.02, 43971.77],\n", "    [41693.87, 34833.07, 43195.73],\n", "    [40938.92, 33795.01, 42393.6],\n", "    [40183.97, 32756.96, 41591.46],\n", "    [39429.02, 31718.9, 40789.33],\n", "    [38674.07, 30680.85, 39987.2],\n", "    [37919.12, 29670.9, 39171.01],\n", "    [37164.17, 28727.21, 38321.7],\n", "    [36409.22, 27783.53, 37472.38],\n", "    [35654.27, 26839.84, 36623.06],\n", "    [34899.32, 25896.16, 35773.74],\n", "    [34144.38, 24952.47, 34924.42],\n", "    [33512.91, 24173.43, 34239.75],\n", "    [32899.52, 23418.48, 33579.17],\n", "    [32286.12, 22663.53, 32918.59],\n", "    [31672.72, 21908.58, 32258.01],\n", "    [31059.33, 21153.63, 31597.43],\n", "    [30467.01, 20419.77, 30957.93],\n", "    [29900.8, 19712, 30344.53],\n", "    [29334.59, 19004.23, 29731.14],\n", "    [28768.38, 18296.47, 29117.74],\n", "    [28202.16, 17588.71, 28504.35],\n", "    [27641.98, 16886.96, 27901.99],\n", "    [27358.87, 16462.31, 27807.62],\n", "    [27075.77, 16037.65, 27713.26],\n", "    [26792.66, 15612.99, 27618.89],\n", "    [26509.55, 15188.33, 27524.52],\n", "    [26226.45, 14763.67, 27430.15],\n", "    [26027.67, 14479.56, 27448.22],\n", "    [25886.12, 14290.82, 27542.59],\n", "    [25744.56, 14102.09, 27636.96],\n", "    [25603.01, 13913.35, 27731.33],\n", "    [25461.46, 13724.61, 27825.69],\n", "    [25279.75, 13503.75, 27944.16],\n", "    [24902.28, 13126.27, 28180.08],\n", "    [24524.8, 12748.8, 28416],\n", "    [24147.33, 12371.33, 28651.92],\n", "    [23769.85, 11993.85, 28887.84],\n", "    [23392.38, 11616.38, 29123.77],\n", "    [22874.35, 11168.63, 29359.69],\n", "    [22308.14, 10696.78, 29595.61],\n", "    [21741.93, 10224.94, 29831.53],\n", "    [21175.72, 9753.098, 30067.45],\n", "    [20609.51, 9281.255, 30303.37],\n", "    [19952.94, 8899.765, 30539.29],\n", "    [19103.62, 8711.027, 30775.21],\n", "    [18254.31, 8522.29, 31011.14],\n", "    [17404.99, 8333.553, 31247.06],\n", "    [16555.67, 8144.816, 31482.98],\n", "    [15706.35, 7956.079, 31718.9],\n", "    [14688.38, 7893.835, 31828.33],\n", "    [13650.32, 7846.651, 31922.7],\n", "    [12612.27, 7799.467, 32017.07],\n", "    [11574.21, 7752.282, 32111.44],\n", "    [10536.16, 7705.098, 32205.8],\n", "    [9807.31, 7922.949, 32388.52],\n", "    [9429.835, 8441.977, 32671.62],\n", "    [9052.36, 8961.004, 32954.73],\n", "    [8674.887, 9480.031, 33237.84],\n", "    [8297.412, 9999.059, 33520.94],\n", "    [7911.906, 10526.12, 33812.08],\n", "    [7345.694, 11233.88, 34283.92],\n", "    [6779.482, 11941.65, 34755.77],\n", "    [6213.271, 12649.41, 35227.61],\n", "    [5647.059, 13357.18, 35699.45],\n", "    [5080.847, 14064.94, 36171.29],\n", "    [4543.749, 14714.48, 36614.02],\n", "    [4024.722, 15327.87, 37038.68],\n", "    [3505.694, 15941.27, 37463.34],\n", "    [2986.667, 16554.67, 37888],\n", "    [2467.639, 17168.06, 38312.66],\n", "    [1984.753, 17790.49, 38764.42],\n", "    [1654.463, 18451.07, 39330.64],\n", "    [1324.173, 19111.65, 39896.85],\n", "    [993.8823, 19772.23, 40463.06],\n", "    [663.5922, 20432.82, 41029.27],\n", "    [333.302, 21093.4, 41595.48],\n", "    [256, 21464.85, 41944.85],\n", "    [256, 21747.95, 42227.95],\n", "    [256, 22031.06, 42511.06],\n", "    [256, 22314.16, 42794.16],\n", "    [256, 22597.27, 43077.27],\n", "    [239.9373, 23008.88, 43456.75],\n", "    [192.7529, 23669.46, 44022.96],\n", "    [145.5686, 24330.04, 44589.18],\n", "    [98.38432, 24990.62, 45155.39],\n", "    [51.2, 25651.2, 45721.6],\n", "    [4.015687, 26311.78, 46287.81],\n", "    [0, 26972.36, 46897.19],\n", "    [0, 27632.94, 47510.59],\n", "    [0, 28293.52, 48123.98],\n", "    [0, 28954.1, 48737.38],\n", "    [0, 29614.68, 49350.78],\n", "    [0, 30344.53, 50033.44],\n", "    [0, 31146.67, 50788.39],\n", "    [0, 31948.8, 51543.34],\n", "    [0, 32750.93, 52298.29],\n", "    [0, 33553.07, 53053.24],\n", "    [0, 34358.21, 53805.18],\n", "    [0, 35207.53, 54512.94],\n", "    [0, 36056.85, 55220.71],\n", "    [0, 36906.16, 55928.47],\n", "    [0, 37755.48, 56636.23],\n", "    [0, 38604.8, 57344],\n", "    [0, 39062.59, 57208.47],\n", "    [0, 39298.51, 56595.07],\n", "    [0, 39534.43, 55981.68],\n", "    [0, 39770.35, 55368.28],\n", "    [0, 40006.27, 54754.89],\n", "    [0, 40181.96, 54041.1],\n", "    [0, 40134.78, 52955.86],\n", "    [0, 40087.59, 51870.62],\n", "    [0, 40040.41, 50785.38],\n", "    [0, 39993.22, 49700.14],\n", "    [0, 39946.04, 48614.9],\n", "    [0, 39936, 47641.1],\n", "    [0, 39936, 46697.41],\n", "    [0, 39936, 45753.73],\n", "    [0, 39936, 44810.04],\n", "    [0, 39936, 43866.35],\n", "    [0, 39918.93, 42854.4],\n", "    [0, 39871.75, 41721.98],\n", "    [0, 39824.57, 40589.55],\n", "    [0, 39777.38, 39457.13],\n", "    [0, 39730.2, 38324.71],\n", "    [0, 39683.01, 37192.28],\n", "    [0, 39680, 36369.07],\n", "    [0, 39680, 35566.93],\n", "    [0, 39680, 34764.8],\n", "    [0, 39680, 33962.67],\n", "    [0, 39680, 33160.54],\n", "    [0, 39680, 32527.06],\n", "    [0, 39680, 32055.21],\n", "    [0, 39680, 31583.37],\n", "    [0, 39680, 31111.53],\n", "    [0, 39680, 30639.69],\n", "    [0, 39675.98, 30123.67],\n", "    [0, 39628.8, 29132.8],\n", "    [0, 39581.62, 28141.93],\n", "    [0, 39534.43, 27151.06],\n", "    [0, 39487.25, 26160.19],\n", "    [0, 39440.06, 25169.32],\n", "    [0, 39361.76, 24240.69],\n", "    [0, 39267.39, 23344.19],\n", "    [0, 39173.02, 22447.69],\n", "    [0, 39078.65, 21551.18],\n", "    [0, 38984.28, 20654.68],\n", "    [0, 38923.04, 19835.48],\n", "    [0, 38970.23, 19269.27],\n", "    [0, 39017.41, 18703.06],\n", "    [0, 39064.6, 18136.85],\n", "    [0, 39111.78, 17570.63],\n", "    [0, 39158.96, 17004.42],\n", "    [0, 39435.04, 16781.55],\n", "    [0, 39765.33, 16640],\n", "    [0, 40095.62, 16498.45],\n", "    [0, 40425.91, 16356.89],\n", "    [0, 40756.2, 16215.34],\n", "    [993.8823, 41122.64, 16073.79],\n", "    [3589.02, 41547.29, 15932.24],\n", "    [6184.157, 41971.95, 15790.68],\n", "    [8779.294, 42396.61, 15649.13],\n", "    [11374.43, 42821.27, 15507.58],\n", "    [13969.57, 43245.93, 15366.02],\n", "    [15796.71, 43715.77, 15224.47],\n", "    [17589.71, 44187.61, 15082.92],\n", "    [19382.71, 44659.45, 14941.36],\n", "    [21175.72, 45131.29, 14799.81],\n", "    [22968.72, 45603.14, 14658.26],\n", "    [24686.43, 46100.08, 14516.71],\n", "    [26337.88, 46619.11, 14375.15],\n", "    [27989.33, 47138.13, 14233.6],\n", "    [29640.79, 47657.16, 14092.05],\n", "    [31292.23, 48176.19, 13950.49],\n", "    [32933.65, 48705.25, 13798.9],\n", "    [34490.73, 49318.65, 13562.98],\n", "    [36047.81, 49932.05, 13327.06],\n", "    [37604.89, 50545.44, 13091.14],\n", "    [39161.98, 51158.84, 12855.22],\n", "    [40719.06, 51772.23, 12619.29],\n", "    [41922.76, 52225, 12415.5],\n", "    [42960.82, 52602.48, 12226.76],\n", "    [43998.87, 52979.95, 12038.02],\n", "    [45036.93, 53357.43, 11849.29],\n", "    [46074.98, 53734.9, 11660.55],\n", "    [47293.74, 54196.71, 11411.58],\n", "    [49039.56, 54904.47, 10986.92],\n", "    [50785.38, 55612.23, 10562.26],\n", "    [52531.2, 56320, 10137.6],\n", "    [54277.02, 57027.77, 9712.941],\n", "    [56022.84, 57735.53, 9288.282],\n", "    [57494.59, 58325.84, 8785.317],\n", "    [58910.12, 58892.05, 8266.29],\n", "    [60325.65, 59458.26, 7747.263],\n", "    [61741.18, 60024.47, 7228.235],\n", "    [63156.71, 60590.68, 6709.208],\n", "    [64076.3, 60470.21, 6457.224],\n", "    [64265.04, 59337.79, 6598.776],\n", "    [64453.77, 58205.36, 6740.33],\n", "    [64642.51, 57072.94, 6881.882],\n", "    [64831.25, 55940.52, 7023.435],\n", "    [65019.98, 54808.09, 7164.988],\n", "    [64746.92, 53260.05, 7398.902],\n", "    [64463.81, 51702.96, 7634.824],\n", "    [64180.71, 50145.88, 7870.745],\n", "    [63897.6, 48588.8, 8106.667],\n", "    [63614.49, 47031.72, 8342.588],\n", "    [63592.41, 45605.14, 8474.102],\n", "    [63781.14, 44283.98, 8521.286],\n", "    [63969.88, 42962.82, 8568.471],\n", "    [64158.62, 41641.66, 8615.655],\n", "    [64347.36, 40320.5, 8662.839],\n", "    [64415.62, 38993.32, 8704],\n", "    [63660.68, 37624.97, 8704],\n", "    [62905.73, 36256.63, 8704],\n", "    [62150.78, 34888.28, 8704],\n", "    [61395.83, 33519.94, 8704],\n", "    [60640.88, 32151.59, 8704],\n", "    [60283.48, 30882.63, 8704],\n", "    [60094.75, 29655.84, 8704],\n", "    [59906.01, 28429.05, 8704],\n", "    [59717.27, 27202.26, 8704],\n", "    [59528.54, 25975.47, 8704],\n", "    [59339.8, 24722.57, 8704],\n", "    [59151.06, 23401.41, 8704],\n", "    [58962.32, 22080.25, 8704],\n", "    [58773.59, 20759.09, 8704],\n", "    [58584.85, 19437.93, 8704],\n", "    [58396.11, 18116.77, 8704],\n", "    [58287.69, 17197.18, 8704],\n", "    [58193.32, 16347.86, 8704],\n", "    [58098.95, 15498.54, 8704],\n", "    [58004.58, 14649.22, 8704],\n", "    [57910.21, 13799.91, 8704],\n", "    [57795.77, 12267.92, 8704],\n", "    [57654.21, 9814.337, 8704],\n", "    [57512.66, 7360.753, 8704],\n", "    [57371.11, 4907.168, 8704],\n", "    [57229.55, 2453.584, 8704],\n", "    [57088, 0, 8704]])\n", "# Create the array from the provided data\n", "\n", "\n", "# Normalize the RGB values to the range 0-1\n", "normalized_data = igor_data / 65535.0\n", "\n", "# Create custom colorscale for Plotly\n", "def create_custom_colorscale(rgb_array):\n", "    \"\"\"Convert RGB array to Plotly colorscale format\"\"\"\n", "    n_colors = len(rgb_array)\n", "    colorscale = []\n", "    for i, rgb in enumerate(rgb_array):\n", "        position = i / (n_colors - 1)\n", "        color = f'rgb({int(rgb[0]*255)}, {int(rgb[1]*255)}, {int(rgb[2]*255)})'\n", "        colorscale.append([position, color])\n", "    return colorscale\n", "\n", "rainbowlightct = create_custom_colorscale(normalized_data)\n", "\n", "def load_data_files(folder_path):\n", "    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n", "    data_files.sort()\n", "    return [os.path.join(folder_path, f) for f in data_files]\n", "\n", "def determine_scan_type(data_files):\n", "    scan_types = []\n", "    previous_polar = None\n", "    previous_hv = None\n", "    for file_path in data_files:\n", "        data = read_single_pxt(file_path)\n", "        if 'polar' in data.attrs and 'hv' in data.attrs:\n", "            current_polar = data.attrs['polar']\n", "            current_hv = data.attrs['hv']\n", "            if previous_polar is not None and previous_hv is not None:\n", "                if current_polar != previous_polar:\n", "                    scan_types.append(('polar', current_polar))\n", "                elif current_hv != previous_hv:\n", "                    scan_types.append(('hv', current_hv))\n", "                else:\n", "                    scan_types.append(('unknown', None))\n", "            else:\n", "                scan_types.append(('unknown', None))\n", "            previous_polar = current_polar\n", "            previous_hv = current_hv\n", "        else:\n", "            scan_types.append(('unknown', None))\n", "    return scan_types\n", "\n", "def moving_average(data, window_size):\n", "    kernel = np.ones((window_size, window_size)) / (window_size * window_size)\n", "    return np.convolve(data.flatten(), kernel.flatten(), mode='same').reshape(data.shape)\n", "\n", "def load_dft_data(file_paths):\n", "    dft_data = []\n", "    for file_path in file_paths:\n", "        data = np.loadtxt(file_path)\n", "        dft_data.append(data)\n", "    return dft_data"]}, {"cell_type": "code", "execution_count": null, "id": "2bf7f0d6", "metadata": {}, "outputs": [], "source": ["# Additional configuration and warnings suppression\n", "warnings.filterwarnings(\"ignore\", category=UserWarning)\n", "if not hasattr(np, 'complex'):\n", "    np.complex = np.complex128\n", "\n", "# Duplicate igor_data definition (keeping for compatibility)\n", "igor_data = np.array([\n", "    [57600, 54784, 58112],\n", "    [56561.95, 53415.66, 57121.13],\n", "    [55523.89, 52047.31, 56130.26],\n", "    [54485.84, 50678.96, 55139.39],\n", "    [53447.78, 49310.62, 54148.52],\n", "    [52409.73, 47942.27, 53157.65],\n", "    [51371.67, 46655.25, 52193.88],\n", "    [50333.62, 45428.45, 51250.2],\n", "    [49295.56, 44201.66, 50306.51],\n", "    [48257.51, 42974.87, 49362.82],\n", "    [47219.45, 41748.08, 48419.14],\n", "    [46223.56, 40563.45, 47510.59],\n", "    [45468.61, 39619.77, 46802.82],\n", "    [44713.66, 38676.08, 46095.06],\n", "    [43958.71, 37732.39, 45387.29],\n", "    [43203.77, 36788.71, 44679.53],\n", "    [42448.82, 35845.02, 43971.77],\n", "    [41693.87, 34833.07, 43195.73],\n", "    [40938.92, 33795.01, 42393.6],\n", "    [40183.97, 32756.96, 41591.46],\n", "    [39429.02, 31718.9, 40789.33],\n", "    [38674.07, 30680.85, 39987.2],\n", "    [37919.12, 29670.9, 39171.01],\n", "    [37164.17, 28727.21, 38321.7],\n", "    [36409.22, 27783.53, 37472.38],\n", "    [35654.27, 26839.84, 36623.06],\n", "    [34899.32, 25896.16, 35773.74],\n", "    [34144.38, 24952.47, 34924.42],\n", "    [33512.91, 24173.43, 34239.75],\n", "    [32899.52, 23418.48, 33579.17],\n", "    [32286.12, 22663.53, 32918.59],\n", "    [31672.72, 21908.58, 32258.01],\n", "    [31059.33, 21153.63, 31597.43],\n", "    [30467.01, 20419.77, 30957.93],\n", "    [29900.8, 19712, 30344.53],\n", "    [29334.59, 19004.23, 29731.14],\n", "    [28768.38, 18296.47, 29117.74],\n", "    [28202.16, 17588.71, 28504.35],\n", "    [27641.98, 16886.96, 27901.99],\n", "    [27358.87, 16462.31, 27807.62],\n", "    [27075.77, 16037.65, 27713.26],\n", "    [26792.66, 15612.99, 27618.89],\n", "    [26509.55, 15188.33, 27524.52],\n", "    [26226.45, 14763.67, 27430.15],\n", "    [26027.67, 14479.56, 27448.22],\n", "    [25886.12, 14290.82, 27542.59],\n", "    [25744.56, 14102.09, 27636.96],\n", "    [25603.01, 13913.35, 27731.33],\n", "    [25461.46, 13724.61, 27825.69],\n", "    [25279.75, 13503.75, 27944.16],\n", "    [24902.28, 13126.27, 28180.08],\n", "    [24524.8, 12748.8, 28416],\n", "    [24147.33, 12371.33, 28651.92],\n", "    [23769.85, 11993.85, 28887.84],\n", "    [23392.38, 11616.38, 29123.77],\n", "    [22874.35, 11168.63, 29359.69],\n", "    [22308.14, 10696.78, 29595.61],\n", "    [21741.93, 10224.94, 29831.53],\n", "    [21175.72, 9753.098, 30067.45],\n", "    [20609.51, 9281.255, 30303.37],\n", "    [19952.94, 8899.765, 30539.29],\n", "    [19103.62, 8711.027, 30775.21],\n", "    [18254.31, 8522.29, 31011.14],\n", "    [17404.99, 8333.553, 31247.06],\n", "    [16555.67, 8144.816, 31482.98],\n", "    [15706.35, 7956.079, 31718.9],\n", "    [14688.38, 7893.835, 31828.33],\n", "    [13650.32, 7846.651, 31922.7],\n", "    [12612.27, 7799.467, 32017.07],\n", "    [11574.21, 7752.282, 32111.44],\n", "    [10536.16, 7705.098, 32205.8],\n", "    [9807.31, 7922.949, 32388.52],\n", "    [9429.835, 8441.977, 32671.62],\n", "    [9052.36, 8961.004, 32954.73],\n", "    [8674.887, 9480.031, 33237.84],\n", "    [8297.412, 9999.059, 33520.94],\n", "    [7911.906, 10526.12, 33812.08],\n", "    [7345.694, 11233.88, 34283.92],\n", "    [6779.482, 11941.65, 34755.77],\n", "    [6213.271, 12649.41, 35227.61],\n", "    [5647.059, 13357.18, 35699.45],\n", "    [5080.847, 14064.94, 36171.29],\n", "    [4543.749, 14714.48, 36614.02],\n", "    [4024.722, 15327.87, 37038.68],\n", "    [3505.694, 15941.27, 37463.34],\n", "    [2986.667, 16554.67, 37888],\n", "    [2467.639, 17168.06, 38312.66],\n", "    [1984.753, 17790.49, 38764.42],\n", "    [1654.463, 18451.07, 39330.64],\n", "    [1324.173, 19111.65, 39896.85],\n", "    [993.8823, 19772.23, 40463.06],\n", "    [663.5922, 20432.82, 41029.27],\n", "    [333.302, 21093.4, 41595.48],\n", "    [256, 21464.85, 41944.85],\n", "    [256, 21747.95, 42227.95],\n", "    [256, 22031.06, 42511.06],\n", "    [256, 22314.16, 42794.16],\n", "    [256, 22597.27, 43077.27],\n", "    [239.9373, 23008.88, 43456.75],\n", "    [192.7529, 23669.46, 44022.96],\n", "    [145.5686, 24330.04, 44589.18],\n", "    [98.38432, 24990.62, 45155.39],\n", "    [51.2, 25651.2, 45721.6],\n", "    [4.015687, 26311.78, 46287.81],\n", "    [0, 26972.36, 46897.19],\n", "    [0, 27632.94, 47510.59],\n", "    [0, 28293.52, 48123.98],\n", "    [0, 28954.1, 48737.38],\n", "    [0, 29614.68, 49350.78],\n", "    [0, 30344.53, 50033.44],\n", "    [0, 31146.67, 50788.39],\n", "    [0, 31948.8, 51543.34],\n", "    [0, 32750.93, 52298.29],\n", "    [0, 33553.07, 53053.24],\n", "    [0, 34358.21, 53805.18],\n", "    [0, 35207.53, 54512.94],\n", "    [0, 36056.85, 55220.71],\n", "    [0, 36906.16, 55928.47],\n", "    [0, 37755.48, 56636.23],\n", "    [0, 38604.8, 57344],\n", "    [0, 39062.59, 57208.47],\n", "    [0, 39298.51, 56595.07],\n", "    [0, 39534.43, 55981.68],\n", "    [0, 39770.35, 55368.28],\n", "    [0, 40006.27, 54754.89],\n", "    [0, 40181.96, 54041.1],\n", "    [0, 40134.78, 52955.86],\n", "    [0, 40087.59, 51870.62],\n", "    [0, 40040.41, 50785.38],\n", "    [0, 39993.22, 49700.14],\n", "    [0, 39946.04, 48614.9],\n", "    [0, 39936, 47641.1],\n", "    [0, 39936, 46697.41],\n", "    [0, 39936, 45753.73],\n", "    [0, 39936, 44810.04],\n", "    [0, 39936, 43866.35],\n", "    [0, 39918.93, 42854.4],\n", "    [0, 39871.75, 41721.98],\n", "    [0, 39824.57, 40589.55],\n", "    [0, 39777.38, 39457.13],\n", "    [0, 39730.2, 38324.71],\n", "    [0, 39683.01, 37192.28],\n", "    [0, 39680, 36369.07],\n", "    [0, 39680, 35566.93],\n", "    [0, 39680, 34764.8],\n", "    [0, 39680, 33962.67],\n", "    [0, 39680, 33160.54],\n", "    [0, 39680, 32527.06],\n", "    [0, 39680, 32055.21],\n", "    [0, 39680, 31583.37],\n", "    [0, 39680, 31111.53],\n", "    [0, 39680, 30639.69],\n", "    [0, 39675.98, 30123.67],\n", "    [0, 39628.8, 29132.8],\n", "    [0, 39581.62, 28141.93],\n", "    [0, 39534.43, 27151.06],\n", "    [0, 39487.25, 26160.19],\n", "    [0, 39440.06, 25169.32],\n", "    [0, 39361.76, 24240.69],\n", "    [0, 39267.39, 23344.19],\n", "    [0, 39173.02, 22447.69],\n", "    [0, 39078.65, 21551.18],\n", "    [0, 38984.28, 20654.68],\n", "    [0, 38923.04, 19835.48],\n", "    [0, 38970.23, 19269.27],\n", "    [0, 39017.41, 18703.06],\n", "    [0, 39064.6, 18136.85],\n", "    [0, 39111.78, 17570.63],\n", "    [0, 39158.96, 17004.42],\n", "    [0, 39435.04, 16781.55],\n", "    [0, 39765.33, 16640],\n", "    [0, 40095.62, 16498.45],\n", "    [0, 40425.91, 16356.89],\n", "    [0, 40756.2, 16215.34],\n", "    [993.8823, 41122.64, 16073.79],\n", "    [3589.02, 41547.29, 15932.24],\n", "    [6184.157, 41971.95, 15790.68],\n", "    [8779.294, 42396.61, 15649.13],\n", "    [11374.43, 42821.27, 15507.58],\n", "    [13969.57, 43245.93, 15366.02],\n", "    [15796.71, 43715.77, 15224.47],\n", "    [17589.71, 44187.61, 15082.92],\n", "    [19382.71, 44659.45, 14941.36],\n", "    [21175.72, 45131.29, 14799.81],\n", "    [22968.72, 45603.14, 14658.26],\n", "    [24686.43, 46100.08, 14516.71],\n", "    [26337.88, 46619.11, 14375.15],\n", "    [27989.33, 47138.13, 14233.6],\n", "    [29640.79, 47657.16, 14092.05],\n", "    [31292.23, 48176.19, 13950.49],\n", "    [32933.65, 48705.25, 13798.9],\n", "    [34490.73, 49318.65, 13562.98],\n", "    [36047.81, 49932.05, 13327.06],\n", "    [37604.89, 50545.44, 13091.14],\n", "    [39161.98, 51158.84, 12855.22],\n", "    [40719.06, 51772.23, 12619.29],\n", "    [41922.76, 52225, 12415.5],\n", "    [42960.82, 52602.48, 12226.76],\n", "    [43998.87, 52979.95, 12038.02],\n", "    [45036.93, 53357.43, 11849.29],\n", "    [46074.98, 53734.9, 11660.55],\n", "    [47293.74, 54196.71, 11411.58],\n", "    [49039.56, 54904.47, 10986.92],\n", "    [50785.38, 55612.23, 10562.26],\n", "    [52531.2, 56320, 10137.6],\n", "    [54277.02, 57027.77, 9712.941],\n", "    [56022.84, 57735.53, 9288.282],\n", "    [57494.59, 58325.84, 8785.317],\n", "    [58910.12, 58892.05, 8266.29],\n", "    [60325.65, 59458.26, 7747.263],\n", "    [61741.18, 60024.47, 7228.235],\n", "    [63156.71, 60590.68, 6709.208],\n", "    [64076.3, 60470.21, 6457.224],\n", "    [64265.04, 59337.79, 6598.776],\n", "    [64453.77, 58205.36, 6740.33],\n", "    [64642.51, 57072.94, 6881.882],\n", "    [64831.25, 55940.52, 7023.435],\n", "    [65019.98, 54808.09, 7164.988],\n", "    [64746.92, 53260.05, 7398.902],\n", "    [64463.81, 51702.96, 7634.824],\n", "    [64180.71, 50145.88, 7870.745],\n", "    [63897.6, 48588.8, 8106.667],\n", "    [63614.49, 47031.72, 8342.588],\n", "    [63592.41, 45605.14, 8474.102],\n", "    [63781.14, 44283.98, 8521.286],\n", "    [63969.88, 42962.82, 8568.471],\n", "    [64158.62, 41641.66, 8615.655],\n", "    [64347.36, 40320.5, 8662.839],\n", "    [64415.62, 38993.32, 8704],\n", "    [63660.68, 37624.97, 8704],\n", "    [62905.73, 36256.63, 8704],\n", "    [62150.78, 34888.28, 8704],\n", "    [61395.83, 33519.94, 8704],\n", "    [60640.88, 32151.59, 8704],\n", "    [60283.48, 30882.63, 8704],\n", "    [60094.75, 29655.84, 8704],\n", "    [59906.01, 28429.05, 8704],\n", "    [59717.27, 27202.26, 8704],\n", "    [59528.54, 25975.47, 8704],\n", "    [59339.8, 24722.57, 8704],\n", "    [59151.06, 23401.41, 8704],\n", "    [58962.32, 22080.25, 8704],\n", "    [58773.59, 20759.09, 8704],\n", "    [58584.85, 19437.93, 8704],\n", "    [58396.11, 18116.77, 8704],\n", "    [58287.69, 17197.18, 8704],\n", "    [58193.32, 16347.86, 8704],\n", "    [58098.95, 15498.54, 8704],\n", "    [58004.58, 14649.22, 8704],\n", "    [57910.21, 13799.91, 8704],\n", "    [57795.77, 12267.92, 8704],\n", "    [57654.21, 9814.337, 8704],\n", "    [57512.66, 7360.753, 8704],\n", "    [57371.11, 4907.168, 8704],\n", "    [57229.55, 2453.584, 8704],\n", "    [57088, 0, 8704]])\n", "# Create the array from the provided data\n", "\n", "\n", "# Normalize the RGB values to the range 0-1\n", "normalized_data = igor_data / 65535.0\n", "\n", "# Create the custom colorscale for Plotly (duplicate for compatibility)\n", "rainbowlightct = create_custom_colorscale(normalized_data)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "0ad49a3e", "metadata": {}, "outputs": [], "source": ["import os\n", "import pandas as pd\n", "import numpy as np\n", "from arpes.load_pxt import read_single_pxt\n", "import tkinter as tk\n", "from tkinter import filedialog, messagebox\n", "\n", "def load_pxt_files():\n", "    folder_path = filedialog.askdirectory(title=\"Select folder containing PXT files\")\n", "    if not folder_path:\n", "        return\n", "\n", "    pxt_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n", "    if not pxt_files:\n", "        messagebox.showinfo(\"No PXT files\", \"No PXT files found in the selected folder.\")\n", "        return\n", "\n", "    # Sort the files based on their names\n", "    pxt_files.sort()\n", "\n", "    data_arrays = []\n", "    attributes = []\n", "    for file in pxt_files:\n", "        file_path = os.path.join(folder_path, file)\n", "        try:\n", "            data = read_single_pxt(file_path)\n", "            df = pd.DataFrame(data.values, columns=data.coords['phi'].values, index=data.coords['eV'].values)\n", "            data_arrays.append(df)\n", "            attributes.append(data.attrs)\n", "        except Exception as e:\n", "            messagebox.showerror(\"Error\", f\"Failed to load {file}: {str(e)}\")\n", "\n", "    messagebox.showinfo(\"Success\", f\"Loaded {len(data_arrays)} PXT files into pandas DataFrames.\")\n", "    return data_arrays, attributes\n", "\n", "# Create the main window\n", "root = tk.Tk()\n", "root.title(\"ARPES PXT File Loader\")\n", "\n", "# Create and pack a button to trigger file loading\n", "def load_and_store_data():\n", "    global data, data_attributes\n", "    data, data_attributes = load_pxt_files()\n", "    \n", "    # Add new code to correspond data_attributes[i]['polar'] to data[i]\n", "    for i in range(len(data)):\n", "        data[i].attrs['polar'] = data_attributes[i]['polar']\n", "\n", "load_button = tk.<PERSON><PERSON>(root, text=\"Load PXT Files\", command=load_and_store_data)\n", "load_button.pack(pady=20)\n", "\n", "# Start the GUI event loop\n", "root.mainloop()\n", "\n", "\n", "work_function = 4.5 #eV\n", "E_photon = np.zeros(np.shape(data_attributes)[0])\n", "for i in range(len(E_photon)):\n", "    E_photon[i]=data_attributes[i]['hv']\n", "\n", "E_photon\n", "\n", "theta =  np.zeros(np.shape(data_attributes)[0])\n", "for i in range(len(theta)):\n", "    theta[i]=data_attributes[i]['polar']\n", "\n", "theta\n", "data_proc = data.copy()\n", "for i in range(len(data_proc)):\n", "    new_index = [data_attributes[i]['hv'] - work_function - abs(idx) for idx in data[i].index]\n", "    data_proc[i] = data_proc[i].set_index(pd.Index(new_index))\n", "data_proc"]}, {"cell_type": "code", "execution_count": null, "id": "7006fde4", "metadata": {}, "outputs": [], "source": ["# Import additional libraries for enhanced plotting\n", "from scipy.interpolate import griddata, LinearNDInterpolator\n", "import ipywidgets as widgets\n", "from scipy.ndimage import gaussian_filter, maximum_filter\n", "from skimage.measure import label, euler_number\n", "from scipy.spatial import cKDTree\n", "\n", "# Use the custom colorscale created earlier\n", "# rainbowlightct is already defined as a Plotly colorscale\n", "\n", "# Constants\n", "work_function = 4.5  # eV\n", "V0 = 10  # Inner potential in eV, adjust based on your material properties\n", "\n", "# Function to apply moving average filter\n", "def moving_average(data, kernel_size):\n", "    kernel = np.ones(kernel_size) / kernel_size\n", "    return np.convolve(data, kernel, mode='same')\n", "\n", "# Compute the binding energy range spanned by the data\n", "E_binding_values = []\n", "\n", "for i in range(len(data_proc)):\n", "    df = data_proc[i]\n", "    hv = data_attributes[i]['hv']\n", "    E_kinetic_values = df.index.values.astype(float)\n", "    # Compute binding energies for these kinetic energies\n", "    E_binding_scan = hv - work_function - E_kinetic_values\n", "    E_binding_values.extend(E_binding_scan)\n", "\n", "E_binding_array = np.array(E_binding_values)\n", "E_binding_min = E_binding_array.min()\n", "E_binding_max = E_binding_array.max()\n", "\n", "# Global variables for plot management\n", "current_fig = None\n", "\n", "# Function to find peaks in the intensity data\n", "def find_peaks_in_intensity(intensities, neighborhood_size, threshold, smoothing_sigma):\n", "    # Smooth the intensities to reduce noise\n", "    intensities_smooth = gaussian_filter(intensities, sigma=smoothing_sigma)\n", "\n", "    # Apply maximum filter to find local maxima\n", "    local_max = maximum_filter(intensities_smooth, size=neighborhood_size) == intensities_smooth\n", "\n", "    # Apply threshold to identify significant peaks\n", "    detected_peaks = (intensities_smooth > threshold) & local_max\n", "\n", "    # Get peak indices\n", "    peak_indices = np.argwhere(detected_peaks)\n", "\n", "    return peak_indices\n", "\n", "# Enhanced Plotly-based plotting function\n", "def plot_constant_energy_map(\n", "    mode, E_binding, vmin, vmax, use_contours, contour_levels,\n", "    kernel_size, x_offset, y_offset, sigma, low_threshold, high_threshold,\n", "    display_edges, display_components, scan_number, peak_threshold, neighborhood_size, smoothing_sigma,\n", "    euler_binary_threshold, display_euler_points, intensity_threshold, surface_resolution,\n", "    gradient_threshold=0.01, enable_critical_points=False, data_density_threshold=5\n", "):\n", "    \"\"\"Enhanced plotting function using Plotly for interactive visualization\"\"\"\n", "    global current_fig\n", "    \n", "    try:\n", "\n", "    if mode == 'E vs kx':\n", "        fig, ax = plt.subplots(figsize=(10, 8))\n", "        colorbar = None  # Reset colorbar\n", "\n", "        # Ensure the scan_number is within valid range\n", "        if scan_number < 0 or scan_number >= len(data_proc):\n", "            print(\"Invalid scan number selected.\")\n", "            return\n", "\n", "        # Use data from the selected scan only\n", "        df = data_proc[scan_number]\n", "        hv = data_attributes[scan_number]['hv']\n", "\n", "        # Emission angles (theta) in degrees, convert to radians\n", "        emission_angles = df.columns.values.astype(float)\n", "        theta_rad = np.deg2rad(emission_angles)\n", "\n", "        # Kinetic energy values\n", "        E_kinetic_values = df.index.values.astype(float)\n", "\n", "        # Reverse E_kinetic_values to ensure increasing order if necessary\n", "        if np.any(np.diff(E_kinetic_values) < 0):\n", "            E_kinetic_values = E_kinetic_values[::-1]\n", "            df = df.iloc[::-1]\n", "\n", "        # Create grids for E_kinetic and theta using 'xy' indexing\n", "        theta_grid, E_kinetic_grid = np.meshgrid(theta_rad, E_kinetic_values, indexing='xy')\n", "\n", "        # Compute E_binding_grid\n", "        E_binding_grid = hv - work_function - E_kinetic_grid + y_offset\n", "\n", "        # Compute kx for all combinations\n", "        kx_grid = 0.5123 * np.sqrt(E_kinetic_grid) * np.sin(theta_grid) + x_offset\n", "\n", "        # Intensities from the data\n", "        intensities = df.values\n", "\n", "        # Apply moving average filter along the energy axis\n", "        if kernel_size > 1:\n", "            intensities = np.apply_along_axis(\n", "                lambda m: moving_average(m, kernel_size),\n", "                axis=0,\n", "                arr=intensities\n", "            )\n", "\n", "        # Normalize the intensities\n", "        max_intensity = np.nanmax(intensities)\n", "        if max_intensity > 0:\n", "            intensities = intensities / max_intensity\n", "\n", "        # Mask invalid data (e.g., due to sqrt of negative energies)\n", "        valid_mask = np.isfinite(kx_grid) & np.isfinite(E_binding_grid) & np.isfinite(intensities)\n", "        intensities[~valid_mask] = np.nan\n", "\n", "        # Update the plot\n", "        pcm = ax.pcolormesh(\n", "            kx_grid, E_binding_grid, intensities,\n", "            shading='auto', cmap=rainbowlightct, vmin=vmin, vmax=vmax\n", "        )\n", "\n", "        if colorbar is None:\n", "            colorbar = fig.colorbar(pcm, ax=ax, label='Normalized Intensity')\n", "        else:\n", "            colorbar.update_normal(pcm)\n", "\n", "        ax.set_xlabel(r'$k_x$ (Å$^{-1}$)')\n", "        ax.set_ylabel('Binding Energy (eV)')\n", "        ax.set_title(f'Binding Energy vs $k_x$ Map\\nScan Number: {scan_number}')\n", "\n", "        # Ensure y-axis is inverted (binding energy increases downward)\n", "        ax.set_ylim(ax.get_ylim()[::-1])\n", "\n", "        # Prepare intensities for peak detection\n", "        intensities_filled = np.nan_to_num(intensities, nan=0.0)\n", "\n", "        # Find peaks in the intensity data\n", "        peak_indices = find_peaks_in_intensity(\n", "            intensities_filled, neighborhood_size, peak_threshold, smoothing_sigma\n", "        )\n", "\n", "        # Plot peaks\n", "        if peak_indices.size > 0:\n", "            peak_coords = np.array([(kx_grid[i, j], E_binding_grid[i, j]) for i, j in peak_indices])\n", "            ax.plot(peak_coords[:, 0], peak_coords[:, 1], 'o', color='red', label='Peaks')\n", "\n", "        # Threshold the intensities to create a binary image for Euler characteristic calculation\n", "        binary_intensity = intensities_filled >= euler_binary_threshold\n", "\n", "        # Compute the <PERSON><PERSON><PERSON> characteristic\n", "        euler_char = euler_number(binary_intensity)\n", "\n", "        # Display the Euler characteristic on the plot\n", "        #ax.text(\n", "        #    0.95, 0.95,\n", "        #    f'Euler characteristic: {euler_char}',\n", "        #    transform=ax.transAxes,\n", "        #    fontsize=12,\n", "        #    verticalalignment='top',\n", "        #    horizontalalignment='right',\n", "        #    bbox=dict(facecolor='white', alpha=0.5)\n", "        #)\n", "\n", "        # Display binary points used in Euler characteristic calculation\n", "        if display_euler_points:\n", "            # Overlay the binary image onto the plot\n", "            ax.contour(\n", "                kx_grid, E_binding_grid, binary_intensity,\n", "                levels=[0.5], colors='white', linewidths=1.0, linestyles='--', label='Euler Binary'\n", "            )\n", "\n", "        # Add legend for peaks and Euler binary contour\n", "        ax.legend(loc='lower left')\n", "\n", "        if display_edges or display_components:\n", "            # Apply Canny edge detection\n", "            edges = canny(\n", "                intensities_filled,\n", "                sigma=sigma,\n", "                low_threshold=low_threshold,\n", "                high_threshold=high_threshold\n", "            )\n", "\n", "            if display_edges:\n", "                # Overlay edges on the plot\n", "                ax.contour(\n", "                    kx_grid, E_binding_grid, edges,\n", "                    levels=[0.5], colors='cyan', linewidths=1.0\n", "                )\n", "\n", "            if display_components:\n", "                # Label connected components\n", "                labeled_array, num_features = label(edges, connectivity=2, return_num=True)\n", "\n", "                # Iterate over each detected component\n", "                for region_label in range(1, num_features + 1):\n", "                    # Create a mask for the current component\n", "                    component_mask = labeled_array == region_label\n", "\n", "                    # Extract the coordinates of the component pixels\n", "                    kx_component = kx_grid[component_mask]\n", "                    E_binding_component = E_binding_grid[component_mask]\n", "\n", "                    # Plot the component\n", "                    ax.plot(\n", "                        kx_component,\n", "                        E_binding_component,\n", "                        '.', markersize=1, color='yellow'\n", "                    )\n", "\n", "        plt.tight_layout()\n", "        plt.show()\n", "\n", "    elif mode in ['kx vs ky', 'kx vs kz']:\n", "        fig, ax = plt.subplots(figsize=(10, 8))\n", "        colorbar = None  # Reset colorbar\n", "\n", "        # Prepare lists to store kx, ky_kz, and intensity values\n", "        kx_list = []\n", "        ky_kz_list = []\n", "        intensity_list = []\n", "\n", "        # Loop over all scans\n", "        for i in range(len(data_proc)):\n", "            df = data_proc[i]\n", "            hv = data_attributes[i]['hv']\n", "\n", "            # Emission angles (theta) in degrees, convert to radians\n", "            emission_angles = df.columns.values.astype(float)\n", "            theta_rad = np.deg2rad(emission_angles)\n", "\n", "            # Compute the kinetic energy corresponding to the desired binding energy\n", "            E_kinetic = hv - work_function - E_binding\n", "\n", "            # Check if E_kinetic is within the kinetic energy range of the data\n", "            E_kinetic_values = df.index.values.astype(float)\n", "            if E_kinetic < E_kinetic_values.min() or E_kinetic > E_kinetic_values.max():\n", "                continue  # Skip if E_<PERSON> is outside the data range\n", "\n", "            # Calculate k_magnitude (wave vector magnitude) in Å⁻¹\n", "            k_magnitude = 0.5123 * np.sqrt(E_kinetic)  # Å⁻¹\n", "            polar_angle = data_attributes[i]['polar']\n", "            polar_angle_rad = np.deg2rad(polar_angle)\n", "\n", "            # Calculate kx components\n", "            kx = k_magnitude * np.sin(theta_rad) + x_offset  # Array of kx values\n", "\n", "            # Extract intensities at the specified kinetic energy\n", "            if E_kinetic in E_kinetic_values:\n", "                intensities = df.loc[E_kinetic].values\n", "            else:\n", "                # Interpolate intensities at E_kinetic for each theta\n", "                intensities = df.apply(\n", "                    lambda col: np.interp(E_kinetic, E_kinetic_values, col.values)\n", "                ).values\n", "\n", "            # Apply moving average filter to intensities\n", "            if kernel_size > 1:\n", "                intensities = moving_average(intensities, kernel_size)\n", "\n", "            # Depending on the mode, calculate ky or kz\n", "            if mode == 'kx vs ky':\n", "                # Polar angle for this scan (in degrees)\n", "                polar_angle = data_attributes[i]['polar']\n", "                polar_angle_rad = np.deg2rad(polar_angle)\n", "\n", "                # Calculate ky components\n", "                ky = k_magnitude * np.sin(polar_angle_rad) + y_offset  # Scalar ky value\n", "\n", "                # Create an array of ky values matching the length of kx\n", "                ky_kz_array = np.full_like(kx, ky)\n", "                ylabel = r'$k_y$ (Å$^{-1}$)'\n", "                title = f'Constant Energy Map at Binding Energy = {E_binding:.2f} eV\\nMode: kx vs ky'\n", "\n", "            elif mode == 'kx vs kz':\n", "                # Calculate kz components using the inner potential V0\n", "                kz = 0.5123 * np.sqrt(E_kinetic * np.cos(theta_rad)**2 + V0) + y_offset  # Å⁻¹\n", "\n", "                ky_kz_array = kz\n", "                ylabel = r'$k_z$ (Å$^{-1}$)'\n", "                title = f'Constant Energy Map at Binding Energy = {E_binding:.2f} eV\\nMode: kx vs kz'\n", "\n", "            else:\n", "                print(\"Invalid mode selected.\")\n", "                return\n", "\n", "            # Append to the lists\n", "            kx_list.extend(kx)\n", "            ky_kz_list.extend(ky_kz_array)\n", "            intensity_list.extend(intensities)\n", "\n", "        # Convert lists to numpy arrays\n", "        kx_array = np.array(kx_list)\n", "        ky_kz_array = np.array(ky_kz_list)\n", "        intensity_array = np.array(intensity_list)\n", "\n", "        # Check if there is data to plot\n", "        if kx_array.size == 0 or ky_kz_array.size == 0 or intensity_array.size == 0:\n", "            print(\"No data available for the selected binding energy.\")\n", "            return\n", "\n", "        # Create grid for interpolation\n", "        grid_resolution = 600\n", "        kx_grid = np.linspace(kx_array.min(), kx_array.max(), grid_resolution)\n", "        ky_kz_grid = np.linspace(ky_kz_array.min(), ky_kz_array.max(), grid_resolution)\n", "        kx_mesh, ky_kz_mesh = np.meshgrid(kx_grid, ky_kz_grid)\n", "\n", "        # Interpolate intensity data onto the grid\n", "        intensity_grid = griddata(\n", "            points=(kx_array, ky_kz_array),\n", "            values=intensity_array,\n", "            xi=(kx_mesh, ky_kz_mesh),\n", "            method='cubic'\n", "        )\n", "\n", "        # Handle NaN values in the interpolated data\n", "        intensity_grid = np.nan_to_num(intensity_grid)\n", "\n", "        # Normalize intensity_grid to maximum value\n", "        max_intensity = intensity_grid.max()\n", "        if max_intensity > 0:\n", "            intensity_grid /= max_intensity\n", "\n", "        # Update the plot\n", "        if use_contours:\n", "            # Use the contour_levels parameter to adjust contour density\n", "            cs = ax.contour(\n", "                kx_mesh, ky_kz_mesh, intensity_grid,\n", "                levels=contour_levels, cmap=rainbowlightct\n", "            )\n", "            if colorbar is None:\n", "                colorbar = fig.colorbar(cs, ax=ax, label='Intensity')\n", "            else:\n", "                colorbar.update_normal(cs)\n", "        else:\n", "            pcm = ax.pcolormesh(\n", "                kx_mesh, ky_kz_mesh, intensity_grid,\n", "                shading='auto', cmap=rainbowlightct, vmin=vmin, vmax=vmax\n", "            )\n", "            if colorbar is None:\n", "                colorbar = fig.colorbar(pcm, ax=ax, label='Intensity')\n", "            else:\n", "                colorbar.update_normal(pcm)\n", "\n", "        ax.set_xlabel(r'$k_x$ (Å$^{-1}$)')\n", "        ax.set_ylabel(ylabel)\n", "        ax.set_title(title)\n", "\n", "        # Prepare intensities for peak detection\n", "        intensities_filled = np.nan_to_num(intensity_grid, nan=0.0)\n", "\n", "        # Find peaks in the intensity data\n", "        peak_indices = find_peaks_in_intensity(\n", "            intensities_filled, neighborhood_size, peak_threshold, smoothing_sigma\n", "        )\n", "\n", "        # Plot peaks\n", "        if peak_indices.size > 0:\n", "            peak_coords = np.array([(kx_mesh[i, j], ky_kz_mesh[i, j]) for i, j in peak_indices])\n", "            ax.plot(peak_coords[:, 0], peak_coords[:, 1], 'o', color='red', label='Peaks')\n", "\n", "        # Threshold the intensities to create a binary image for Euler characteristic calculation\n", "        binary_intensity = intensities_filled >= euler_binary_threshold\n", "\n", "        # Compute the <PERSON><PERSON><PERSON> characteristic\n", "        euler_char = euler_number(binary_intensity)\n", "\n", "        # Display the Euler characteristic on the plot\n", "        #ax.text(\n", "         #   0.95, 0.95,\n", "          #  f'Euler characteristic: {euler_char}',\n", "           # transform=ax.transAxes,\n", "            #fontsize=12,\n", "            #verticalalignment='top',\n", "            #horizontalalignment='right',\n", "            #bbox=dict(facecolor='white', alpha=0.5)\n", "        #)\n", "\n", "        # Display binary points used in Euler characteristic calculation\n", "        if display_euler_points:\n", "            # Overlay the binary image onto the plot\n", "            ax.contour(\n", "                kx_mesh, ky_kz_mesh, binary_intensity,\n", "                levels=[0.5], colors='white', linewidths=1.0, linestyles='--', label='Euler Binary'\n", "            )\n", "\n", "        # Add legend for peaks and Euler binary contour\n", "        ax.legend(loc='lower left')\n", "\n", "        if display_edges or display_components:\n", "            # Apply Canny edge detection\n", "            edges = canny(\n", "                intensities_filled,\n", "                sigma=sigma,\n", "                low_threshold=low_threshold,\n", "                high_threshold=high_threshold\n", "            )\n", "\n", "            if display_edges:\n", "                # Overlay edges on the plot\n", "                ax.contour(\n", "                    kx_mesh, ky_kz_mesh, edges,\n", "                    levels=[0.5], colors='cyan', linewidths=1.0\n", "                )\n", "\n", "            if display_components:\n", "                # Label connected components\n", "                labeled_array, num_features = label(edges, connectivity=2, return_num=True)\n", "\n", "                # Iterate over each detected component\n", "                for region_label in range(1, num_features + 1):\n", "                    # Create a mask for the current component\n", "                    component_mask = labeled_array == region_label\n", "\n", "                    # Extract the coordinates of the component pixels\n", "                    kx_component = kx_mesh[component_mask]\n", "                    ky_kz_component = ky_kz_mesh[component_mask]\n", "\n", "                    # Plot the component\n", "                    ax.plot(\n", "                        kx_component,\n", "                        ky_kz_component,\n", "                        '.', markersize=1, color='yellow'\n", "                    )\n", "\n", "        plt.tight_layout()\n", "        plt.show()\n", "\n", "    elif mode == 'E vs ky vs kx':\n", "        # Handle 3D point cloud plot with critical point detection\n", "        fig = plt.figure(figsize=(10, 8))\n", "        ax = fig.add_subplot(111, projection='3d')\n", "        colorbar = None  # Reset colorbar\n", "\n", "        # Prepare lists to store kx, ky, E_binding, and intensity values\n", "        kx_list = []\n", "        ky_list = []\n", "        E_binding_list = []\n", "        intensity_list = []\n", "\n", "        # Loop over all scans\n", "        for i in range(len(data_proc)):\n", "            df = data_proc[i]\n", "            hv = data_attributes[i]['hv']\n", "            polar_angle = data_attributes[i]['polar']\n", "            polar_angle_rad = np.deg2rad(polar_angle)\n", "\n", "            # Emission angles (theta) in degrees, convert to radians\n", "            emission_angles = df.columns.values.astype(float)\n", "            theta_rad = np.deg2rad(emission_angles)\n", "\n", "            # Kinetic energy values\n", "            E_kinetic_values = df.index.values.astype(float)\n", "\n", "            # Compute E_binding_values\n", "            E_binding_values = hv - work_function - E_kinetic_values\n", "\n", "            # Compute k_magnitude\n", "            k_magnitude = 0.5123 * np.sqrt(E_kinetic_values)  # Shape (n_Ekinetic,)\n", "\n", "            # Compute kx_grid\n", "            kx_grid = k_magnitude[:, np.newaxis] * np.sin(theta_rad[np.newaxis, :]) + x_offset  # Shape (n_Ekinetic, n_theta)\n", "\n", "            # Compute ky_grid\n", "            ky_grid = k_magnitude[:, np.newaxis] * np.sin(polar_angle_rad) + y_offset  # Shape (n_Ekinetic, 1)\n", "            ky_grid = np.tile(ky_grid, (1, len(theta_rad)))  # Shape (n_Ekinetic, n_theta)\n", "\n", "            # Compute E_binding_grid\n", "            E_binding_grid = E_binding_values[:, np.newaxis]  # Shape (n_Ekinetic, 1)\n", "            E_binding_grid = np.tile(E_binding_grid, (1, len(theta_rad)))  # Shape (n_Ekinetic, n_theta)\n", "\n", "            # Intensities\n", "            intensities = df.values  # Shape (n_Ekinetic, n_theta)\n", "\n", "            # Apply moving average filter along energy axis\n", "            if kernel_size > 1:\n", "                intensities = np.apply_along_axis(\n", "                    lambda m: moving_average(m, kernel_size),\n", "                    axis=0,\n", "                    arr=intensities\n", "                )\n", "\n", "            # Normalize intensities\n", "            max_intensity = np.nanmax(intensities)\n", "            if max_intensity > 0:\n", "                intensities = intensities / max_intensity\n", "\n", "            # Flatten the arrays\n", "            kx_flat = kx_grid.flatten()\n", "            ky_flat = ky_grid.flatten()\n", "            E_binding_flat = E_binding_grid.flatten()\n", "            intensities_flat = intensities.flatten()\n", "\n", "            # Collect data\n", "            kx_list.append(kx_flat)\n", "            ky_list.append(ky_flat)\n", "            E_binding_list.append(E_binding_flat)\n", "            intensity_list.append(intensities_flat)\n", "\n", "        # Concatenate the lists\n", "        kx_array = np.concatenate(kx_list)\n", "        ky_array = np.concatenate(ky_list)\n", "        E_binding_array = np.concatenate(E_binding_list)\n", "        intensity_array = np.concatenate(intensity_list)\n", "\n", "        # Apply intensity threshold to remove points below threshold\n", "        intensity_mask = intensity_array >= intensity_threshold\n", "        kx_array = kx_array[intensity_mask]\n", "        ky_array = ky_array[intensity_mask]\n", "        E_binding_array = E_binding_array[intensity_mask]\n", "        intensity_array = intensity_array[intensity_mask]\n", "\n", "        # Check if there is data to plot\n", "        if kx_array.size == 0:\n", "            print(\"No data to plot with the given intensity threshold.\")\n", "            return\n", "\n", "        # Normalize intensity_array for color mapping\n", "        intensity_array_normalized = intensity_array / intensity_array.max()\n", "\n", "        # Create a KDTree for efficient neighborhood queries\n", "        from scipy.spatial import cKDTree\n", "\n", "        data_points = np.column_stack((kx_array, ky_array, E_binding_array))\n", "        tree = cKDTree(data_points)\n", "\n", "        # Set the radius for local neighborhood (based on bin size)\n", "        kx_range = kx_array.max() - kx_array.min()\n", "        ky_range = ky_array.max() - ky_array.min()\n", "        E_range = E_binding_array.max() - E_binding_array.min()\n", "\n", "        # Define the neighborhood radius as a fraction of the data ranges\n", "        radius_kx = kx_range / surface_resolution\n", "        radius_ky = ky_range / surface_resolution\n", "        radius_E = E_range / surface_resolution\n", "\n", "        # Maximum radius for neighborhood search\n", "        max_radius = max(radius_kx, radius_ky, radius_E)\n", "\n", "        # Only perform critical point detection if enabled\n", "        if enable_critical_points:\n", "            # Initialize lists to store critical points\n", "            critical_kx = []\n", "            critical_ky = []\n", "            critical_E = []\n", "\n", "            # Loop over a subset of points for efficiency\n", "            num_points = len(data_points)\n", "            max_points = 50000  # Adjust as needed\n", "            if num_points > max_points:\n", "                indices = np.random.choice(num_points, max_points, replace=False)\n", "                sample_points = data_points[indices]\n", "                sample_intensities = intensity_array_normalized[indices]\n", "            else:\n", "                sample_points = data_points\n", "                sample_intensities = intensity_array_normalized\n", "\n", "            # For each sample point, compute the gradient based on neighbors\n", "            for idx, point in enumerate(sample_points):\n", "                # Find neighbors within the radius\n", "                indices = tree.query_ball_point(point, r=max_radius)\n", "\n", "                # Ensure sufficient number of neighbors\n", "                if len(indices) < data_density_threshold:\n", "                    continue  # Skip this point due to insufficient data\n", "\n", "                # Get neighbor points and intensities\n", "                neighbor_points = data_points[indices]\n", "                neighbor_intensities = intensity_array_normalized[indices]\n", "\n", "                # Fit a local plane (first-order polynomial) to the intensities\n", "                A = np.column_stack((\n", "                    neighbor_points[:, 0] - point[0],\n", "                    neighbor_points[:, 1] - point[1],\n", "                    neighbor_points[:, 2] - point[2],\n", "                    np.ones(len(indices))\n", "                ))\n", "                b = neighbor_intensities\n", "\n", "                # Use least squares to solve for the gradient components\n", "                try:\n", "                    coeffs, residuals, rank, s = np.linalg.lstsq(A, b, rcond=None)\n", "                    grad_kx, grad_ky, grad_E = coeffs[:3]\n", "\n", "                    # Compute gradient magnitude\n", "                    grad_magnitude = np.sqrt(grad_kx**2 + grad_ky**2 + grad_E**2)\n", "\n", "                    # Check if gradient magnitude is below threshold\n", "                    if grad_magnitude < gradient_threshold:\n", "                        critical_kx.append(point[0])\n", "                        critical_ky.append(point[1])\n", "                        critical_E.append(point[2])\n", "                except np.linalg.LinAlgError:\n", "                    continue  # Skip if singular matrix encountered\n", "\n", "        # Plot the point cloud\n", "        # Optionally downsample for plotting efficiency\n", "        num_points = kx_array.size\n", "        max_points_plot = 100000  # Adjust as needed\n", "        if num_points > max_points_plot:\n", "            # Randomly sample max_points indices\n", "            indices = np.random.choice(num_points, max_points_plot, replace=False)\n", "            kx_array_plot = kx_array[indices]\n", "            ky_array_plot = ky_array[indices]\n", "            E_binding_array_plot = E_binding_array[indices]\n", "            intensity_array_normalized_plot = intensity_array_normalized[indices]\n", "        else:\n", "            kx_array_plot = kx_array\n", "            ky_array_plot = ky_array\n", "            E_binding_array_plot = E_binding_array\n", "            intensity_array_normalized_plot = intensity_array_normalized\n", "\n", "        sc = ax.scatter(\n", "            kx_array_plot, ky_array_plot, E_binding_array_plot,\n", "            c=intensity_array_normalized_plot, cmap=rainbowlightct, marker='.', s=1, vmin=0, vmax=1\n", "        )\n", "\n", "        if enable_critical_points and len(critical_kx) > 0:\n", "            # Plot critical points as larger pink points\n", "            ax.scatter(\n", "                critical_kx, critical_ky, critical_E,\n", "                color='pink', s=150, label='Critical Points'\n", "            )\n", "            ax.legend(loc='best')  # Add legend to identify critical points\n", "\n", "        # Add colorbar\n", "        mappable = plt.cm.ScalarMappable(cmap=rainbowlightct, norm=plt.Normalize(vmin=0, vmax=1))\n", "        mappable.set_array([])\n", "        if colorbar is None:\n", "            colorbar = fig.colorbar(mappable, ax=ax, label='Normalized Intensity')\n", "        else:\n", "            colorbar.update_normal(mappable)\n", "\n", "        ax.set_xlabel(r'$k_x$ (Å$^{-1}$)')\n", "        ax.set_ylabel(r'$k_y$ (Å$^{-1}$)')\n", "        ax.set_zlabel('Binding Energy (eV)')\n", "        ax.set_title('3D Scatter Plot: E vs $k_y$ vs $k_x$ with Critical Points')\n", "\n", "        plt.tight_layout()\n", "        plt.show()\n", "\n", "    else:\n", "        print(\"Invalid mode selected.\")\n", "        return\n", "\n", "# Widgets for user interaction\n", "mode_selector = widgets.Dropdown(\n", "    options=['kx vs ky', 'kx vs kz', 'E vs kx', 'E vs ky vs kx'],\n", "    value='kx vs ky',\n", "    description='Select Mode:',\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "# Create a slider for binding energy\n", "E_binding_widget = widgets.FloatSlider(\n", "    value=0.0,\n", "    min=E_binding_min,\n", "    max=E_binding_max,\n", "    step=0.01,\n", "    description='Binding Energy (eV):',\n", "    continuous_update=False,\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "# Add a toggle for enabling contour maps\n", "contour_toggle = widgets.Checkbox(\n", "    value=False,\n", "    description='Enable Contour Map',\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "# Add a slider to adjust contour density\n", "contour_density_widget = widgets.IntSlider(\n", "    value=20,\n", "    min=1,\n", "    max=100,\n", "    step=1,\n", "    description='Contour Density:',\n", "    continuous_update=False,\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "# Widget for adjusting kernel size of moving average filter\n", "kernel_size_widget = widgets.IntSlider(\n", "    value=1,\n", "    min=1,\n", "    max=51,\n", "    step=2,\n", "    description='<PERSON><PERSON> Si<PERSON>:',\n", "    continuous_update=False,\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "# Widgets for adjusting x and y axis offsets\n", "x_offset_widget = widgets.FloatSlider(\n", "    value=0.0,\n", "    min=-5.0,\n", "    max=5.0,\n", "    step=0.01,\n", "    description='X Offset:',\n", "    continuous_update=False,\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "y_offset_widget = widgets.FloatSlider(\n", "    value=0.0,\n", "    min=-5.0,\n", "    max=5.0,\n", "    step=0.01,\n", "    description='Y Offset:',\n", "    continuous_update=False,\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "# Intensity range is now between 0 and 1 after normalization\n", "vmin_widget = widgets.FloatSlider(\n", "    value=0.0,\n", "    min=0.0,\n", "    max=1.0,\n", "    step=0.01,\n", "    description='Min Intensity:',\n", "    continuous_update=False,\n", "    readout_format='.2f',\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "vmax_widget = widgets.FloatSlider(\n", "    value=1.0,\n", "    min=0.0,\n", "    max=1.0,\n", "    step=0.01,\n", "    description='Max Intensity:',\n", "    continuous_update=False,\n", "    readout_format='.2f',\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "# Intensity threshold specific to the 3D mode\n", "intensity_threshold_widget = widgets.FloatSlider(\n", "    value=0.0,\n", "    min=0.0,\n", "    max=1.0,\n", "    step=0.01,\n", "    description='Intensity Threshold:',\n", "    continuous_update=False,\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "# Checkbox to enable surface plot\n", "surface_plot_toggle = widgets.Checkbox(\n", "    value=False,\n", "    description='Enable Surface Plot',\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "# Slider for surface resolution\n", "surface_resolution_widget = widgets.IntSlider(\n", "    value=200,\n", "    min=50,\n", "    max=1000,\n", "    step=50,\n", "    description='Surface Resolution:',\n", "    continuous_update=False,\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "# Widgets for Canny edge detection parameters\n", "sigma_widget = widgets.FloatSlider(\n", "    value=1.0,\n", "    min=0.1,\n", "    max=5.0,\n", "    step=0.1,\n", "    description='Canny Sigma:',\n", "    continuous_update=False,\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "low_threshold_widget = widgets.FloatSlider(\n", "    value=0.1,\n", "    min=0.0,\n", "    max=1.0,\n", "    step=0.01,\n", "    description='Canny Low Threshold:',\n", "    continuous_update=False,\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "high_threshold_widget = widgets.FloatSlider(\n", "    value=0.3,\n", "    min=0.0,\n", "    max=1.0,\n", "    step=0.01,\n", "    description='Canny High Threshold:',\n", "    continuous_update=False,\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "# Create a slider to select the scan number\n", "scan_selector = widgets.IntSlider(\n", "    value=0,\n", "    min=0,\n", "    max=len(data_proc) - 1,\n", "    step=1,\n", "    description='Scan Number:',\n", "    continuous_update=False,\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "# Peak detection parameters\n", "peak_threshold_widget = widgets.FloatSlider(\n", "    value=0.5,\n", "    min=0.0,\n", "    max=1.0,\n", "    step=0.01,\n", "    description='Peak Threshold:',\n", "    continuous_update=False,\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "neighborhood_size_widget = widgets.IntSlider(\n", "    value=5,\n", "    min=1,\n", "    max=21,\n", "    step=2,\n", "    description='Neighborhood Size:',\n", "    continuous_update=False,\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "smoothing_sigma_widget = widgets.FloatSlider(\n", "    value=1.0,\n", "    min=0.0,\n", "    max=5.0,\n", "    step=0.1,\n", "    description='Smoothing Sigma:',\n", "    continuous_update=False,\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "# Euler characteristic calculation threshold\n", "euler_binary_threshold_widget = widgets.FloatSlider(\n", "    value=0.5,\n", "    min=0.0,\n", "    max=1.0,\n", "    step=0.01,\n", "    description='<PERSON><PERSON><PERSON>:',\n", "    continuous_update=False,\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "# Checkbox to display Euler characteristic points\n", "display_euler_points_toggle = widgets.Checkbox(\n", "    value=False,\n", "    description='Display Euler Points',\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "# Toggle to display components and edges\n", "display_edges_toggle = widgets.Checkbox(\n", "    value=False,\n", "    description='Display Edges',\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "display_components_toggle = widgets.Checkbox(\n", "    value=False,\n", "    description='Display Components',\n", "    style={'description_width': 'initial'}\n", ")\n", "# Add a widget for adjusting the gradient threshold\n", "gradient_threshold_widget = widgets.FloatSlider(\n", "    value=0.01,\n", "    min=0.0,\n", "    max=0.1,\n", "    step=0.001,\n", "    description='Grad<PERSON> Threshold:',\n", "    continuous_update=False,\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "# Checkbox to enable critical point detection\n", "enable_critical_points_toggle = widgets.Checkbox(\n", "    value=False,\n", "    description='Enable Critical Point Detection',\n", "    style={'description_width': 'initial'}\n", ")\n", "# Slider for data density threshold\n", "data_density_threshold_widget = widgets.IntSlider(\n", "    value=5,\n", "    min=1,\n", "    max=20,\n", "    step=1,\n", "    description='Data Density Threshold:',\n", "    continuous_update=False,\n", "    style={'description_width': 'initial'}\n", ")\n", "\n", "# Update the interactive_output to include the new parameter\n", "out = widgets.interactive_output(\n", "    plot_constant_energy_map,\n", "    {\n", "        'mode': mode_selector,\n", "        'E_binding': E_binding_widget,\n", "        'vmin': vmin_widget,\n", "        'vmax': vmax_widget,\n", "        'use_contours': contour_toggle,\n", "        'contour_levels': contour_density_widget,\n", "        'kernel_size': kernel_size_widget,\n", "        'x_offset': x_offset_widget,\n", "        'y_offset': y_offset_widget,\n", "        'sigma': sigma_widget,\n", "        'low_threshold': low_threshold_widget,\n", "        'high_threshold': high_threshold_widget,\n", "        'display_edges': display_edges_toggle,\n", "        'display_components': display_components_toggle,\n", "        'scan_number': scan_selector,\n", "        'peak_threshold': peak_threshold_widget,\n", "        'neighborhood_size': neighborhood_size_widget,\n", "        'smoothing_sigma': smoothing_sigma_widget,\n", "        'euler_binary_threshold': euler_binary_threshold_widget,\n", "        'display_euler_points': display_euler_points_toggle,\n", "        'intensity_threshold': intensity_threshold_widget,\n", "        'surface_resolution': surface_resolution_widget,\n", "        'gradient_threshold': gradient_threshold_widget,\n", "        'enable_critical_points': enable_critical_points_toggle,\n", "        'data_density_threshold': data_density_threshold_widget  # Include the new parameter\n", "    }\n", ")\n", "\n", "# Update the UI to include the new slider\n", "ui = widgets.VBox([\n", "    widgets.HBox([mode_selector, E_binding_widget, scan_selector]),\n", "    widgets.HBox([vmin_widget, vmax_widget, intensity_threshold_widget, kernel_size_widget]),\n", "    widgets.HBox([x_offset_widget, y_offset_widget]),\n", "    widgets.HBox([sigma_widget, low_threshold_widget, high_threshold_widget]),\n", "    widgets.HBox([peak_threshold_widget, neighborhood_size_widget, smoothing_sigma_widget]),\n", "    widgets.HBox([euler_binary_threshold_widget, display_euler_points_toggle]),\n", "    widgets.HBox([contour_toggle, contour_density_widget, surface_resolution_widget]),\n", "    widgets.HBox([gradient_threshold_widget, data_density_threshold_widget, enable_critical_points_toggle]),\n", "    widgets.HBox([display_edges_toggle, display_components_toggle]),\n", "])\n", "\n", "display(ui, out)"]}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.8.19"}}, "nbformat": 4, "nbformat_minor": 5}